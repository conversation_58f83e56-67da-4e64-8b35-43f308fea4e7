package id.co.bri.brimons.payment.feature.qrtransfer.ui

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.graphics.createBitmap
import id.co.bri.brimons.payment.R
import id.co.bri.brimons.payment.core.common.thousandSeparator
import id.co.bri.brimons.payment.core.design.theme.MainTheme
import id.co.bri.brimons.payment.core.network.response.base.AccountResponse
import id.co.bri.brimons.payment.core.network.response.base.AlertResponse
import id.co.bri.brimons.payment.core.network.response.base.PopupResponse
import id.co.bri.brimons.payment.core.network.response.qrtransfer.QrTransferGenerateResponse

@Composable
internal fun QrTransferView(
    qrTransferGenerate: QrTransferGenerateResponse,
    bitmap: Bitmap
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Image(
            painter = painterResource(R.drawable.qr_transfer_background),
            contentDescription = null,
            modifier = Modifier
                .fillMaxWidth()
                .height(552.dp),
            contentScale = ContentScale.Crop
        )

        Image(
            painter = painterResource(R.drawable.image_qr_transfer),
            contentDescription = null,
            modifier = Modifier
                .width(224.dp)
                .height(320.dp)
                .align(Alignment.TopEnd),
            contentScale = ContentScale.Crop
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopCenter)
                .padding(horizontal = 24.dp)
        ) {
            Spacer(modifier = Modifier.height(24.dp))

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.image_logo),
                    contentDescription = null,
                    modifier = Modifier
                        .width(64.dp)
                        .height(32.dp),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.weight(1f))

                Image(
                    painter = painterResource(R.drawable.image_qris),
                    contentDescription = null,
                    modifier = Modifier
                        .width(64.dp)
                        .height(32.dp),
                    contentScale = ContentScale.Fit
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            Box(
                modifier = Modifier
                    .size(220.dp)
                    .align(Alignment.CenterHorizontally),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    bitmap = bitmap.asImageBitmap(),
                    contentDescription = null,
                    modifier = Modifier
                        .size(220.dp)
                        .clip(RoundedCornerShape(16.dp)),
                    contentScale = ContentScale.Crop
                )

                Image(
                    painter = painterResource(R.drawable.qr_logo),
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    contentScale = ContentScale.Fit
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            Row(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = qrTransferGenerate.selectedAccount?.alias.orEmpty()
                        .ifEmpty { qrTransferGenerate.selectedAccount?.name.orEmpty() },
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    style = MaterialTheme.typography.bodyLarge
                )
            }

            val productType = qrTransferGenerate.selectedAccount?.productType.orEmpty()
            val accountString =
                qrTransferGenerate.selectedAccount?.maskAccountNumber.orEmpty().ifEmpty {
                    qrTransferGenerate.selectedAccount?.accountString.orEmpty()
                }

            Text(
                text = "$productType - $accountString",
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = Color.White,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(24.dp))

            val amount = qrTransferGenerate.amount.toString().thousandSeparator()

            if (amount.isNotEmpty() && amount != "0") {
                Text(
                    text = "Rp$amount",
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    style = MaterialTheme.typography.bodyLarge
                )

                Spacer(modifier = Modifier.height(24.dp))
            }

            Text(
                text = qrTransferGenerate.mpan.orEmpty(),
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "Kode QR berlaku hingga ${qrTransferGenerate.expired.orEmpty()} WIB",
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = Color.White,
                style = MaterialTheme.typography.bodySmall
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "Hanya untuk 1 kali transaksi",
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = Color.White,
                style = MaterialTheme.typography.labelSmall
            )

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

@Preview
@Composable
private fun PreviewQrTransferView() {
    MainTheme {
        QrTransferView(
            qrTransferGenerate = QrTransferGenerateResponse(
                amount = 100000,
                expired = "86400",
                qrCode = "00020101021240580013ID.CO.BRI.WWW0118936000021000007675021502300113711550752044829530336054061000005802ID5920ADIXXXXXXXXXXXXXXLTI6015KOTA ADM JAKART61051256062340804DMCT99220002000112323549795892630419C5",
                mpan = "9360000210000076756",
                title = "Tunjukkan kode QR ini ke pengirim dana.",
                titleShare = "Scan QR ini untuk melakukan transfer.",
                description = "",
                maxNominal = "10000000",
                minNominal = "10000",
                footer = "",
                popupMinTransaction = PopupResponse(
                    title = "Nominal Kurang dari Limit Transaksi",
                    imagePath = "",
                    imageName = "",
                    description = "Minimum nominal untuk menerima transfer adalah Rp10.000."
                ),
                popupMaxTransaction = PopupResponse(
                    title = "Nominal Melebihi Limit Transaksi",
                    imagePath = "",
                    imageName = "",
                    description = "Maksimum nominal untuk menerima transfer adalah Rp10.000.000."
                ),
                popupGeneralAlert = AlertResponse(
                    title = "Buat Kode QR Baru?",
                    description = "Setelah membuat kode QR baru, QR sebelumnya tidak dapat digunakan lagi. Pastikan transaksi QR ini sudah selesai, ya."
                ),
                snackbarMessage = "Nominal ditambahkan dan QR siap dibagikan.",
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "Product Type",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = ""
                    )
                ),
                selectedAccount = AccountResponse(
                    account = "***************",
                    accountString = "1234 5678 9012 345",
                    name = "Name",
                    currency = "Rp",
                    cardNumber = "****************",
                    cardNumberString = "0987 XXXX XXXX 8765",
                    productType = "Product Type",
                    accountType = "",
                    scCode = "",
                    default = 0,
                    alias = "Alias",
                    minimumBalance = "",
                    limit = "",
                    limitString = "",
                    imageName = "",
                    imagePath = "",
                    maskAccountNumber = "1234 **** **** 345"
                )
            ),
            bitmap = createBitmap(100, 100)
        )
    }
}
