<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </base-config>
</network-security-config>


<!--Cert for SSL
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">apps.ocp-new-dev.bri.co.id</domain>
        <trust-anchors>
            <certificates src="@raw/ingress_ca" />
        </trust-anchors>
        <pin-set expiration="2026-01-01">
            <pin digest="SHA-256">KrPgtSSCRiHGiRUZ7YV4wIcxCb5B6ZuOa6lYHkT18aE=</pin>
        </pin-set>
        <trustkit-config
            disableDefaultReportUri="true"
            enforcePinning="true" />
    </domain-config>
</network-security-config>
-->

