package id.co.bri.brimons.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingCameraPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingCameraView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingSendKycReq
import id.co.bri.brimons.models.apimodel.response.OcrKtpErrorResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.StatusResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingCameraPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingCameraPresenter<V> where V : IMvpView, V : IOnboardingCameraView {

    private var urlKtp: String = ""
    private var urlKycKtp: String = ""

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

    override fun setUrlCheckKtp(url: String) {
        urlKtp = url
    }

    override fun setUrlKycKtp(url: String) {
        urlKycKtp = url
    }

    override fun sendCheckKtp() {
        if (urlKtp.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlKtp, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val progressResponse =
                            response.getData(StatusResponse::class.java)

                        if (progressResponse.status == 1) {
                            getView().onFailedStatusKtp(progressResponse.status)
                        } else {
                            getView().onSuccessStatusKtp(Gson().toJson(response.data))
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code == Constant.RE_INVALID_KTP){
                            val ocrErrorResponse = restResponse.getData(OcrKtpErrorResponse::class.java)
                            getView().onExceptionInvalidKtp(ocrErrorResponse)
                        }
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun sendKycKtp(onboardingSendKycReq: OnboardingSendKycReq) {
        if (urlKycKtp.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlKycKtp, onboardingSendKycReq, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT_ONBOARDING.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessKycKtp(Gson().toJson(response.data))
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}