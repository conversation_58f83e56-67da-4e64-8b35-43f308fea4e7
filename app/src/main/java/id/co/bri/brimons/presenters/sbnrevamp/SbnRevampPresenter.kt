package id.co.bri.brimons.presenters.sbnrevamp

import id.co.bri.brimons.contract.IPresenter.sbnrevamp.ISbnRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.sbnrevamp.ISbnRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.OnboardingRDNSBNRequest
import id.co.bri.brimons.models.apimodel.request.esbn.DetailOfferSbnRequest
import id.co.bri.brimons.models.apimodel.request.esbn.RegisKemenkeuRequest
import id.co.bri.brimons.models.apimodel.request.sbnrevamp.SbnDetailPortoRequest
import id.co.bri.brimons.models.apimodel.request.sbnrevamp.SbnSimulasiRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.esbn.BeliSbnResponse
import id.co.bri.brimons.models.apimodel.response.esbn.DashboardDataSbnResponse
import id.co.bri.brimons.models.apimodel.response.esbn.EsbnExceptionResponse
import id.co.bri.brimons.models.apimodel.response.esbn.regisESBN.EsbnProductBriefResponse
import id.co.bri.brimons.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimons.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimons.models.apimodel.response.sbnrevamp.*
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class SbnRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                            compositeDisposable: CompositeDisposable,
                            mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                            categoryPfmSource: CategoryPfmSource,
                            transaksiPfmSource: TransaksiPfmSource,
                            anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        ISbnRevampPresenter<V> where V : IMvpView, V : ISbnRevampView {

    private var urlHeader = ""
    private var urlBody = ""
    private var urlBeli = ""
    private var urlRegistrasi = ""
    private var urlProductBrief = ""
    private var urlSimulasi = ""
    private var urlDetail = ""
    private var urlDetailBeli = ""
    private var urlValidateUser = ""
    private var mUrlKemenkeu = ""


    override fun setUrlSbnDashboardHeader(urlDashboardHeader: String) {
        this.urlHeader = urlDashboardHeader
    }

    override fun setUrlSbnDashboardBody(urlDashboardBody: String) {
        this.urlBody = urlDashboardBody
    }

    override fun setUrlBeliSbn(urlBeliSbn: String) {
        urlBeli = urlBeliSbn
    }

    override fun setUrlRegistrasiSbn(urlRegisSbn: String) {
        urlRegistrasi = urlRegisSbn
    }

    override fun setUrlProductsBrief(urlProductBrief: String) {
        this.urlProductBrief = urlProductBrief
    }

    override fun setUrlSimulasiSbn(url: String) {
        urlSimulasi = url
    }

    override fun setUrlSbnDetailPorto(urlDetailItemPorto: String) {
        this.urlDetail = urlDetailItemPorto
    }

    override fun setUrlGetDetail(urlGetDetail: String) {
        this.urlDetailBeli = urlGetDetail
    }

    override fun setUrlValidateUsers(urlValidate: String) {
        urlValidateUser= urlValidate
    }

    override fun setUrlKemenkeu(urlKemenkeu: String) {
        mUrlKemenkeu = urlKemenkeu
    }

    override fun getSbnDashboardHeader() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(urlHeader, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data =  response.getData(SbnDashboardHeaderResponse::class.java)

                            if (data.sbnRegistered == true) {
                                getView().onSuccessSbnDashboardHeader(data)
                            } else {
                                getView().onSuccessFirstTimeSbn(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            }else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)){
                                getView().onExceptionTrxExpired(restResponse.desc)
                            }
                            else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getSbnDashboardBody() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(urlBody, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(SbnDashboardBodyResponse::class.java)

                            getView().onSuccessSbnDashboardBody(data)
                            getSbnDashboardHeader()
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            }
                            else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)){
                                getView().onExceptionTrxExpired(restResponse.desc)
                            }
                            else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getSbnBeliSbn() {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                    apiSource.getDataTanpaRequest(urlBeli, seqNum).subscribeOn(
                            schedulerProvider.io()
                    ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .replay()
            compositeDisposable.add(
                    listConnectableObservable
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String) {
                                    getView().hideProgress()
                                    getView().onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    val data =  response.getData(SbnBeliResponse::class.java)

                                    getView().onSuccessBeliSbn(data)

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                        getView().onSessionEnd(restResponse.desc)
                                    } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                        getView().onException12(restResponse.desc)
                                    } else {
                                        getView().onException(restResponse.desc)
                                    }
                                }
                            })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getSbnRegisData() {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(urlRegistrasi, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                val data = response.getData(EsbnProductBriefResponse::class.java)
                                getView().onSuccessGetRegisSbn(data)
                            }
                            else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_S1.value, ignoreCase = true)) {
                                val data = response.getData(DashboardDataSbnResponse::class.java)
                                getView().onSuccessGetRegisRdnS1(data)
                            }
                            else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_S2.value, ignoreCase = true)) {
                                val data = response.getData(DashboardDataSbnResponse::class.java)
                                getView().onSuccessGetRegisRdnS2(data)
                            }
                            else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                val data = response.getData(EsbnExceptionResponse::class.java)
                                getView().onSuccessGetSbnException(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, ignoreCase = true))
                                getView().onException99(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                                getView().onException12(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getOnProductBrief() {
        if (isViewAttached) {
            view.showProgress()
            val onboardingRDNSBNRequest = OnboardingRDNSBNRequest(
                brImoPrefRepository.firstRdn
            )
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlProductBrief, onboardingRDNSBNRequest, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()

                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                val data = response.getData(RdnOnBoardingResponse::class.java)
                                getView().onSuccessProductRdn(data)
                            }
                            else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                val data = response.getData(RdnOnCheckpointResponse::class.java)
                                getView().onCheckPointRegisSbn(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                                getView().onException12(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataSimulasiSbn(request: SbnSimulasiRequest,idSeri : Int) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlSimulasi,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data =  response.getData(SbnSimulasiResponse::class.java)

                            getView().onSuccessSimulasiSbn(data,idSeri)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataDetailPortoSbn(request: SbnDetailPortoRequest) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlDetail,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String?) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data =  response.getData(SbnDetailPortoResponse::class.java)

                            getView().onSuccessGetDetailPorto(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDetailOffer(idSeri: Int) {
        if (isViewAttached) {
            val request = DetailOfferSbnRequest(idSeri)
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlDetailBeli,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String?) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                val data = response.getData(BeliSbnResponse::class.java)
                                getView().onSuccessGetDetail(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                                getView().onException12(restResponse.desc)
                            else
                                getView().onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getValidateUser(idSeri: Int) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                    apiSource.getDataTanpaRequest(urlValidateUser, seqNum).subscribeOn(
                            schedulerProvider.io()
                    ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .replay()
            compositeDisposable.add(
                    listConnectableObservable
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(schedulerProvider.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String?) {
                                    getView().hideProgress()
                                    getView().onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    val data = response.getData(NotFoundSbnModel::class.java)
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                        getView().onSuccessGetValidateUser(idSeri)
                                    }
                                    else if(response.code.equals("R1", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R2", ignoreCase = true)){
                                        getView().onExceptionRegisKemenkeu(data)
                                    }
                                    else if(response.code.equals("R3", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R4", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R5", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R6", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                        getView().onSessionEnd(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                                        getView().onException12(restResponse.desc)
                                    else
                                        getView().onException(restResponse.desc)
                                }
                            })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getValidateUserLihatSemua(idSeri: Int) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                    apiSource.getDataTanpaRequest(urlValidateUser, seqNum).subscribeOn(
                            schedulerProvider.io()
                    ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .replay()
            compositeDisposable.add(
                    listConnectableObservable
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(schedulerProvider.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String?) {
                                    getView().hideProgress()
                                    getView().onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    val data = response.getData(NotFoundSbnModel::class.java)
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                        getView().onSuccessGetValidateUserLihatSemua()
                                    }
                                    else if(response.code.equals("R1", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R2", ignoreCase = true)){
                                        getView().onExceptionRegisKemenkeu(data)
                                    }
                                    else if(response.code.equals("R3", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R4", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R5", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }
                                    else if(response.code.equals("R6 ", ignoreCase = true)){
                                        getView().onExceptionR5(data)
                                    }

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                        getView().onSessionEnd(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                                        getView().onException12(restResponse.desc)
                                    else
                                        getView().onException(restResponse.desc)
                                }
                            })
            )
            listConnectableObservable.connect()
        }
    }

    override fun saveBackGeneral(status: Int) {
        brImoPrefRepository.saveBackGeneral(status);
    }

    override fun getKemenkeuData(regisKemenkeuRequest: RegisKemenkeuRequest) {
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()
        compositeDisposable.add(
                apiSource.getData(mUrlKemenkeu, regisKemenkeuRequest, seqNum)
                        .subscribeOn(schedulerProvider.single())
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(type: String) {
                                getView().hideProgress()
                                getView().onException(type)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                if(response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                    val dialogData = response.getData(DashboardDataSbnResponse::class.java)
                                    getView().onSuccessKemenkeu(dialogData)
                                }
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                    val esbnExceptionResponse = response.getData(EsbnExceptionResponse::class.java)
                                    getView().onException02(esbnExceptionResponse)
                                }

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                    getView().onSessionEnd(restResponse.desc)
                                else getView().onException(restResponse.desc)
                            }
                        })
        )
    }
}