package id.co.bri.brimons.presenters.transaksisaya.riwayatpembelian

import id.co.bri.brimons.contract.IPresenter.transaksisaya.riwayatpembelian.IPurchaseHistoryPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.transaksisaya.riwayatpembelian.IPurchaseHistoryView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.config.LifestyleConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimons.models.apimodel.request.StatusRequest
import id.co.bri.brimons.models.apimodel.request.transaksisaya.riwayatpembelian.PurchaseHistoryRequest
import id.co.bri.brimons.models.apimodel.request.transaksisaya.riwayatpembelian.SearchFilterRequest
import id.co.bri.brimons.models.apimodel.response.CityFormResponse
import id.co.bri.brimons.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptKaiInboxTravel
import id.co.bri.brimons.models.apimodel.response.ReceiptResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampInboxResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.transaction.emptyresult.LifestyleTransactionEmptyResultResponseData
import id.co.bri.brimons.models.apimodel.response.lifestyle.transaction.filterbase.FilterBaseResponseData
import id.co.bri.brimons.models.apimodel.response.lifestyle.transaction.history.PurchaseHistoryResponseData
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

open class PurchaseHistoryPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IPurchaseHistoryPresenter<V> where V: IMvpView?, V: IPurchaseHistoryView? {

    var mUrlPurchaseHistory = ""
    var mUrlFilterBase = ""
    var mUrlSearchFilter = ""
    var mUrlInboxDetail = ""
    var mUrlTugu = ""
    var mTrxType = ""
    var mUrlFormBus = ""
    private var isSearch = false

    private var receiptResponse: ReceiptResponse? = null
    private var receiptRevampResponse: ReceiptRevampInboxResponse? = null
    private var statusRequest: StatusRequest? = null

    override fun setUrlPurchaseHistory(url: String) {
        this.mUrlPurchaseHistory = url
    }

    override fun setUrlFilterBase(url: String) {
        this.mUrlFilterBase = url
    }

    override fun setUrlSearchFilter(url: String) {
        this.mUrlSearchFilter = url
    }

    override fun setUrlInboxDetail(url: String) {
        this.mUrlInboxDetail = url
    }

    override fun setTrxType(trxType: String) {
        this.mTrxType = trxType
    }

    override fun setUrlWebViewTugu(urlTugu: String) {
        this.mUrlTugu = urlTugu
    }

    override fun setUrlFormBus(urlFormBus: String) {
        this.mUrlFormBus = urlFormBus
    }

    override fun getPurchaseHistoryResponse(request: PurchaseHistoryRequest, isRefresh: Boolean): Boolean {
        isSearch = false
        if (mUrlPurchaseHistory.isEmpty() || !isViewAttached) return isSearch

        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            mUrlPurchaseHistory,
            request,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView()?.onException(message)
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView()?.apply {
                        when (response?.code) {
                            RestResponse.ResponseCodeEnum.RC_01.value -> {
                                val responseData = response?.getData(
                                    LifestyleTransactionEmptyResultResponseData::class.java
                                )
                                responseData?.let { getView()?.onExceptionWithDialogFiltered(it) }
                            }
                            else -> {
                                val responseData = response?.getData(
                                    PurchaseHistoryResponseData::class.java
                                )
                                responseData?.let { getView()?.onSuccessPurchaseHistory(it, isRefresh) }
                            }
                        }

                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView()?.apply {
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc)
                                RestResponse.ResponseCodeEnum.RC_12.value -> this.onException(response.desc)
                                else -> this.onException(response.desc)
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
        return isSearch
    }

    override fun getFilterBase() {
        if (mUrlFilterBase.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getDataTanpaRequest(
            mUrlFilterBase,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView()?.onException(message)
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView()?.apply {
                        when (response?.code) {
                            RestResponse.ResponseCodeEnum.RC_01.value -> {
                                val responseData = response?.getData(
                                    LifestyleTransactionEmptyResultResponseData::class.java
                                )
                                responseData?.let { getView()?.onExceptionWithDialogFiltered(it) }
                            }
                            else -> {
                                val responseData = response?.getData(
                                    FilterBaseResponseData::class.java
                                )
                                responseData?.let { getView()?.onSuccessFilterBase(it) }
                            }
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView()?.apply {
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_12.value -> this.onExceptionSnackbar(restResponse.desc.orEmpty())
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getPurchaseHistorySearchFilter(request: SearchFilterRequest, isRefresh: Boolean): Boolean {
        isSearch = false
        if (mUrlFilterBase.isEmpty() || !isViewAttached) return false
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            mUrlSearchFilter,
            request,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView()?.onException(message)
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView()?.apply {
                        when (response?.code) {
                            RestResponse.ResponseCodeEnum.RC_01.value -> {
                                val responseData = response?.getData(
                                    LifestyleTransactionEmptyResultResponseData::class.java
                                )
                                responseData?.let { getView()?.onExceptionWithDialogFiltered(it) }
                            }
                            else -> {
                                val responseData = response?.getData(
                                    PurchaseHistoryResponseData::class.java
                                )
                                responseData?.let { getView()?.onSuccessPurchaseHistory(it, isRefresh) }
                            }
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView()?.apply {
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                RestResponse.ResponseCodeEnum.RC_01.value -> {
                                    val responseData = response.getData(
                                        LifestyleTransactionEmptyResultResponseData::class.java
                                    )
                                    responseData?.let { getView()?.onExceptionWithDialogFiltered(it) }
                                }
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        isSearch = true
        compositeDisposable.add(disposable)
        return isSearch
    }

    override fun getInboxDetail(refNum: String) {
        if (mUrlFilterBase.isEmpty() || !isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        statusRequest = StatusRequest(refNum)
        val disposable = apiSource.getData(
            mUrlInboxDetail,
            statusRequest,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView()?.apply {
                        hideProgress()
                        onException(message)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView()?.hideProgress()
                    try {
                        receiptResponse = response?.getData(ReceiptResponse::class.java)
                    } catch (e: Exception) {
                        // none
                    }

                    try {
                        receiptRevampResponse = response?.getData(ReceiptRevampInboxResponse::class.java)
                    } catch (e: Exception) {
                        // none
                    }

                    when (mTrxType) {
                        Constant.PURCHASE_KAI_TRAVEL -> {
                            val receiptKaiInboxTravel = response?.getData(ReceiptKaiInboxTravel::class.java)
                            getView()?.onSuccessKai(receiptKaiInboxTravel?.receiptTravelTrainResponse)
                        }

                        Constant.PURCHASE_KCIC_TRAVEL, Constant.PAYMENT_MOBELANJA,
                        Constant.PAYMENT_FLIGHT_TRAVEL, Constant.PAYMENT_MOLIGA -> {
                            val receiptRevampResponse = response?.getData(ReceiptRevampInboxResponse::class.java)
                            if (receiptRevampResponse != null) {
                                getView()?.onSuccessGetReceiptRevamp(receiptRevampResponse)
                            }
                        }

                        Constant.PURCHASE_VOUCHER_GAME, Constant.PAYMENT_VOUCHER_STREAMING,
                        Constant.PAYMENT_MOKIRIM ->
                            receiptRevampResponse?.let { getView()?.onSuccessGetDetailInboxRevamp(it) }

                        LifestyleConfig.LifestyleTrxType.TRX_MOBELANJA_PATTERN.trxType,
                        LifestyleConfig.LifestyleTrxType.TRX_PELNI_PATTERN.trxType ->
                            receiptRevampResponse?.let {
                                getView()?.onSuccessGetReceiptPattern(it)
                            }

                        LifestyleConfig.LifestyleTrxType.TRX_MOEVENT_LOKET.trxType ->
                            receiptRevampResponse?.let {
                                getView()?.onSuccessGetReceiptPatternBanner(it)
                            }

                        else -> {
                            receiptResponse = response?.getData(ReceiptResponse::class.java)
                            getView()?.onSuccessGetInboxDetail(receiptResponse)
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView()?.apply {
                            hideProgress()
                            when (response.code) {
                                Constant.RE12 -> this.onExceptionSnackbar(restResponse.desc.orEmpty())
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getWebViewTugu(partnerIdRequest: PartnerIdRequest?) {
        if (!isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            mUrlTugu,
            partnerIdRequest,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView()?.apply {
                        hideProgress()
                        onException(errorMessage)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView()?.apply {
                        hideProgress()
                        val webViewResponse = response.getData(GeneralWebviewResponse::class.java)
                        onSuccessGetWebViewTugu(webViewResponse)
                    }
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView()?.apply {
                        hideProgress()
                        when (restResponse.code) {
                            Constant.RE12 -> this.onExceptionSnackbar(restResponse.desc.orEmpty())
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(restResponse.desc.orEmpty())
                            else -> this.onException(restResponse.desc.orEmpty())
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getFormBus() {
        if (!isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getDataTanpaRequest(
            mUrlFormBus,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView()?.apply {
                        hideProgress()
                        onException(errorMessage)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView()?.apply {
                        hideProgress()
                        val cityFormResponse = response.getData(CityFormResponse::class.java)
                        onSuccessGetForm(cityFormResponse)
                    }
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView()?.apply {
                        hideProgress()
                        when (restResponse.code) {
                            Constant.RE12 -> this.onExceptionSnackbar(restResponse.desc.orEmpty())
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(restResponse.desc.orEmpty())
                            else -> this.onException(restResponse.desc.orEmpty())
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

}