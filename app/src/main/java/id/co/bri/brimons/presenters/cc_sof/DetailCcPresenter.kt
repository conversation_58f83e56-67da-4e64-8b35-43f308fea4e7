package id.co.bri.brimons.presenters.cc_sof

import id.co.bri.brimons.contract.IPresenter.cc_sof.IDetailCcPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.cc_sof.IDetailCcView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.CheckBlockRequest
import id.co.bri.brimons.models.apimodel.request.InquiryKreditRequest
import id.co.bri.brimons.models.apimodel.response.CheckBlockResponse
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DetailCcPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailCcPresenter<V> where V : IMvpView, V : IDetailCcView {

    private var urlCheckBlock = ""
    private var urlInquiry = ""

    override fun setUrlCheckBlock(url: String) {
        urlCheckBlock = url
    }

    override fun setInquiryUrl(url: String) {
        urlInquiry = url
    }

    override fun getDataCheckBlock(request: CheckBlockRequest) {
        if (urlCheckBlock.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlCheckBlock, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val checkBlockResponse = response.getData(
                            CheckBlockResponse::class.java
                        )
                        getView().onSuccessCheckBlock(checkBlockResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquiry(request: InquiryKreditRequest) {
        if (urlInquiry.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlInquiry, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val responseKredit = response.getData(
                            GeneralInquiryResponse::class.java
                        )
                        if (responseKredit.openPayment == true) {
                            if (responseKredit.isBilling == true)
                                getView().gotoInquiryClose(responseKredit)
                            else getView().gotoInquiryOpen(responseKredit)
                        } else getView().gotoInquiryClose(responseKredit)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }
}