package id.co.bri.brimons.presenters.activationdebit

import id.co.bri.brimons.contract.IPresenter.activationdebit.IActivationDebitOtpPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.activationdebit.IActivationDebitOtpView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.activationdebit.request.SubmitOtpActivationDebitRequest
import id.co.bri.brimons.models.activationdebit.request.ValidateActivationDebitRequest
import id.co.bri.brimons.models.activationdebit.response.ActivationDebitResponse
import id.co.bri.brimons.models.activationdebit.response.SubmitOtpResponse
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class ActivationDebitOtpPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IActivationDebitOtpPresenter<V> where V : IMvpView, V : IActivationDebitOtpView {

    override fun getSubmitOtp(
        urlOtp: String,
        submitOtpActivationDebitRequest: SubmitOtpActivationDebitRequest
    ) {
        view.getDataWithOrWithoutRequest(
            urlOtp,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            submitOtpActivationDebitRequest,
            onApiCallError = {
                view.onFailedSubmitOtp(it.desc)
            }
        ) {
            view.onSuccessSubmitOtp(it.getData(SubmitOtpResponse::class.java))
        }
    }

    override fun getValidateActivation(
        urlValidate: String,
        validateActivationDebitRequest: ValidateActivationDebitRequest?
    ) {
        view.getDataWithOrWithoutRequest(
            urlValidate,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            validateActivationDebitRequest,
            onApiCallError = {
                view.onException(it.desc)
            }
        ) {
            view.onSuccessResendOtp(it.getData(ActivationDebitResponse::class.java))
        }
    }
}