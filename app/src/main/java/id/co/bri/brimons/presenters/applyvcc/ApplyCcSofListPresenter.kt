package id.co.bri.brimons.presenters.applyvcc

import android.util.Log
import com.google.gson.Gson
import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.applyvcc.IApplyCcSofListPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.applyvcc.IApplyCcSofListView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.DetailCcSofRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SnkResponse
import id.co.bri.brimons.models.apimodel.response.applyccrevamp.ApplyCcSofListResponse
import id.co.bri.brimons.models.apimodel.response.cc.DetailCcSofResponse
import id.co.bri.brimons.models.apimodel.response.cc.LimitCcSofResponse
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class ApplyCcSofListPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IApplyCcSofListPresenter<V> where V : IMvpView, V : IApplyCcSofListView {

    private var ccSofListResponse: ApplyCcSofListResponse? = null

    private var urlSofList: String? = null
    private var urlCheckLimit: String? = null
    private var urlDetailCcSof: String? = null
    private var urlTerm: String? = null
    private var count = 0
    private var isLoading = false

    override fun setUrlListCcSof(urlSofList: String) {
        this.urlSofList = urlSofList
    }

    override fun setUrlCheckLimitCcSof(urlCheckLimit: String) {
        this.urlCheckLimit = urlCheckLimit
    }

    override fun setUrlDetailCcSof(urlDetailCcSof: String) {
        this.urlDetailCcSof = urlDetailCcSof
    }

    override fun setUrlTerm(urlTerm: String) {
        this.urlTerm = urlTerm
    }

    override fun getAccountWithSaldo() {
        if (isLoading) return

        if (isViewAttached) {
            isLoading = true
            view.getDataWithOrWithoutRequest(
                urlSofList,
                brImoPrefRepository,
                compositeDisposable,
                apiSource,
                schedulerProvider,
                false,
                onApiCallError = { restResponse ->
                    isLoading = false
                    when {
                        restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true) -> {
                            ++count
                            getView().onSessionEnd(restResponse.desc)
                        }

                        restResponse.desc.contains("HV01", true) -> view.onGetAccountWithSaldoSuccess01("")
                        else -> getView().onException(restResponse.desc)
                    }
                },
            ) { response ->
                isLoading = false
                when {
                    response.code.equals("00", ignoreCase = true) -> {
                        val data = response.getData(ApplyCcSofListResponse::class.java)
                        ccSofListResponse = data
                        getView().onGetAccountWithSaldoSuccess(data)
                    }

                    response.code.equals("01", ignoreCase = true) -> view.onGetAccountWithSaldoSuccess01("")
                }
            }
        }
    }

    override fun getTerm() {
        view.onGetTermLoading(true)
        view.getDataWithOrWithoutRequest(
            urlTerm,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            false,
            onFailureHttp = {
                view.onGetTermLoading(false)
                view.onException(it)
            },
            onApiCallError = {
                view.onGetTermLoading(false)
                if (it.code.equals("05", ignoreCase = true)) {
                    ++count
                    checkSession(it.desc)
                } else {
                    view.onException(it.desc)
                }
            }
        ) {
            view.onGetTermLoading(false)
            val snkResponse: SnkResponse = it.getData(SnkResponse::class.java)
            view.onGetTermSuccess(snkResponse)
        }
    }

    private fun checkSession(msg: String) {
        if (count == 1) {
            view.onSessionEnd(msg)
        } else {
            stop()
        }
    }

    override fun stop() {
        if (isViewAttached)
            view.onGetLimitComplete()
        super.stop()
    }

    override fun getCcSofLimit() {
        view.getDataWithOrWithoutRequest(
            urlCheckLimit,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            false,
            onApiCallError = { restResponse ->
                when {
                    restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true) -> {
                        ++count
                        getView().onSessionEnd(restResponse.desc)
                    }
                }
            }
        ) { response ->
            val data = response.getData(LimitCcSofResponse::class.java)
            getView()?.onLimitCheckComplete(data)
        }
    }

    override fun getDetailApplyVcc(cardToken: String, itemPosition: Int, account: ApplyCcSofListResponse.Account) {
        val request = DetailCcSofRequest(cardToken)
        view.getDataWithOrWithoutRequest(
            urlDetailCcSof,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            false,
            request,
            onApiCallError = { restResponse ->
                val detailCcSofResponse = DetailCcSofResponse()
                detailCcSofResponse.balanceString = restResponse.code
                detailCcSofResponse.name = restResponse.desc
                account.detailCcResponse = detailCcSofResponse
                view.getDetailApplyVccError(account, itemPosition)
            }
        ) {
            val detailCcSofResponse = it.getData(DetailCcSofResponse::class.java)
            account.detailCcResponse = detailCcSofResponse
            view.getDetailApplyVccSuccess(account, itemPosition)
        }
    }
}