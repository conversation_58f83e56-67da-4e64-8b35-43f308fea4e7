package id.co.bri.brimons.presenters

import id.co.bri.brimons.contract.MasukkanPinContract

class MasukkanPinPresenter(private var view: MasukkanPinContract.View) : MasukkanPinContract.Presenter {

    override fun checkPin(pin: String) {
        if (!pin.equals("")) view.onCorrectPin()
        else view.onWrongPin()
    }


    override fun start() {

    }

    override fun stop() {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }


}