package id.co.bri.brimons.presenters.britamarencana;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.britamarencana.IPilihTabunganPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.britamarencana.IPilihTabunganView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest;
import id.co.bri.brimons.models.apimodel.response.bukarekening.JenisTabunganResponse;
import id.co.bri.brimons.models.apimodel.response.QuestionResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observables.ConnectableObservable;
import io.reactivex.schedulers.Schedulers;

public class  PilihTabunganPresenter<V extends IMvpView & IPilihTabunganView> extends MvpPresenter<V>
        implements IPilihTabunganPresenter<V> {

    private JenisTabunganResponse jenisTabunganResponse = new JenisTabunganResponse();
    private List<JenisTabunganResponse.Product> productList;
    private String mUrl;
    private String urlPusatBantuanSafety;

    public PilihTabunganPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getJenisTabungan() {
        if (isViewAttached()) {
            productList = new ArrayList<>();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            ConnectableObservable<String> listConnectableObservable =
                    getApiSource().getDataForm(mUrl, seqNum).subscribeOn(getSchedulerProvider().io())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .replay();
            getCompositeDisposable().add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            jenisTabunganResponse = restResponse.getData(JenisTabunganResponse.class);
                            productList.addAll(jenisTabunganResponse.getProduk());
                            getView().onSuccessGetData(jenisTabunganResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                            else {
                                getView().onException(restResponse.getDesc());
                            }
                        }

                        @Override
                        public void onComplete() {
                            super.onComplete();
                        }
                    }));
            listConnectableObservable.connect();

        }


    }

    @Override
    public void getPusatBantuanSafety(String id) {
        if (urlPusatBantuanSafety == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        SafetyPusatBantuanRequest safetyPusatBantuanRequest = new SafetyPusatBantuanRequest(id);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlPusatBantuanSafety, safetyPusatBantuanRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                QuestionResponse topicQuestionResponse = response.getData(QuestionResponse.class);

                                if (urlPusatBantuanSafety != null)
                                    getView().onSuccessPusatBantuanSafety(topicQuestionResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void setUrlPusatBantuanSafety(String urlPusatBantuanSafety) {
        this.urlPusatBantuanSafety = urlPusatBantuanSafety;
    }

    @Override
    public void setUrl(String url) {
        this.mUrl = url;
    }
}