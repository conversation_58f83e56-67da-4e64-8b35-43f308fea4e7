package id.co.bri.brimons.presenters.onboardingrevamp

import android.content.Intent
import android.util.Log
import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingRekeningPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingRekeningView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant

import id.co.bri.brimons.domain.helpers.DeviceIDHelper
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.ReferralRequest
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.StatusResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.OnboardingReferralRes
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.TabunganResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.security.NoSuchAlgorithmException
import java.util.concurrent.TimeUnit

class OnboardingRekeningPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingRekeningPresenter<V> where V : IMvpView, V : IOnboardingRekeningView {

    private var urlProgress: String = ""
    private var urlReferral: String = ""
    override fun checkDevice() {
        if (brImoPrefRepository.isContains(Constant.USER_TYPE)) brImoPrefRepository.saveUserExist(
            true
        )

        if (!brImoPrefRepository.isContains(Constant.DEVICE_ID)) {
            generateDeviceId()
        }
    }

    override fun generateDeviceId() {
        try {
            brImoPrefRepository.saveDeviceId(DeviceIDHelper.setDeviceID())
            brImoPrefRepository.saveDeviceId2(DeviceIDHelper.setIdPersistent())
            brImoPrefRepository.deleteSeqNumber()
        } catch (e: NoSuchAlgorithmException) {
            if (!GeneralHelper.isProd()) Log.e("TAG", ": ", e)
        }
    }

    override fun setUrlProgress(url: String) {
        urlProgress = url
    }

    override fun setUrlReferral(url: String) {
        urlReferral = url
    }

    override fun getProgressOnboarding() {
        if (urlProgress.isEmpty() || !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlProgress, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        when (response.code) {
                            RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> {
                                val progressResponse = response.getData(StatusResponse::class.java)
                                if (progressResponse.status == 0) {
                                    val status0Response =
                                        response.getData(TabunganResponse::class.java)
                                    getView().onSavingsDataView(status0Response)
                                } else {
                                    getView().onCheckPointView(
                                        progressResponse.status,
                                        Gson().toJson(response.data)
                                    )
                                }
                            }

                            RestResponse.ResponseCodeEnum.RC_01.value -> {
                                getView().onExceptionRevamp(Constant.TRANSAKSI_GAGAL)
                            }

                            RestResponse.ResponseCodeEnum.RC_02.value -> {
                                val forceUpdateRes =
                                    response.getData(ForceUpdateResponse::class.java)
                                getView().onUpdateVersion(forceUpdateRes)
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun sendReferral(request: ReferralRequest) {
        if (urlReferral.isEmpty() || !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlReferral, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val progressResponse = response.getData(StatusResponse::class.java)
                        when (progressResponse.status) {
                            0 -> {
                                val referralRes =
                                    response.getData(OnboardingReferralRes::class.java)
                                val productResponse = response.getData(TabunganResponse::class.java)
                                when {
                                    referralRes.selectProduct && referralRes.selectBranch ->
                                        getView().onSavingsDataView(productResponse)

                                    referralRes.selectProduct && !referralRes.selectBranch ->
                                        getView().onSkipOffice(productResponse)

                                    !referralRes.selectProduct && referralRes.selectBranch ->
                                        getView().onSkipProduct()
                                }
                            }

                            1 -> getView().onPhotoKtp()
                            else -> getView().onCheckPointView(
                                progressResponse.status,
                                Gson().toJson(response.data)
                            )
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun checkUsername(intent: Intent) {
        if (brImoPrefRepository.username.isEmpty())
            view.onCheckReferral(intent)
        else view.onLoginExist()
    }
}