package id.co.bri.brimons.presenters.lifestyle

import id.co.bri.brimons.contract.IPresenter.lifestyle.IRepurchaseLifestylePresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.lifestyle.IRepurchaseLifestyleView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimons.models.apimodel.response.CityFormResponse
import id.co.bri.brimons.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.UrlWebViewResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class RepurchaseLifestylePresenter <V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    IRepurchaseLifestylePresenter<V> where V : IMvpView?, V : IRepurchaseLifestyleView? {

    lateinit var mUrlFormBus: String
    lateinit var mUrlFormKai: String
    lateinit var mUrlWebTugu: String

    override fun setUrlBusShuttle(urlFormBus: String) {
        mUrlFormBus = urlFormBus
    }

    override fun setUrlKai(urlFormKai: String) {
        mUrlFormKai = urlFormKai
    }

    override fun setUrlWebviewTugu(urlWebTugu: String) {
        mUrlWebTugu = urlWebTugu
    }

    override fun getFormBus() {
        if (!isViewAttached)
            return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataForm(mUrlFormBus, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val cityFormResponse = response.getData(CityFormResponse::class.java)
                        getView()!!.onSuccessGetFormBus(cityFormResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException99(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getFormKai(mTitle: String) {
        if (!isViewAttached)
            return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(mUrlFormKai, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val urlWebViewResponse = response.getData(
                            UrlWebViewResponse::class.java
                        )
                        getView()!!.onSuccessGetFormKai(urlWebViewResponse, mTitle)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException99(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getWebviewTugu(partnerIdRequest: PartnerIdRequest, mTitle: String) {
        if (!isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(mUrlWebTugu, partnerIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val webviewResponse = response.getData(
                            GeneralWebviewResponse::class.java
                        )
                        getView()!!.onSuccessGetWebviewTugu(webviewResponse, mTitle)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()!!.onSessionEnd(restResponse.desc)
                        } else {
                            getView()!!.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

}