package id.co.bri.brimons.presenters.splitbill

import android.util.Log
import com.google.gson.Gson
import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.splitbill.ISplitBillAddMemberPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.splitbill.ISplitBillAddMemberView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.extension.isNumeric
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.LifestyleHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.splitbill.AddMemberRequest
import id.co.bri.brimons.models.apimodel.request.splitbill.InquiryMemberRequest
import id.co.bri.brimons.models.apimodel.request.splitbill.SaveListMemberRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.CurrentMemberResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.UserData
import id.co.bri.brimons.models.splitbill.SplitBillAddMemberItemViewModel
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit
import kotlin.random.Random
import kotlin.random.nextUInt

class SplitBillAddMemberPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource, transaksiPfmSource
    ),
    ISplitBillAddMemberPresenter<V> where V : IMvpView?, V : ISplitBillAddMemberView? {

    private val addedMembers = mutableListOf<SplitBillAddMemberItemViewModel>()
    private val viewRecentMembers = mutableListOf<SplitBillAddMemberItemViewModel>()

    private lateinit var recentMember: SplitBillAddMemberItemViewModel

    //to hold all recent members in support of filter members
    private val tempOriginalRecentMembers = mutableListOf<SplitBillAddMemberItemViewModel>()

    private var memberResponse: UserData? = null
    private var userDatas: MutableList<SaveListMemberRequest> = mutableListOf()

    private var mUrlInquiryMember = ""
    private var mUrlAddedNewMember = ""

    private var counterAddNewMember = -1

    override fun setUrlInquiryMember(urlInquiryMember: String) {
        mUrlInquiryMember = urlInquiryMember
    }

    override fun setUrlAddedNewMember(urlNewMember: String) {
        mUrlAddedNewMember = urlNewMember
    }

    override fun fetchIntentChosenMembers(
        addMembers: List<SplitBillAddMemberItemViewModel>,
        recentMembers: List<SplitBillAddMemberItemViewModel>
    ) {
        addedMembers.clear()
        addedMembers.addAll(addMembers)

        tempOriginalRecentMembers.clear()
        tempOriginalRecentMembers.addAll(recentMembers)
        viewRecentMembers.addAll(tempOriginalRecentMembers)

        for (recentMem in viewRecentMembers) {
            recentMember = recentMem
        }

        compareAddedToRecent(recentMember)
    }

    override fun fetchRecentMembers() {
        view?.showRecentMembers(viewRecentMembers)
        view?.showAddedMembers(addedMembers)
        view?.showSkeleton(false)
    }

    override fun deleteAddedMember(model: SplitBillAddMemberItemViewModel) {
        addedMembers.firstOrNull { it.id == model.id }?.let {
            addedMembers.remove(it)
        }
        view?.showAddedMembers(addedMembers)
        updateRecentMember(model, false)
    }

    override fun doFilterRecentMembers(searchQuery: String) {
        val listRecent = if (searchQuery.isEmpty()) {
            viewRecentMembers
        } else {
            val keyWords = searchQuery.lowercase().trim()
            viewRecentMembers.filter { data ->
                data.name.contains(keyWords, true)
                        || data.alias.contains(keyWords, true)
                        || (keyWords.isNumeric() || data.accNumber.contains(keyWords))
            }
        }

        view?.showRecentMembers(
            models = listRecent,
            searchQuery = searchQuery
        )
    }

    override fun addFromRecentMembers(model: SplitBillAddMemberItemViewModel) {
        addedMembers.firstOrNull { it.id == model.id }?.let {
            return
        }
        addedMembers.add(model.copy(isListRemovable = true))
        view?.showAddedMembers(addedMembers)
        updateRecentMember(model, true)
    }

    private fun compareAddedToRecent(recentMembers: SplitBillAddMemberItemViewModel) {
        addedMembers.firstOrNull { it.id == recentMembers.id }?.let {
            updateRecentMember(recentMembers, true)
        }
        fetchRecentMembers()
    }

    override fun getChosenMembersJson(): String {
        val membersJson = Gson().toJson(addedMembers, addedMembers::class.java)
        return membersJson
    }

    private fun updateRecentMember(
        model: SplitBillAddMemberItemViewModel,
        isChecked: Boolean
    ) {
        val updatedModel = model.copy(isListRemovable = false, isChecked = isChecked)
        val idxModelTempOrigin = tempOriginalRecentMembers.indexOfFirst { it.id == model.id }
        val idxModelViewRecent = viewRecentMembers.indexOfFirst { it.id == model.id }

        updatedModel.copy(isChecked = isChecked).also { element ->
            if (idxModelTempOrigin != -1)
                tempOriginalRecentMembers[idxModelTempOrigin] = element
            if (idxModelViewRecent != -1)
                viewRecentMembers[idxModelViewRecent] = element
        }
        view?.showRecentMembers(viewRecentMembers)
    }

    override fun getInquiryMember(newMemberInput: String) {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view?.showProgress()
            compositeDisposable.add(
                apiSource.getData(mUrlInquiryMember, InquiryMemberRequest(newMemberInput), seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            memberResponse = response.getData(
                                UserData::class.java
                            )

                            val randomPositive = Random.nextInt(Int.MAX_VALUE)

                            val isDuplicateAccNumberInAdd = addedMembers.any { it.accNumber == memberResponse?.accountNumber }
                            if (!isDuplicateAccNumberInAdd){
                                addedMembers.add(
                                    SplitBillAddMemberItemViewModel(
                                        id = randomPositive,
                                        name = memberResponse?.accountName!!,
                                        accNumber = memberResponse?.accountNumber!!,
                                        alias = memberResponse?.userAlias!!,
                                        isListRemovable = true,
                                        isNonBrimo = false,
                                        username = memberResponse?.username!!,
                                        isNew = true
                                    )
                                )
                            }

                            val isDuplicateAccNumberInRecent = viewRecentMembers.any { it.accNumber == memberResponse?.accountNumber }
                            if (!isDuplicateAccNumberInRecent){
                                viewRecentMembers.add(
                                    SplitBillAddMemberItemViewModel(
                                        id = randomPositive,
                                        name = memberResponse?.accountName!!,
                                        accNumber = memberResponse?.accountNumber!!,
                                        alias = memberResponse?.userAlias!!,
                                        isListRemovable = true,
                                        isChecked = true,
                                        isNonBrimo = false,
                                        isNew = true
                                    )
                                )
                            }

                            if (isDuplicateAccNumberInAdd && isDuplicateAccNumberInRecent){
                                getView()?.onExceptionAddNewMember(GeneralHelper.getString(R.string.txt_validation_duplicate_member))
                            } else {
                                getView()?.showRecentMembers(viewRecentMembers)
                                getView()?.showAddedMembers(addedMembers)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onExceptionAddNewMember(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun getAddedNewMember() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view?.showProgress()
            compositeDisposable.add(
                apiSource.getData(mUrlAddedNewMember, AddMemberRequest(userDatas), seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()

                            val addMembersResponse: CurrentMemberResponse = response.getData(
                                CurrentMemberResponse::class.java
                            )

                            addedMembers.filter { it.isNew }
                                .zip(addMembersResponse.members)
                                .forEach { (newMember, member) ->
                                    newMember.apply {
                                        id = member.memberId
                                        accNumber = if (member.accountNumber != null){
                                            member.accountNumber
                                        } else {
                                            ""
                                        }
                                        name = if (member.accountName != null){
                                            member.accountName
                                        } else {
                                            ""
                                        }
                                        alias = if (member.userAlias != null){
                                            member.userAlias
                                        } else {
                                            ""
                                        }
                                        username = if (member.username != null){
                                            member.username
                                        } else {
                                            ""
                                        }
                                        isNonBrimo = member.typeMember == 0
                                    }
                                }


                            getView()?.onAddNewMember()
                            getView()?.showRecentMembers(viewRecentMembers)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onException(restResponse.desc)
                        }
                    })
            )
        }

        view?.showAddedMembers(addedMembers)
    }

    override fun checkIsAddNewMember() {
        userDatas.clear()
        userDatas = addedMembers.filter {
            it.isNew
        }.map {
            SaveListMemberRequest(
                userAlias = it.alias,
                accountName = it.name,
                typeMember = LifestyleHelper.mapBooleanToInt(it.isNonBrimo),
                accountNumber = it.accNumber.trim(),
                username = it.username
            )
        }.toMutableList()

        if (userDatas.size > 0) {
            getAddedNewMember()
        } else {
            getView()?.onAddNewMember()
        }
    }

    override fun onNewMemberSubmitted(newMemberInput: String) {
        if (newMemberInput.isNumeric() || newMemberInput.startsWith("@", ignoreCase = true)) {
            getInquiryMember(newMemberInput)
        } else {
            val randomPositive = Random.nextInt(Int.MAX_VALUE)

            addedMembers.add(
                SplitBillAddMemberItemViewModel(
                    id = randomPositive,
                    name = newMemberInput,
                    accNumber = "",
                    alias = "",
                    isListRemovable = true,
                    isNonBrimo = true,
                    isNew = true
                )
            )

            viewRecentMembers.add(
                SplitBillAddMemberItemViewModel(
                    id = randomPositive,
                    name = newMemberInput,
                    accNumber = "",
                    alias = "",
                    isListRemovable = true,
                    isNonBrimo = true,
                    isChecked = true,
                    isNew = true
                )
            )

            getView()?.showAddedMembers(addedMembers)
            getView()?.showRecentMembers(viewRecentMembers)
        }
    }

}
