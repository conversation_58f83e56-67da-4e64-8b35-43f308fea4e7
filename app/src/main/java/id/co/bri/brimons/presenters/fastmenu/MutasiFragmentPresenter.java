package id.co.bri.brimons.presenters.fastmenu;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.fastmenu.IMutasiFragmentPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.fastmenu.IMutasiFragmentView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.FastMenuFiveResponse;
import id.co.bri.brimons.models.apimodel.request.FastMutasiRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class MutasiFragmentPresenter<V extends IMvpView & IMutasiFragmentView> extends MvpPresenter<V> implements IMutasiFragmentPresenter<V> {

    private String mUsername = null;
    private String mTokenKey = null;
    private String mAccountDefault = null;

    public MutasiFragmentPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getFastMutasi() {
        if (getView() != null) {

            mTokenKey = getBRImoPrefRepository().getTokenKey();
            mUsername = getBRImoPrefRepository().getUsername();
            mAccountDefault = getBRImoPrefRepository().getSaldoRekeningUtama();


            if (mUsername == null || mTokenKey == null || mAccountDefault == null || mTokenKey.isEmpty() || mUsername.isEmpty())
                return;

            FastMutasiRequest request = new FastMutasiRequest(mUsername, mAccountDefault, mTokenKey);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(GeneralHelper.getString(R.string.fastmenu_Mutasi), request, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().newThread())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
//                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
//                            getView().hideProgress();
                            FastMenuFiveResponse fastMenuFiveResponse = response.getData(FastMenuFiveResponse.class);
                            getView().onSuccessFastMutasi(fastMenuFiveResponse.getTransaction());
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
//                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }
}