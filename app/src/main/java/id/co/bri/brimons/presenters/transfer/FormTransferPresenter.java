package id.co.bri.brimons.presenters.transfer;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.transfer.IFormTransferPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transfer.IFormTransferView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryTransferRequest;
import id.co.bri.brimons.models.apimodel.response.DataTransferResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryTransferResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by FNS
 */

public class FormTransferPresenter<V extends IMvpView & IFormTransferView> extends MvpPresenter<V> implements IFormTransferPresenter<V> {

    private static final String TAG = "FormTransferPresenter";
    protected String urlInqTransfer;
    protected String urlFormTransfer;

    public FormTransferPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getData() {
        if (getView() != null) {
            //initiate param with getter from view

            if(urlFormTransfer != null){
                Log.d(TAG, "getData: FormTransferPresenter");
                return;
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getDataForm(urlFormTransfer,seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            //TO-DO onSuccess
                            DataTransferResponse response1 = response.getData(DataTransferResponse.class);
                            getView().onLoadSuccess(response1);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            onApiError(restResponse);
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void getDataInquiry(String kodeBank, String norek) {
        if (getView() != null) {
            //initiate param with getter from view
            getView().showProgress();

            InquiryTransferRequest request = new InquiryTransferRequest(kodeBank, norek);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlInqTransfer, request, seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {


                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            InquiryTransferResponse inquiryTransferResponse = response.getData(InquiryTransferResponse.class);
                            getView().onSuccessGetData(inquiryTransferResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }

    }

    @Override
    public void onApiError(RestResponse restResponse) {
        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
            getView().hideProgress();
            getView().onException12(restResponse.getDesc());
        }else {
            super.onApiError(restResponse);
        }
    }

    public void setUrlFormTransfer(String urlFormTransfer) {
        this.urlFormTransfer = urlFormTransfer;
    }


    public void setUrlInqTransfer(String urlInqTransfer) {
        this.urlInqTransfer = urlInqTransfer;
    }

}