package id.co.bri.brimons.presenters.deposito;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.deposito.ITambahDaftarDepositoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.deposito.ITambahDaftarDepositoView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.openDepoInquiryRequest;
import id.co.bri.brimons.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TambahDaftarDepositoPresenter<V extends IMvpView & ITambahDaftarDepositoView>
        extends MvpPresenter<V> implements ITambahDaftarDepositoPresenter<V> {

    protected String urlInquiry;
    protected String urlKonfirmasi;
    protected String urlpayment;

    public TambahDaftarDepositoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlInquiry(String urlInquiry) {
        this.urlInquiry = urlInquiry;
    }

    @Override
    public void setUrlKonfirmasi(String urlKonfirmasi) {
        this.urlKonfirmasi = urlKonfirmasi;
    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlpayment = urlPayment;
    }

    @Override
    public void sendDataInquiry(openDepoInquiryRequest request) {
        if (isViewAttached()) {
            //initiate param with getter from view

            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlInquiry, request,seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InquiryOpenRencanaResponse inquiryResponse = response.getData(InquiryOpenRencanaResponse.class);
                                    getView().getDataSuccess(inquiryResponse, urlKonfirmasi, urlpayment, false);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }
}