package id.co.bri.brimons.presenters.deposito;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.deposito.IInquiryOpenDepositoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.deposito.IInquiryOpenDepositoView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.OpenDepoConfirmReq;
import id.co.bri.brimons.models.apimodel.response.DepositoConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryOpenDepositoPresenter<V extends IMvpView & IInquiryOpenDepositoView>
        extends MvpPresenter<V> implements IInquiryOpenDepositoPresenter<V> {

    protected String urlConfirm;

    public InquiryOpenDepositoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void start() {
        super.start();
        getDefaultSaldo();
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold);
    }

    @Override
    public void setUrlConfirmation(String urlConfrim) {
        this.urlConfirm = urlConfrim;
    }

    @Override
    public void senDataConfirm(String refNum, String account) {
        if (isViewAttached()) {

            getView().showProgress();

            OpenDepoConfirmReq confirmationRequest = new OpenDepoConfirmReq(refNum, account);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlConfirm, confirmationRequest,seqNum)//function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            onLoad = false;
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            onLoad = false;
                            getView().hideProgress();
                            DepositoConfirmationResponse konfirmasiResponse = response.getData(DepositoConfirmationResponse.class);
                            getView().getDataSuccessConfirm(konfirmasiResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            onLoad = false;
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
/*                            else if (restResponse.getCode().equalsIgnoreCase("93"))
                                getView().onException93(restResponse.getDesc());*/
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }
}