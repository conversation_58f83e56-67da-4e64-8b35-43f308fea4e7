package id.co.bri.brimons.presenters.oneShild;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.oneshild.IOneShildPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.oneshild.IOneShildView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.CeriaResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class OneShildPresenter<V extends IMvpView & IBaseFormView & IOneShildView> extends BaseFormPresenter<V> implements IOneShildPresenter<V> {

    String url;

    public OneShildPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onGetOneShild() {
        if (url == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getInfoKurs(url, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
//                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
//                        getView().hideProgress();
                        CeriaResponse ceriaResponse = response.getData(CeriaResponse.class);
                        if (url != null) {
                            getView().onSuccessGetOneShild(ceriaResponse);
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                            getView().onException12Web(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);



    }

    @Override
    public void onGetOneshildFastMenu() {
        if (!isViewAttached() || url.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable().add(
                getApiSource().getData(url, getFastMenuRequest(),seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                CeriaResponse ceriaResponse = response.getData(CeriaResponse.class);
                                if (url != null) {
                                    getView().onSuccessGetOneShild(ceriaResponse);
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12Web(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

}