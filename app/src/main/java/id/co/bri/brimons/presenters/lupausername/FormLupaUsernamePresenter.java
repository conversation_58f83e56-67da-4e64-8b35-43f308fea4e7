package id.co.bri.brimons.presenters.lupausername;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.lupausername.IFormLupaUsernamePresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.lupausername.IFormLupaUsernameView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ForgetUsernameInquReq;
import id.co.bri.brimons.models.apimodel.response.DetailAkunResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class FormLupaUsernamePresenter<V extends IMvpView & IFormLupaUsernameView>
        extends MvpPresenter<V> implements IFormLupaUsernamePresenter<V> {

    private String url;

    public FormLupaUsernamePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                     BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource,
                                     TransaksiPfmSource transaksiPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void sendDataForm(ForgetUsernameInquReq reqest) {
        if (url.isEmpty() || !isViewAttached()) return;
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable().add(getApiSource().getData(url, reqest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        DetailAkunResponse dataResponse = response.getData(DetailAkunResponse.class);
                        getView().onDataSuccess(dataResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        getView().onException(restResponse.getDesc());
                    }
                }));
    }

    @Override
    public void getIsNotLogin() {
        getView().isNotLogin(getBRImoPrefRepository().getUsername().isEmpty());
    }
}