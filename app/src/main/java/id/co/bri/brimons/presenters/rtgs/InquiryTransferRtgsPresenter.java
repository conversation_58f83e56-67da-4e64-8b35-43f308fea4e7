package id.co.bri.brimons.presenters.rtgs;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.rtgs.IInquiryTransferRtgsPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.rtgs.IInquiryTransferRtgsView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ConfirmationRequest;
import id.co.bri.brimons.models.apimodel.request.FastConfirmationRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryTransferRtgsPresenter<V extends IMvpView & IInquiryTransferRtgsView> extends MvpPresenter<V> implements IInquiryTransferRtgsPresenter<V> {
    protected static final String TAG = "InquiryBrivaPresenter";
    protected String urlConfirmation = null;
    protected Object confirmationRequest;


    public InquiryTransferRtgsPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }
    /**
     * @param refNum
     * @param accountNum
     * @param amount
     * @param save
     * @param fromFastMenu
     */
    @Override
    public void getDataConfirmation(String refNum, String accountNum, String amount, String save, boolean fromFastMenu) {
        if (isViewAttached() && urlConfirmation != null && !onLoad) {
            //flag on Load true
            onLoad = true;
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();

            if (fromFastMenu)
                confirmationRequest = new FastConfirmationRequest(getFastMenuRequest(), refNum, accountNum, amount, save);
            else
                confirmationRequest = new ConfirmationRequest(refNum, accountNum, amount, save);


            Disposable disposable = getApiSource().getData(urlConfirmation, confirmationRequest, seqNum)//function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            onLoad = false;
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            onLoad = false;
                            getView().hideProgress();
                            GeneralConfirmationResponse brivaResponse = response.getData(GeneralConfirmationResponse.class);
                            getView().onSuccessGetConfirmation(brivaResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            onLoad = false;
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())){
                                getView().onException99(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }
    /**
     * @param urlConfirmation
     */
    @Override
    public void setUrlConfirmation(String urlConfirmation) {
        this.urlConfirmation = urlConfirmation;
    }


    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();

        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        getView().setDefaultSaldo(saldo, saldoString, defaultAcc);
    }

}