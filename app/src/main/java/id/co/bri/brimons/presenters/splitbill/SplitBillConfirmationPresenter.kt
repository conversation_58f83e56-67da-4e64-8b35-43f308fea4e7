package id.co.bri.brimons.presenters.splitbill

import id.co.bri.brimons.contract.IPresenter.splitbill.ISplitBillConfirmationPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.splitbill.ISplitBillConfirmationView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.LifestyleHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.AccountModel
import id.co.bri.brimons.models.apimodel.request.splitbill.ConfirmationSplitBillRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.ConfirmationSplitBillResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.GenerateBillResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.SplitBillDetailResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.TempBillViewModel
import id.co.bri.brimons.models.splitbill.SplitBillAddMemberItemViewModel
import id.co.bri.brimons.models.splitbill.SplitBillEditFormItemViewModel
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class SplitBillConfirmationPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ), ISplitBillConfirmationPresenter<V> where V : IMvpView?, V : ISplitBillConfirmationView? {

    private var addedMembers = mutableListOf<SplitBillAddMemberItemViewModel>()

    private lateinit var model: AccountModel
    private var mListAccountModel: List<AccountModel>? = null

    private var mUrlDraft = ""
    private var mUrlGenerate = ""
    private var mUrlHistoryBill = ""
    private var mBillId = 0
    private var mAccNumber = ""
    private var mAccName = ""

    private var mSplitBillDetailResponse: SplitBillDetailResponse? = null

    override fun setUrlDraftConfirm(urlDraftConfirm: String) {
        mUrlDraft = urlDraftConfirm
    }

    override fun setUrlGenerate(urlGenerate: String) {
        mUrlGenerate = urlGenerate
    }

    override fun setUrlHistoryBill(urlHistory: String) {
        mUrlHistoryBill = urlHistory
    }

    override fun parseDataFromHistory(splitBillDetailResponse: SplitBillDetailResponse) {
        if (splitBillDetailResponse != null &&
            splitBillDetailResponse.billModel != null &&
            splitBillDetailResponse.billModel.confirmation != null) {

            mSplitBillDetailResponse = splitBillDetailResponse
        }
    }

    override fun fetchData(confirmationSplitBillResponse: ConfirmationSplitBillResponse) {
        addedMembers = confirmationSplitBillResponse.members.map { members ->
            SplitBillAddMemberItemViewModel(
                id = members.id.toInt(),
                name = members.name,
                desc = members.desc,
                identifier = members.identifier,
                alias = members.userAlias,
                isNonBrimo = members.isBrimo,
                isPaid = members.isPaid,
                amountToPay = members.details.amounts,
                totalAmountPay = members.fmtAmount,
                products = members.details.items.map { products ->
                    SplitBillEditFormItemViewModel(
                        id = products.id,
                        name = products.name,
                        quantity = products.shared,
                        price = LifestyleHelper.convertStringtoNominal(products.fmtAmount).toLong()
                    )
                }
            )
        }.toMutableList()

        val splitBillModel = TempBillViewModel(
            billName = confirmationSplitBillResponse.header.name,
            billDate = confirmationSplitBillResponse.header.date,
            totalBill = LifestyleHelper.convertStringtoNominal(
                confirmationSplitBillResponse.header.fmtTotalAmt).toLong(),
            membersConfirmation = addedMembers
        )

        mListAccountModel = confirmationSplitBillResponse.accounts
        for (accountModel in mListAccountModel!!) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel!![0]
            }
        }

        mBillId = confirmationSplitBillResponse.billId

        view?.showSplitBillData(splitBillModel)
        view?.showDestinationAccount(model)
    }

    override fun updateDestinationAccount(accountNumber: String, accountName: String) {
        mAccNumber = accountNumber
        mAccName = accountName
    }

    private fun setRequest(): ConfirmationSplitBillRequest {
        val request = ConfirmationSplitBillRequest(
            billId = mBillId,
            accountNumber = mAccNumber,
            accountName = mAccName
        )

        return request
    }

    override fun getDraftConfirm() {
        if (mUrlDraft.isEmpty() || !isViewAttached) {
            return
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlDraft, setRequest(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        getView()?.onSuccessGetDraft()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        getView()?.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun getGenerateBill() {
        if (mUrlGenerate.isEmpty() || !isViewAttached) {
            return
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlGenerate, setRequest(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        val generatedBillResponse = response.getData(
                            GenerateBillResponse::class.java
                        )

                        getView()?.onSuccessGetGenerate(generatedBillResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        getView()?.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun onBackFromHistory() {
        mSplitBillDetailResponse?.let {
            view?.onBackFromHistory(it)
        }
    }

}
