package id.co.bri.brimons.presenters.general

import android.util.Log
import id.co.bri.brimons.contract.IPresenter.general.IGeneralConfirmationOpenPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.general.IGeneralConfirmationOpenView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.DbConfig

import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.*
import id.co.bri.brimons.models.apimodel.request.cashback.CashbackRevFilterFMRequest
import id.co.bri.brimons.models.apimodel.request.cashback.CashbackRevFilterRequest
import id.co.bri.brimons.models.apimodel.request.cashback.PilihCashbackFMRequest
import id.co.bri.brimons.models.apimodel.request.cashback.PilihCashbackRequest
import id.co.bri.brimons.models.apimodel.response.*
import id.co.bri.brimons.models.daomodel.Transaksi
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver
import io.reactivex.schedulers.Schedulers

class GeneralConfirmationOpenPresenter<V : IMvpView>(schedulerProvider: SchedulerProvider?,
                                                     compositeDisposable: CompositeDisposable?,
                                                     mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                                     categoryPfmSource: CategoryPfmSource?,
                                                     transaksiPfmSource: TransaksiPfmSource?,
                                                     anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IGeneralConfirmationOpenPresenter<V> where V : IGeneralConfirmationOpenView? {
    val TAG = "GeneralConfirmationOpenPresenter"
    lateinit var urlPayment: String
    var isGeneral = false
    private var idPayment: Long = 0
    private lateinit var paymentRequest: Any
    private lateinit var urlGetCashback: String
    private lateinit var cashbackRequest: Any
    private lateinit var selectCashbackUrl: String
    private lateinit var redeemCashbackRequest: Any

    override fun getDataPayment(
        pin: String,
        note: String,
        generalConfirmationResponse: GeneralConfirmationResponse,
        fromfastMenu: Boolean
    ) {
        if (urlPayment.isEmpty() && !isViewAttached) return

        if (isViewAttached) {
            //set flag Loading
            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            paymentRequest = if (fromfastMenu) FastPaymentRequest(
                getFastMenuRequest(),
                generalConfirmationResponse.referenceNumber,
                pin,
                generalConfirmationResponse.pfmCategory.toString(),
                note
            ) else if (!isGeneral) RencanaPaymentRequest(
                pin,
                generalConfirmationResponse.referenceNumber
            ) else PaymentRequest(
                generalConfirmationResponse.referenceNumber,
                pin,
                generalConfirmationResponse.pfmCategory.toString(),
                note
            )
            val disposable: Disposable =
                apiSource.getData(urlPayment, paymentRequest, seqNum) //function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()

                            //jika response data adalah response tarik tunai
                            try {
                                val brivaResponse = response.getData(PendingResponse::class.java)
                                if (isGeneral) {
                                    if (brivaResponse.immediatelyFlag) onSaveTransaksiPfm(
                                        generateTransaksiModel(
                                            generalConfirmationResponse.pfmCategory,
                                            generalConfirmationResponse.payAmount,
                                            generalConfirmationResponse.referenceNumber,
                                            generalConfirmationResponse.pfmDescription
                                        )
                                    )
                                }
                                getView()?.onSuccessGetPayment(brivaResponse)
                            } catch (e: Exception) {
                                onSaveTransaksiPfm(
                                    generateTransaksiModel(
                                        generalConfirmationResponse.pfmCategory,
                                        generalConfirmationResponse.payAmount,
                                        generalConfirmationResponse.referenceNumber,
                                        "Tarik Tunai"
                                    )
                                )
                                val responseTarik =
                                    response.getData(PaymentTarikResponse::class.java)
                                getView()?.onSuccesGetTarikTunai(responseTarik)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            when (restResponse.code.uppercase()) {
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onException93(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_01.value -> getView().onException01(restResponse.desc)
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun getDataPaymentRevamp(
        pin: String,
        generalConfirmationResponse: GeneralConfirmationResponse,
        accountNumber: String,
        fromfastMenu: Boolean
    ) {
        if (urlPayment.isEmpty() && !isViewAttached) return

        if (isViewAttached) {
            //set flag Loading
            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            paymentRequest = when {
                fromfastMenu -> FastPaymentOpenRevampRequest(
                    getFastMenuRequest(),
                    generalConfirmationResponse.referenceNumber,
                    pin,
                    accountNumber,
                    generalConfirmationResponse.pfmCategory.toString(),
                )

                !isGeneral -> RencanaPaymentRequest(
                    pin,
                    generalConfirmationResponse.referenceNumber
                )

                else -> PaymentRevampOpenRequest(
                    generalConfirmationResponse.referenceNumber,
                    pin,
                    accountNumber,
                    generalConfirmationResponse.pfmCategory.toString(),
                )
            }
            val disposable: Disposable =
                apiSource.getData(urlPayment, paymentRequest, seqNum) //function(param)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()

                            val receiptRevampResponse =
                                response.getData(ReceiptRevampResponse::class.java)
                            if (isGeneral) {
                                if (receiptRevampResponse.immediatelyFlag) onSaveTransaksiPfm(
                                    generateTransaksiModel(
                                        generalConfirmationResponse.pfmCategory,
                                        generalConfirmationResponse.payAmount,
                                        generalConfirmationResponse.referenceNumber,
                                        generalConfirmationResponse.pfmDescription
                                    )
                                )
                            }
                            getView()?.onSuccessGetPaymentRevamp(receiptRevampResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView()?.onException93(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_01.value -> getView()?.onException01(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value -> getView()?.onExceptionLimitExceed(
                                    restResponse.getData(GeneralResponse::class.java)
                                )
                                else -> getView()?.onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun urlPayment(url: String) {
        urlPayment = url
    }

    override fun isGeneral(general: Boolean) {
        isGeneral = general
    }

    override fun idPayment(idPayment: Long) {
        this.idPayment = idPayment
    }

    override fun generateTransaksiModel(
        kategoriId: Int,
        amount: Long,
        referenceNumber: String,
        billingName: String
    ): Transaksi? {
        var transaksi: Transaksi? = null
        try {
            transaksi = Transaksi(
                kategoriId.toLong(),
                1,
                billingName,
                "",
                DbConfig.TRX_OUT,
                brImoPrefRepository.user,
                amount,
                CalendarHelper.getCurrentDate(),
                CalendarHelper.getCurrentTime(),
                java.lang.Long.valueOf(referenceNumber),
                idPayment
            )
        } catch (e: java.lang.Exception) {
        }

        return transaksi
    }

    override fun onSaveTransaksiPfm(transaksi: Transaksi?) {
        if (transaksi != null) {
            compositeDisposable.add(
                transaksiPfmSource
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : DisposableSingleObserver<Long?>() {
                        override fun onSuccess(aLong: Long) {}
                        override fun onError(e: Throwable) {
                            Log.d("TAG", "onError: $e")
                        }
                    })
            )
        }
    }

    override fun setUrlGetCashback(url: String) {
        this.urlGetCashback = url
    }

    override fun getCashbackAll(
        accountNumber: String,
        referenceNumber: String,
        isFromFastMenu: Boolean
    ) {
        if (urlGetCashback == null || !isViewAttached) {
            return
        }

        cashbackRequest = if (isFromFastMenu) {
            CashbackRevFilterFMRequest(
                getFastMenuRequest(),
                refNum = referenceNumber,
                accountNumber = accountNumber
            )
        } else {
            CashbackRevFilterRequest(
                accountNumber = accountNumber,
                referenceNumber = referenceNumber
            )
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlGetCashback, cashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                            val responseData = response.getData(
                                ListAllCashbackFilterResponse::class.java
                            )
                            getView().onSuccessGetCashback(responseData)
                        } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) {
                            getView().isCashbackBlank(response.desc)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        getView()?.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun setRedeemCashbackUrl(url: String) {
        this.selectCashbackUrl = url
    }

    override fun getRedeemCashback(refNum: String, code: String, fromFastMenu: Boolean) {
        if (selectCashbackUrl == null || !isViewAttached) {
            return
        }

        redeemCashbackRequest = if (fromFastMenu) {
            PilihCashbackFMRequest(getFastMenuRequest(), refNum, code)
        } else {
            PilihCashbackRequest(refNum, code)
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(selectCashbackUrl, redeemCashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val selectCashbackResponse = response.getData(
                            SelectCashbackResponse::class.java
                        )
                        getView()?.onSuccessClearSelectedCashback(selectCashbackResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        when {
                            restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_12.value,
                                ignoreCase = true
                            ) -> getView()?.onException12(restResponse.desc)

                            else -> getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun start() {
        super.start()
        this.getDefaultSaldo()
    }

    fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view?.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }
}