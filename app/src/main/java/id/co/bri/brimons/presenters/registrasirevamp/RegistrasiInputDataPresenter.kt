package id.co.bri.brimons.presenters.registrasirevamp

import id.co.bri.brimons.contract.IPresenter.registrasirevamp.IRegistrasiInputDataPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.registrasirevamp.IRegistrasiInputDataView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.RegisSendDataRequest
import id.co.bri.brimons.models.apimodel.response.registrasi.RegisBCFUResponse
import id.co.bri.brimons.models.apimodel.response.registrasi.RegisOtpResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.registrasi.RegisMNVResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiInputDataPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiInputDataPresenter<V> where V : IMvpView, V : IRegistrasiInputDataView {

    private lateinit var url: String
    private val mnv = "MNV"

    override fun getDeviceId() {
        view.getRegisId(brImoPrefRepository.deviceId)
    }

    override fun setUrlData(urlData: String) {
        url = urlData
    }

    override fun sendDataRegis(regisSendDataRequest: RegisSendDataRequest) {
        if (url.isNotEmpty() && isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(url, regisSendDataRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val regisOtpResponse =
                                    response.getData(RegisOtpResponse::class.java)
                                if (regisOtpResponse.method == mnv) {
                                    val regisMNVResponse =
                                        response.getData(RegisMNVResponse::class.java)
                                    getView().onSuccessGetMNV(regisMNVResponse)
                                } else
                                    getView().onSuccessGetOTP(regisOtpResponse)
                            } else {
                                val regisBCFUResponse =
                                    response.getData(RegisBCFUResponse::class.java)
                                getView().onSuccessBCFU(regisBCFUResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code == RestResponse.ResponseCodeEnum.RC_58.value)
                                getView().onExceptionCX()
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else
                                getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

}