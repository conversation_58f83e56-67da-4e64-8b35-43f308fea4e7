package id.co.bri.brimons.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.simpedes.IInfoSimpedesPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IInfoSimpedesView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.ImpianForm;
import id.co.bri.brimons.models.apimodel.request.AccountRequest;
import id.co.bri.brimons.models.apimodel.request.ProductListAmkkmRequest;
import id.co.bri.brimons.models.apimodel.response.BrifineCheckRes;
import id.co.bri.brimons.models.apimodel.response.BrifineFormRes;
import id.co.bri.brimons.models.apimodel.response.InfoSimpedesResponse;
import id.co.bri.brimons.models.apimodel.response.JenisAsuransiAmkkmResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoSimpedesPresenter<V extends IMvpView & IInfoSimpedesView>
        extends MvpPresenter<V> implements IInfoSimpedesPresenter<V> {

    protected String urlDetail;
    protected String urlAddImpian;
    protected String urlAddBrifine;
    protected String urlBrifineCheck;
    protected String urlProductList;

    public InfoSimpedesPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                 BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                 TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlFormInfo(String urlDetail) {
        this.urlDetail = urlDetail;
    }

    @Override
    public void setUrlAddImpian(String urlAddImpian) {
        this.urlAddImpian = urlAddImpian;
    }

    @Override
    public void setUrlAddBrifine(String urlAddBrifine) {
        this.urlAddBrifine = urlAddBrifine;
    }

    @Override
    public void setUrlBrifineCheck(String urlBrifineCheck) {
        this.urlBrifineCheck = urlBrifineCheck;
    }

    @Override
    public void setUrlProductAmkkm(String urlProductAmkkm) {
        this.urlProductList = urlProductAmkkm;
    }


    @Override
    public void getImpian(String sAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            AccountRequest accountRequest = new AccountRequest(sAccount);

            getView().viewSwipeRefresh(false);
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlDetail, accountRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    InfoSimpedesResponse infoSimpedesResponse = response.getData(InfoSimpedesResponse.class);
                                    getView().getDataInfo(infoSimpedesResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                        getView().onExceptionImpian(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }

                                @Override
                                public void onComplete() {
                                    getView().completeImpian();
                                    super.onComplete();

                                }
                            }));

        }
    }

    @Override
    public void getAddImpian(String sAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            AccountRequest accountRequest = new AccountRequest(sAccount);

            String seq = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlAddImpian, accountRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ImpianForm impianForm = response.getData(ImpianForm.class);
                                    getView().getDataImpianForm(impianForm);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getAddBrifine(String sAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            AccountRequest accountRequest = new AccountRequest(sAccount);

            String seq = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlAddBrifine, accountRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    BrifineFormRes brifineFormRes = response.getData(BrifineFormRes.class);
                                    getView().getDataBrifineForm(brifineFormRes);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getBrifineCheck(String sAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            AccountRequest accountRequest = new AccountRequest(sAccount);

            getView().viewSwipeRefresh(false);
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlBrifineCheck, accountRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    BrifineCheckRes brifineCheckRes = response.getData(BrifineCheckRes.class);
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        getView().getDataBrifineCheck00(brifineCheckRes);
                                    } else if (response.getCode().equalsIgnoreCase("NF")) {
                                        getView().getDataBrifineCheckNF(brifineCheckRes);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                        getView().onExceptionBrifine(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }

                                @Override
                                public void onComplete() {
                                    getView().completeBrifine();
                                    super.onComplete();
                                }
                            }));
        }
    }

    @Override
    public void getProductAmkkm(String sAccount) {
        ProductListAmkkmRequest productListAmkkmRequest = new ProductListAmkkmRequest(sAccount);

        getView().viewSwipeRefresh(false);
        String seq = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getData(urlProductList, productListAmkkmRequest, seq)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seq) {
                    @Override
                    protected void onFailureHttp(String type) {
                        getView().onException(type);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        JenisAsuransiAmkkmResponse jenisAsuransiAmkkmResponse = response.getData(JenisAsuransiAmkkmResponse.class);
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue()))
                            getView().getProductAmkkm(jenisAsuransiAmkkmResponse);
                        else if (response.getCode().equalsIgnoreCase("NF"))
                            getView().getProductAmkkmNF(jenisAsuransiAmkkmResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                            getView().onExceptionAmkkm(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }

                    @Override
                    public void onComplete() {
                        getView().completeAmkkm();
                        super.onComplete();
                    }
                }));
    }

    @Override
    public void stop() {
        super.stop();
    }
}