package id.co.bri.brimons.presenters.pfmPickCategory;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.pickCategory.IPickPaymentPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.pickCategory.IPickPaymentView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.daomodel.Category;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class PickPaymentFragmentPresenter<V extends IMvpView & IPickPaymentView> extends MvpPresenter<V> implements IPickPaymentPresenter<V> {

    private static final String TAG = "PickPaymentFragmentPres";
    private CategoryPfmSource mCategoryPfmSource;

    @Inject
    public PickPaymentFragmentPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        mCategoryPfmSource = categoryPfmSource;
    }

    @Override
    public void loadDataPayment() {
        getCompositeDisposable().add(mCategoryPfmSource.getCategoryPembayaran()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<Category>>() {
                    @Override
                    public void accept(List<Category> categories) throws Exception {
                        getView().onLoadData(categories);
                        Log.d(TAG, "accept: done");
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        getView().onException(throwable.getMessage().toString());
                    }
                }));
    }

    @Override
    public void start() {
        super.start();
        loadDataPayment();
    }
}
