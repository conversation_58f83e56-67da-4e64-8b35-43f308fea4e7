package id.co.bri.brimons.presenters.britamarencanarevamp

import id.co.bri.brimons.contract.IPresenter.britamarencanarevamp.IPilihTabunganRevPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.britamarencanarevamp.IPilihTabunganRevView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimons.models.apimodel.request.bukarekening.InquiryOpenAccountRequest
import id.co.bri.brimons.models.apimodel.response.onExceptionWH
import id.co.bri.brimons.models.apimodel.response.bukarekening.JenisTabunganResponse
import id.co.bri.brimons.models.apimodel.response.QuestionResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.bukarekening.FormValasResponse
import id.co.bri.brimons.models.apimodel.response.bukarekening.InquiryOpenAccJunioResponse
import id.co.bri.brimons.models.apimodel.response.bukarekening.InquiryOpenAccResponse
import id.co.bri.brimons.models.apimodel.response.rencanarev.InquiryRencanaResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit


class PilihTabunganRevPresenter<V>(schedulerProvider: SchedulerProvider,
                                   compositeDisposable: CompositeDisposable,
                                   mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                   categoryPfmSource: CategoryPfmSource,
                                   transaksiPfmSource: TransaksiPfmSource,
                                   anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IPilihTabunganRevPresenter<V> where V : IMvpView, V : IPilihTabunganRevView {

    var mUrl: String? = null
    private var mUrlS3f: String? = null
    private var jenisTabunganResponse = JenisTabunganResponse()
    private var inquiryGeneralOpenAccountResponse = InquiryOpenAccResponse()
    private var inquiryJunioOpenAccountResponse = InquiryOpenAccJunioResponse()
    private var formValasResponse = FormValasResponse()
    private var inquiryOpenRencanaRevResponse = InquiryRencanaResponse()
    var mUrlInquiry: String? = null
    private var mUrlSafety: String? = null
    private var mUrlFormValas: String? = null

    override fun setUrl(url: String) {
        mUrl = url
    }

    override fun setUrlInquiry(url: String) {
        mUrlInquiry = url
    }

    //Integrasi Data Product Dari BE
    override fun getJenisTabungan() {
        if (isViewAttached) {
            view.showSkeleton()
            view.disableButton(true)
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataForm(mUrl, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            jenisTabunganResponse = response.getData(
                                JenisTabunganResponse::class.java
                            )
                            getView().hideSkeleton()
                            getView().disableButton(false)
                            getView().onSuccessGetData(jenisTabunganResponse)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().disableButton(true)
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getInquiryTabungan(request: InquiryOpenAccountRequest) {
        if (isViewAttached) {
            //sho loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlInquiry, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {

                            getView().hideProgress()

                            if (response.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SUCCESS.value,
                                    ignoreCase = true
                                )
                            ) {
                                inquiryGeneralOpenAccountResponse = response.getData(
                                    InquiryOpenAccResponse::class.java
                                )
                                getView().onSuccessInquiry(inquiryGeneralOpenAccountResponse)
                            } else if (response.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_03.value,
                                    ignoreCase = true
                                )
                            ) {
                                val onExceptionWH = response.getData(onExceptionWH::class.java)
                                getView().onException03(onExceptionWH)
                            }

                            if (!GeneralHelper.isProd()) {
                                GeneralHelper.responseChuck(response)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlSafety(url: String) {
        mUrlSafety = url
    }

    override fun getPusatBantuanSafety(id: String) {
        if (mUrlSafety == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(id)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlSafety, safetyPusatBantuanRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val topicQuestionResponse = response.getData(
                                QuestionResponse::class.java
                            )
                            if (mUrlSafety != null) getView()!!.onSuccessPusatBantuanSafety(
                                topicQuestionResponse
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView().onSessionEnd(restResponse.desc) else getView()!!.onException(
                                restResponse.desc
                            )
                        }
                    })
            )

    }

    override fun getFormTabunganValas(request: InquiryOpenAccountRequest) {
        if (isViewAttached) {
            //show loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlFormValas, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()

                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                formValasResponse = response.getData(
                                    FormValasResponse::class.java
                                )
                                getView().onSuccesFormValas(formValasResponse)
                            } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_03.value, ignoreCase = true)) {
                                val onExceptionWH = response.getData(onExceptionWH::class.java)
                                getView().onException03(onExceptionWH)
                            }
                            else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                val onExceptionWH = response.getData(onExceptionWH::class.java)
                                getView().onException02(onExceptionWH)
                            }


                            if (!GeneralHelper.isProd()) {
                                GeneralHelper.responseChuck(response)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlFormValas(url: String) {
        mUrlFormValas = url
    }


    override fun getInquiryTabunganJunio(request: InquiryOpenAccountRequest) {
        if (isViewAttached) {
            //sho loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlInquiry, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        inquiryJunioOpenAccountResponse = response.getData(
                            InquiryOpenAccJunioResponse::class.java
                        )
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            getView().onSuccessJunioInquiry(inquiryJunioOpenAccountResponse)
                        }
                        else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_03.value, ignoreCase = true)) {
                            val onExceptionWH = response.getData(onExceptionWH::class.java)
                            getView().onException03(onExceptionWH)
                        }

                            if (!GeneralHelper.isProd()) {
                                GeneralHelper.responseChuck(response)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getInquiryRencana(rencana: InquiryOpenAccountRequest) {
        if (isViewAttached) {
            //sho loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable
                .add(
                    apiSource.getData(mUrlInquiry, rencana, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                //TO-DO onSuccess
                                getView().hideProgress()
                                inquiryOpenRencanaRevResponse =
                                    response.getData(InquiryRencanaResponse::class.java)
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                    getView().onSuccessInquiryRencana(
                                        inquiryOpenRencanaRevResponse
                                    )
                                } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_03.value)) {
                                    val onExceptionWH =
                                        response.getData(onExceptionWH::class.java)
                                    getView().onException03(onExceptionWH)
                                }
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                        ignoreCase = true
                                    )
                                ) getView().onSessionEnd(restResponse.desc) else getView()!!.onException(
                                    restResponse.desc
                                )
                            }
                        })
                )
        }
    }
}