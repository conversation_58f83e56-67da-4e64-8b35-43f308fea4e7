package id.co.bri.brimons.presenters.dashboard

import id.co.bri.brimons.contract.IPresenter.dashboard.IDashboardInvestPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dashboard.IDashboardInvestView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dashboardInvestasi.*
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DashboardInvesPresenter <V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDashboardInvestPresenter<V> where V : IMvpView, V : IDashboardInvestView {


    lateinit var mUrlFaq :String
    lateinit var mUrlPromo :String
    lateinit var mUrlContent :String
    lateinit var mUrlRecommendation :String
    lateinit var mUrlMenu :String
    override fun setUrlInvestasiFaq(url: String) {
        mUrlFaq = url
    }

    override fun setUrlInvestasiPromo(url: String) {
        mUrlPromo = url
    }

    override fun setUrlInvestasiContent(url: String) {
        mUrlContent = url
    }

    override fun setUrlInvestasiRecomendation(url: String) {
        mUrlRecommendation = url
    }

    override fun setUrlInvestasiMenu(url: String) {
        mUrlMenu = url
    }

    override fun getDataInvestasiFaq() {
        if (mUrlFaq.isEmpty() && !isViewAttached) return

        view.showSekelton(true)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlFaq, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().showSekelton(false)
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().showSekelton(false)
                                        val mResponse = response.getData(
                                                InvenstasiFaqResponse::class.java
                                        )
                                        getView().onSuccessInvestasiFaq(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().showSekelton(false)
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiPromo() {
        if (mUrlPromo.isEmpty() && !isViewAttached) return

        view.showSekelton(true)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlPromo, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().showSekelton(false)
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().showSekelton(false)
                                        val mResponse = response.getData(
                                                BannerResponse::class.java
                                        )
                                        getView().onSuccessInvestasiPromo(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().showSekelton(false)
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiContent() {
        if (mUrlContent.isEmpty() && !isViewAttached) return

        view.showSekelton(true)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlContent, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().showSekelton(false)
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().showSekelton(false)
                                        val mResponse = response.getData(
                                                ContentResponse::class.java
                                        )
                                        getView().onSuccessInvestasiContent(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().showSekelton(false)
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiRecommedantion() {
        if (mUrlRecommendation.isEmpty() && !isViewAttached) return

        view.showSekelton(true)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlRecommendation, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().showSekelton(false)
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().showSekelton(false)
                                        val mResponse = response.getData(
                                                RecomendationResponse::class.java
                                        )
                                        getView().onSuccessInvestasiRecommendation(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().showSekelton(false)
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiMenu() {
        if (mUrlMenu.isEmpty() && !isViewAttached) return

        view.showSekelton(true)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlMenu, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().showSekelton(false)
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().showSekelton(false)
                                        val mResponse = response.getData(
                                                MenuInvestasiResponse::class.java
                                        )
                                        getView().onSuccessInvestasiMenu(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().showSekelton(false)
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }
}