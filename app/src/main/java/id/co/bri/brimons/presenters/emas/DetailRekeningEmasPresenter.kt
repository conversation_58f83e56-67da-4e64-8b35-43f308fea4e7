package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IDetailRekeningEmasPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IDetailRekeningEmasView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.SourceAccountRequest
import id.co.bri.brimons.models.apimodel.request.emas.ToggleRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.emas.DetailEmasResponse
import id.co.bri.brimons.models.apimodel.response.emas.EditSetoranEmasResponse
import id.co.bri.brimons.models.apimodel.response.emas.ToggleResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailRekeningEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                     compositeDisposable: CompositeDisposable?,
                                     mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                     categoryPfmSource: CategoryPfmSource?,
                                     transaksiPfmSource: TransaksiPfmSource?,
                                     anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDetailRekeningEmasPresenter<V> where V : IMvpView?, V : IDetailRekeningEmasView{

    var urlEditSetoranEmas : String? = null
    private var urlDetailRekening : String? = null
    private var urlToggle : String? = null

    override fun setUrlEditSetoran(url: String) {
        urlEditSetoranEmas = url
    }

    override fun getDataEditSetoran(sourceAccount: String) {

        view!!.showProgress()
        val sourceAccountRequest = SourceAccountRequest(sourceAccount)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(apiSource.getData(urlEditSetoranEmas, sourceAccountRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()!!.hideProgress()
                                getView()!!.onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                //TO-DO onSuccess
                                getView()!!.hideProgress()
                                val editSetoranEmasResponse = response.getData(
                                        EditSetoranEmasResponse::class.java
                                )
                                getView().onSuccessEditSetoranEmas(editSetoranEmasResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView()!!.hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                        restResponse.desc
                                )
                            }
                        })
                )
    }

    override fun setUrlDetailEmas(url: String) {
        urlDetailRekening = url
    }

    override fun getDetailEmas() {
        if (urlDetailRekening == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(urlDetailRekening, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()!!.hideProgress()
                                        getView()!!.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()!!.hideProgress()

                                        val detailEmasResponse = response.getData(DetailEmasResponse::class.java)
                                        getView()!!.onSuccessDetailEmas(detailEmasResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()!!.hideProgress()
                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun setUrlToggleData(url: String) {
        urlToggle = url
    }

    override fun getToggleData(request: ToggleRequest) {
        if (urlToggle == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getData(urlToggle,request, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()!!.hideProgress()
                                        getView()!!.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()!!.hideProgress()

                                        val toggleResponse = response.getData(ToggleResponse::class.java)
                                        getView()!!.onSuccessToggle(toggleResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()!!.hideProgress()
                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onExceptionDetail(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }
}