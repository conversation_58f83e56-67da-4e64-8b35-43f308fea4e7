package id.co.bri.brimons.presenters.receipt;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.receipt.IReceiptAbnormalRevampPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.receipt.IReceiptAbnormalRevampView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.StatusRequest;
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class ReceiptAbnormalRevampPresenter <V extends IMvpView & IBaseFormView & IReceiptAbnormalRevampView> extends BaseFormPresenter<V> implements IReceiptAbnormalRevampPresenter<V> {

    protected String urlDetailInbox;
    private StatusRequest statusRequest;
    private ReceiptRevampInboxResponse receiptResponse;
    protected boolean isLoadingItem = false;

    public ReceiptAbnormalRevampPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }


    @Override
    public void getInboxDetail(String refnumber) {
        if (isLoadingItem) {
            return;
        } else {

            if (isViewAttached()) {
                isLoadingItem = true;
                getView().showProgress();

                statusRequest = new StatusRequest(refnumber);
                String seqNum = getBRImoPrefRepository().getSeqNumber();

                getCompositeDisposable().add(
                        getApiSource().getData(urlDetailInbox, statusRequest, seqNum)//function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .subscribeOn(getSchedulerProvider().single())
                                .observeOn(getSchedulerProvider().mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {

                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        isLoadingItem = false;
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        receiptResponse = response.getData(ReceiptRevampInboxResponse.class);
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        //TO-DO onSuccess
                                        getView().onSuccessGetInboxDetail(receiptResponse);
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                            getView().onSessionEnd(restResponse.getDesc());
                                        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                            // do nothing
                                        }else{
                                            getView().onException(restResponse.getDesc());
                                        }
                                    }
                                })
                );
            }
        }
    }

    @Override
    public void initBackroundWatermark() {
        getView().onResultLanguage(getBRImoPrefRepository().getLanguange().equalsIgnoreCase(Constant.LANGUAGE_ENGLISH));
    }

    @Override
    public void setUrlDetailInbox(String urlDetailInbox) {
        this.urlDetailInbox = urlDetailInbox;
    }

}