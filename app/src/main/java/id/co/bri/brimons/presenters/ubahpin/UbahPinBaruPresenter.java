package id.co.bri.brimons.presenters.ubahpin;

import javax.inject.Inject;

import id.co.bri.brimons.contract.IPresenter.ubahpin.IUbahPinBaruPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.ubahpin.IUbahPinBaruView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class UbahPinBaruPresenter<V extends IMvpView & IUbahPinBaruView> extends MvpPresenter<V> implements IUbahPinBaruPresenter<V> {

    private String url = "";

    private String pin = "";

    @Inject
    public UbahPinBaruPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void ubahPin() {
        if (getView() == null) {
            return;
        }
    }

    @Override
    public void setPin(String pin) {
        this.pin = pin;
    }

}
