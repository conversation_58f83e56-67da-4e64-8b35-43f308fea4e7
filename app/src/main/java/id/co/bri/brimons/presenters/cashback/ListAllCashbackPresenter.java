package id.co.bri.brimons.presenters.cashback;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import id.co.bri.brimons.contract.IPresenter.cashback.ICashbackAllPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.cashback.ICashbackAllView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.lifestyle.MenuLifestyleSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.converter.MapperHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.cashback.PilihCashbackFMRequest;
import id.co.bri.brimons.models.apimodel.request.cashback.PilihCashbackRequest;
import id.co.bri.brimons.models.apimodel.response.SelectCashbackResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse;
import id.co.bri.brimons.models.apimodel.response.lifestyle.FeatureDataView;
import id.co.bri.brimons.models.daomodel.lifestyle.MenuLifestyle;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.observers.DisposableMaybeObserver;

public class ListAllCashbackPresenter<V extends IMvpView & ICashbackAllView> extends MvpPresenter<V> implements ICashbackAllPresenter<V> {

    private String selectCashbackUrl;
    protected Object redeemCashbackRequest = null;
    private MenuLifestyleSource menuLifestyleSource;
    private DashboardLifestyleMenuResponse lifestyleMenuResponse;

    public ListAllCashbackPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, MenuLifestyleSource menuLifestyleSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.menuLifestyleSource = menuLifestyleSource;
    }

    @Override
    public void setRedeemCashbackUrl(String url) {
        selectCashbackUrl = url;
    }

    @Override
    public void getRedeemCashback(String refNum, String code, boolean isFromFastMenu) {
        if (selectCashbackUrl == null || !isViewAttached()) {
            return;
        }

        if (isFromFastMenu) {
            redeemCashbackRequest = new PilihCashbackFMRequest(getFastMenuRequest(), refNum, code);
        } else {
            redeemCashbackRequest = new PilihCashbackRequest(refNum, code);
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(selectCashbackUrl, redeemCashbackRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                SelectCashbackResponse selectCashbackResponse = response.getData(SelectCashbackResponse.class);
                                getView().onSuccessSelectCashback(selectCashbackResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getDataDashboardLifestyleMenu(String url) {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getDataTanpaRequest(url, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            lifestyleMenuResponse =
                                    response.getData(DashboardLifestyleMenuResponse.class);

                            String parentfeatureCode = "";
                            String subfeatureCode = "";
                            String updateParentFeature = "";
                            String updateSubFeature = "";
                            boolean parentIsNew = false;
                            boolean subIsNew = false;

                            List<FeatureDataView> featureDataLocals = new ArrayList<>();
                            for (int menuData = 0; menuData < lifestyleMenuResponse.getMenuDataView().size();
                                 menuData++) {

                                featureDataLocals.addAll(
                                        lifestyleMenuResponse.getMenuDataView().get(menuData).getFeature());

                                for (int parentMenu = 0; parentMenu < lifestyleMenuResponse.getMenuDataView()
                                        .get(menuData).getFeature().size(); parentMenu++) {

                                    parentfeatureCode = lifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getFeatureCode();
                                    parentIsNew = lifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).isNew();
                                    updateParentFeature = lifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getUpdatedDate();

                                    //to check if parent menu not have submenu and isNew true then set isNew to false
                                    checkMenuLifestyle(parentfeatureCode);

                                    if (lifestyleMenuResponse.getMenuDataView().get(menuData)
                                            .getFeature().get(parentMenu).getSubFeature() != null &&
                                            !lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu).getSubFeature().isEmpty()
                                    ) {

                                        for (int subMenu = 0; subMenu < lifestyleMenuResponse.getMenuDataView()
                                                .get(menuData).getFeature().get(parentMenu).getSubFeature().size(); subMenu++) {

                                            if (subMenu != 0) {
                                                featureDataLocals.addAll(
                                                        lifestyleMenuResponse.getMenuDataView()
                                                                .get(menuData).getFeature().get(parentMenu).getSubFeature()
                                                );
                                            }

                                            updateSubFeature = lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).getUpdatedDate();

                                            subfeatureCode = lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).getFeatureCode();

                                            subIsNew = lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).isNew();
                                        }
                                    }

                                }
                            }

                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                if (Objects.equals(getBRImoPrefRepository().getDateMenuUpdate(), "")) {
                                    saveMenuLifestyle(featureDataLocals);
                                } else if (Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(
                                                lifestyleMenuResponse.getUpdatedDate()))) {
                                    saveMenuLifestyle(featureDataLocals);
                                }
                                else if (Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(updateParentFeature))) {
                                    updateFlagNewLifestyle(parentfeatureCode, parentIsNew ? 1 : 0);
                                }
                                else if (updateSubFeature != null &&
                                        !updateSubFeature.isEmpty() &&
                                        Boolean.TRUE == CalendarHelper.compareDate(
                                                getBRImoPrefRepository().getDateMenuUpdate(),
                                                CalendarHelper.stringDateTimeToddMMyyyy(updateSubFeature))) {
                                    updateFlagNewLifestyle(subfeatureCode, subIsNew ? 1 : 0);
                                }
                                else {
                                    getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                                }
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("05"))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase("99"))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    private void saveMenuLifestyle(List<FeatureDataView> featureDataViews) {
        getCompositeDisposable().add(menuLifestyleSource
                .insertMenuLifestyle(MapperHelper.dashboardLifestyleModelConverter(featureDataViews))
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        lifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    private void updateFlagNewLifestyle(String featureCode, int isNew) {
        getCompositeDisposable().add(menuLifestyleSource.
                updateMenuLifestyle(featureCode, isNew)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        lifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                })
        );
    }

    public void checkMenuLifestyle(String parentfeatureCode) {
        if (isViewAttached()) {
            getCompositeDisposable().add(menuLifestyleSource.getNewMenubyParentCode(parentfeatureCode)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableMaybeObserver<List<MenuLifestyle>>() {
                        @Override
                        public void onSuccess(List<MenuLifestyle> menuLifestyles) {
                            if (menuLifestyles.size() == 1) {
                                updateFlagNewLifestyle(parentfeatureCode, 0);
                            }
                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    }));
        }
    }

}