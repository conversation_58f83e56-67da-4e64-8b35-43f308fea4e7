package id.co.bri.brimons.presenters.lifestyle.ekspedisi

import id.co.bri.brimons.contract.IPresenter.lifestyle.ekspedisi.IKonfirmasiWebviewEkspedisiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.lifestyle.ekspedisi.IKonfirmasiWebviewEkspedisiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimons.models.apimodel.response.GeneralResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class KonfirmasiWebviewEkspedisiPresenter <V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IKonfirmasiWebviewEkspedisiPresenter<V> where V : IMvpView, V : IKonfirmasiWebviewEkspedisiView {

    lateinit var mUrlPay: String
    private lateinit var paymentRequest: Any

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold

        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun setUrlPayment(url: String) {
        mUrlPay = url
    }

    override fun getDataPaymentRevamp(
        pin: String, konfirmasiWebviewEkspedisiResponse: KonfirmasiWebviewEkspedisiResponse,
        account: String, saveAs: String, note: String, fromFastMenu: Boolean
    ) {
        if (mUrlPay.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        paymentRequest = PayBrivaRevampRequest(
            konfirmasiWebviewEkspedisiResponse.referenceNumber,
            account, konfirmasiWebviewEkspedisiResponse.amount.toString(), saveAs,
            note, pin, konfirmasiWebviewEkspedisiResponse.pfmCategory.toString()
        )

        val disposable: Disposable =
            apiSource.getData(mUrlPay, paymentRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val receiptRevampResponse =
                            response.getData(ReceiptRevampResponse::class.java)

                        getView().onSuccessGetPaymentRevamp(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals("93")) getView().onExceptionTrxExpired(
                            restResponse.desc
                        )
                        else if (restResponse.code.equals("01")) getView().onException01(
                            restResponse.desc
                        )
                        else if (restResponse.code == "94") getView().onExceptionSnackbarBack(
                            restResponse.desc
                        )
                        else if (restResponse.code == "61")
                            getView().onExceptionLimitExceed(restResponse.getData(
                            GeneralResponse::class.java))
                        else getView().onException(restResponse.desc)
                    }
                })

        compositeDisposable.add(disposable)
    }

}