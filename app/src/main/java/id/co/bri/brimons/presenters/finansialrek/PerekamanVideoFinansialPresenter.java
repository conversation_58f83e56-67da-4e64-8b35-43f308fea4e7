package id.co.bri.brimons.presenters.finansialrek;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.finansialrek.IPerekamanVideoFinansialPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.finansialrek.IPerekamanVideoFinansialView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.VideoFinansialRequest;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class PerekamanVideoFinansialPresenter<V extends IMvpView & IPerekamanVideoFinansialView>
        extends MvpPresenter<V> implements IPerekamanVideoFinansialPresenter<V> {

    protected String url;

    public PerekamanVideoFinansialPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                            BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                            TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    public void getDeviceId() {
        getView().onDeviceId(getBRImoPrefRepository().getDeviceId());
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void sendVideoData(VideoFinansialRequest request) {
        if (url != null && isViewAttached()) {
            String seq = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(url, request, seq)
                    .timeout(AppConfig.TIMEOUT_CONNECT_ONBOARDING, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seq) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            PendingResponse pendingResponse = response.getData(PendingResponse.class);
                            getView().onSuccessData(pendingResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onExceptionCanceled(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }
}