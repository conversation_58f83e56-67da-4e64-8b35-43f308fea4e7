package id.co.bri.brimons.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingCheckPointPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingCheckPointView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.StatusResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.TabunganResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingCheckPointPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingCheckPointPresenter<V> where V : IMvpView, V : IOnboardingCheckPointView {

    private var urlReset: String = ""
    private var urlProgress: String = ""

    override fun setUrlReset(url: String) {
        urlReset = url
    }

    override fun setUrlProgress(url: String) {
        urlProgress = url
    }

    override fun sendDataReset() {
        if (urlReset.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val onboardIdRequest = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlReset, onboardIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val tabunganResponse = response.getData(TabunganResponse::class.java)
                        getView().onResetView(tabunganResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getProgressOnboarding(progressBack: Boolean) {
        if (urlProgress.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val onboardIdRequest = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlProgress, onboardIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val progressResponse =
                            response.getData(StatusResponse::class.java)

                        when (response.code) {
                            RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> {
                                if (progressBack) {
                                    getView().onCheckPointBack(
                                        Gson().toJson(response.data),
                                        progressResponse
                                    )
                                } else {
                                    getView().onCheckPointView(
                                        Gson().toJson(response.data),
                                        progressResponse
                                    )
                                }
                            }

                            RestResponse.ResponseCodeEnum.RC_01.value -> {
                                getView().onExceptionRevamp(Constant.TRANSAKSI_GAGAL)
                            }

                            RestResponse.ResponseCodeEnum.RC_02.value -> {
                                val forceUpdateRes = response.getData(ForceUpdateResponse::class.java)
                                getView().onUpdateVersion(forceUpdateRes)
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}