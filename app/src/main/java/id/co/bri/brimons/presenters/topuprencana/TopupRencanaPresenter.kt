package id.co.bri.brimons.presenters.topuprencana

import android.content.Context
import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.topuprencana.ITopUpRencanaPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.topuprencana.ITopUpRencanaView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.topuprencana.PostTopupPlanRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

open class TopupRencanaPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ITopUpRencanaPresenter<V> where V : IMvpView?, V : ITopUpRencanaView {

    private var urlTopupRencanaConfirmation = ""

    override fun getSaldoRekeningUtama(): String {
        return brImoPrefRepository.saldoRekeningUtama
    }

    override fun setUrlTopupRencanaConfirmation(url: String) {
        this.urlTopupRencanaConfirmation = url
    }

    override fun postTopupRencanaRequest(context: Context, postTopupPlanRequest: PostTopupPlanRequest) {
        if (urlTopupRencanaConfirmation.isEmpty() || !isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            urlTopupRencanaConfirmation,
            postTopupPlanRequest,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView().apply {
                        hideProgress()
                        onException(message)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView().apply {
                        hideProgress()
                        val responseData = response?.getData(GeneralConfirmationResponse::class.java)
                        responseData?.let { getView().onSuccessResponse(it) }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView().apply {
                            hideProgress()
                            when (response.code) {
                                context.getString(R.string.txt_error_code_05) -> this.onSessionEnd(response.desc.orEmpty())
                                context.getString(R.string.txt_error_code_93) -> this.onExceptionCode93(response.restResponse.desc)
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    private fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view?.setDefaultSaldo(saldo, saldoString, defaultAcc,saldoHold)
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }
}