package id.co.bri.brimons.presenters.lupapassword;

import id.co.bri.brimons.contract.IPresenter.lupapassword.IWaitingLupaPasswordPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.lupapassword.IWaitingLupaPasswordView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;
import id.co.bri.brimons.models.apimodel.response.MagicLupaPassResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.forgetuserpass.OtpEmailRes;
import id.co.bri.brimons.presenters.MvpPresenter;
import id.co.bri.brimons.security.IMyEncrypt;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class WaitingLupaPasswordPresenter<V extends IMvpView & IWaitingLupaPasswordView>
        extends MvpPresenter<V> implements IWaitingLupaPasswordPresenter<V> {

    protected String urlSend;
    protected String urlResend;
    protected IMyEncrypt myEncrypt;

    public WaitingLupaPasswordPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                        BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                        TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, IMyEncrypt myEncrypt) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.myEncrypt = myEncrypt;
    }

    @Override
    public void setUrlSend(String urlSend) {
        this.urlSend = urlSend;
    }

    @Override
    public void setUrlResend(String urlResend) {
        this.urlResend = urlResend;
    }

    @Override
    public void confirmWaiting(ValidateOtpUserPassReq validateRequest) {
        if (urlSend.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seq = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getData(urlSend, validateRequest, seq)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seq) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        OtpEmailRes otpResponse = response.getData(OtpEmailRes.class);
                        getView().onSuccess(otpResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        switch (restResponse.getCode()) {
                            case Constant.RE_TRX_EXPIRED:
                                getView().onExceptionTrxExpired(restResponse.getDesc());
                                break;
                            case Constant.RE12:
                                getView().onException12(restResponse.getDesc());
                                break;
                            default:
                                getView().onException(restResponse.getDesc());
                                break;
                        }
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void resendWaiting(ResendOtpReissueReq resendOtpRequest) {
        if (urlResend.isEmpty() || !isViewAttached()) return;
        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getData(urlResend, resendOtpRequest, seqNum)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        getView().setDisableClick(true);

                        MagicLupaPassResponse resendResponse = response.getData(MagicLupaPassResponse.class);
                        getView().onResendOtp(resendResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        getView().onException12(restResponse.getDesc());

                    }
                });

        getCompositeDisposable().add(disposable);
    }
}