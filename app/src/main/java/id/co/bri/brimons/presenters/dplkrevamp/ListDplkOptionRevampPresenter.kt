package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IListDplkOptionRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IListPilihBrifineRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.InquiryDplkRegisRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.ProductFtuDplkRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.SimulasiDplkRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.FormPilihBrifineResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.ProductFtuDplkResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.SimulasiDplkResponse
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.fromObjectToJson
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class ListDplkOptionRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
),
    IListDplkOptionRevampPresenter<V> where V : IMvpView, V : IListPilihBrifineRevampView {

    private lateinit var responseHeader: ProductFtuDplkResponse
    private lateinit var responseSimulasi: SimulasiDplkResponse
    private lateinit var responseGraphic: DetailPerformanceTabDplkResponse
    private lateinit var requestProductDetail: ProductFtuDplkRequest
    private lateinit var requestSimulasiDetail: SimulasiDplkRequest


    private var urlSimulasi: String = ""
    private var mUrlInquiryDplk: String = ""
    private var mUrlProductFtuDplk: String = ""
    private var mUrlDetailPerformanceTab: String = ""

    private var mIsCombination = false

    override fun setUrlSimulasiDplk(urlSimulasiDplk: String) {
        this.urlSimulasi = urlSimulasiDplk
    }

    override fun setUrlInquiryDplk(urlInquryDplk: String) {
        mUrlInquiryDplk = urlInquryDplk
    }

    override fun setUrlDetailPerformanceTabDplk(urlDetailPerformanceTab: String) {
        mUrlDetailPerformanceTab = urlDetailPerformanceTab
    }

    override fun setUrlProductFtuDplk(urlProductFtuDplk: String) {
        this.mUrlProductFtuDplk = urlProductFtuDplk
    }

    override fun getDataInquiryDplk(request: InquiryDplkRegisRequest) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlInquiryDplk, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(FormPilihBrifineResponse::class.java)
                            getView().onSuccessInquiryDplkRegis(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataProductFtuDplk(request: ProductFtuDplkRequest, isCombination: Boolean) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlProductFtuDplk, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(ProductFtuDplkResponse::class.java)
                            responseHeader = data
                            getDataSimulasiDplk(requestSimulasiDetail)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDetailPerformanceTabDplk(request: ProductFtuDplkRequest) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable
                .add(
                    apiSource.getData(mUrlDetailPerformanceTab, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                val data =
                                    response.getData(DetailPerformanceTabDplkResponse::class.java)
                                responseGraphic = data
                                getDataProductFtuDplk(requestProductDetail, mIsCombination)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                getView().onException(restResponse.desc)
                            }
                        })
                )
        }
    }

    override fun getDataSimulasiDplk(request: SimulasiDplkRequest) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlSimulasi, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.hideProgress()
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(SimulasiDplkResponse::class.java)
                            responseSimulasi = data
                            successLoadingDataDetailProduct()
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDetailProductDpllk(
        request: ProductFtuDplkRequest,
        requestSimulasi: SimulasiDplkRequest,
        isCombination: Boolean,
    ) {
        view.showProgress()
        requestProductDetail = request
        requestSimulasiDetail = requestSimulasi
        mIsCombination = isCombination
        getDetailPerformanceTabDplk(request)
    }

    private fun successLoadingDataDetailProduct() {
        view.hideProgress()
        view.onSuccesDetailProductFtuDplk(responseHeader, responseSimulasi, responseGraphic)
    }

    override fun saveDataGraphicDplk(response: DetailPerformanceTabDplkResponse) {
        getBRImoPrefRepository().saveDataGrapichDplk(response.fromObjectToJson())
    }
}