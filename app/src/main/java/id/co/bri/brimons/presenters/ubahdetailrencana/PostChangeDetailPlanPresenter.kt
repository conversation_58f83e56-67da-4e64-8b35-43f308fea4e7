package id.co.bri.brimons.presenters.ubahdetailrencana

import id.co.bri.brimons.contract.IPresenter.ubahrencana.IPostChangeDetailPlanPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.ubahdetailrencana.IPostChangeDetailPlanView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.ubahdetailrencana.PostChangeDetailPlanRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class PostChangeDetailPlanPresenter <V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable,
    mBRImoPrefRepository, apiSource,
    transaksiPfmSource
), IPostChangeDetailPlanPresenter<V> where V: IMvpView, V: IPostChangeDetailPlanView {

    @JvmField
    var urlPostChangeDetailPlan = ""

    override fun setUrlPostChangeDetailPlan(url: String) {
        this.urlPostChangeDetailPlan = url
    }

    override fun postChangeDetailPlanRequest(postChangeDetailPlanRequest: PostChangeDetailPlanRequest) {
        if (urlPostChangeDetailPlan.isEmpty() || !isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            urlPostChangeDetailPlan,
            postChangeDetailPlanRequest,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView().apply {
                        hideProgress()
                        onException(message)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView().apply {
                        hideProgress()
                        if (response != null) {
                            getView().onSuccessResponse(response.desc)
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView().apply {
                            hideProgress()
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                RestResponse.ResponseCodeEnum.RC_12.value -> this.onException(response.restResponse.desc)
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }
}