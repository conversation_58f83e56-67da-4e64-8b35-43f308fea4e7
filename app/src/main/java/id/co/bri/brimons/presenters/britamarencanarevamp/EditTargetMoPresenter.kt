package id.co.bri.brimons.presenters.britamarencanarevamp

import id.co.bri.brimons.contract.IPresenter.britamarencanarevamp.IEditTargetMoPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.britamarencanarevamp.IEditTargetMoView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.ubahdetailrencana.AturKonfirmasiRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class EditTargetMoPresenter<V>(schedulerProvider: SchedulerProvider,
                               compositeDisposable: CompositeDisposable,
                               mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                               categoryPfmSource: CategoryPfmSource,
                               transaksiPfmSource: TransaksiPfmSource,
                               anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IEditTargetMoPresenter<V> where V : IMvpView, V : IEditTargetMoView {

    private var mUrlKonfirmasi : String? = null
    override fun setUrlKonfirmasi(url: String) {
        mUrlKonfirmasi = url
    }

    override fun getDataConfirmation(request: AturKonfirmasiRequest) {
        if (mUrlKonfirmasi?.isEmpty() == true || !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(mUrlKonfirmasi, request, seqNum) //function(param)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val generalConfirmationResponse = response.getData(
                                        GeneralConfirmationResponse::class.java
                                )
                                getView().onGetDataConfirmation(generalConfirmationResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                                    getView().onSessionEnd(restResponse.desc)
                                }else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                    getView().onExeception12(restResponse.desc)
                                }
                                else {
                                    getView().onException(restResponse.desc)
                                }
                            }
                        })
        compositeDisposable.add(disposable)

    }


}