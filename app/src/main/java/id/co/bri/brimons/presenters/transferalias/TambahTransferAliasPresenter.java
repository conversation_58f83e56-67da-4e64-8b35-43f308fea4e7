package id.co.bri.brimons.presenters.transferalias;

import id.co.bri.brimons.contract.IPresenter.transferalias.ITambahTransferAliasPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transferalias.ITambahTransferAliasView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryBiFastRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryRtgsRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryTransferAliasBriRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryTransferAliasRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.TransferAbnormalResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TambahTransferAliasPresenter<V extends IMvpView & ITambahTransferAliasView> extends MvpPresenter<V> implements ITambahTransferAliasPresenter<V> {

    private static final String TAG = "TambahTransferAliasPres";
    protected String inquiryUrl, inquiryUrlBiFast;
    protected String inquiryUrlRtgs;
    protected String inquiryUrlBri;

    public TambahTransferAliasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataInquiry(InquiryTransferAliasRequest inquiryTransferAliasRequest) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryTransferAliasRequest,seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                            }

                            @Override
                            public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                            }

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiry(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryBri(InquiryTransferAliasBriRequest inquiryTransferAliasRequest) {
        if (inquiryUrlBri == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrlBri, inquiryTransferAliasRequest,seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                            }

                            @Override
                            public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                            }

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiryBri(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryBiFast(InquiryBiFastRequest inquiryBiFastRequest) {
        if (inquiryUrlBiFast == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrlBiFast, inquiryBiFastRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {


                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
//                                responsebriva.setSaved("bifast");
                                getView().onSuccessInquiryBiFast(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryRtgs(InquiryRtgsRequest inquiryRtgsRequest) {
        if (inquiryUrlRtgs == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrlRtgs, inquiryRtgsRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {


                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessInquiryRtgs(responsebriva);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_03.getValue())) {
                                    TransferAbnormalResponse abnormalResponse = response.getData(TransferAbnormalResponse.class);
                                    getView().onSuccessInquiry03(abnormalResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }


    @Override
    public void setInquiryUrl(String url) {
        this.inquiryUrl = url;
    }

    @Override
    public void setInquiryUrlBri(String url) {
        this.inquiryUrlBri = url;
    }

    @Override
    public void setUrlBiFast(String url) {
        this.inquiryUrlBiFast = url;
    }

    @Override
    public void setInquiryUrlRtgs(String inquiryUrlRtgs) {
        this.inquiryUrlRtgs = inquiryUrlRtgs;
    }


}