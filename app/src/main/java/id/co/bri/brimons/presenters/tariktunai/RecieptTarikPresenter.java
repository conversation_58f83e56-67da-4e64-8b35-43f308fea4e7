package id.co.bri.brimons.presenters.tariktunai;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.tariktunai.IRecieptTarikPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.tariktunai.IRecieptTarikView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.StatusRequest;
import id.co.bri.brimons.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class RecieptTarikPresenter <V extends IMvpView & IBaseFormView & IRecieptTarikView> extends BaseFormPresenter<V> implements IRecieptTarikPresenter<V> {

    protected String formUrl;
    protected String urlDetailInbox;
    private StatusRequest statusRequest;
    private ReceiptResponse receiptResponse;
    protected boolean isLoadingItem = false;

    public RecieptTarikPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void onGetBatalTarik() {
        if (formUrl == null || !isViewAttached()) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataForm(formUrl, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    getView().onSuccesGetBatal(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void getInboxDetail(String refnumber) {
        if (isLoadingItem) {
            return;
        } else {

            if (isViewAttached()) {
                isLoadingItem = true;
                getView().showProgress();

                statusRequest = new StatusRequest(refnumber);
                String seqNum = getBRImoPrefRepository().getSeqNumber();

                getCompositeDisposable().add(
                        getApiSource().getData(urlDetailInbox, statusRequest, seqNum)//function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.single())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {

                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        isLoadingItem = false;
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        receiptResponse = response.getData(ReceiptResponse.class);
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        //TO-DO onSuccess
                                        getView().onSuccessGetInboxDetail(receiptResponse);
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                            getView().onSessionEnd(restResponse.getDesc());
                                        } else
                                            getView().onException(restResponse.getDesc());
                                    }
                                })
                );
            }
        }
    }

    @Override
    public void setUrlDetailInbox(String urlDetailInbox) {
        this.urlDetailInbox = urlDetailInbox;
    }

}