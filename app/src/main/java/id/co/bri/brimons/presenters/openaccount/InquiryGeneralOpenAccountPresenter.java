package id.co.bri.brimons.presenters.openaccount;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimons.contract.IPresenter.openaccount.IInquiryGeneralOpenAccountPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.openaccount.IInquiryGeneralOpenAccountView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.KonfirmasiGeneralOpenAccountRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryGeneralOpenAccountPresenter<V extends IMvpView & IInquiryGeneralOpenAccountView> extends MvpPresenter<V> implements IInquiryGeneralOpenAccountPresenter<V> {

    private String url;

    @Inject
    public InquiryGeneralOpenAccountPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlConfirmGeneralOpenAccount(String url) {
        this.url = url;
    }

    @Override
    public void getDataConfirm(String refNum, String accNum, boolean isSnkOk) {
        if (isViewAttached()) {
            //initiate param with getter from view

            KonfirmasiGeneralOpenAccountRequest konfirmasiRequest = new KonfirmasiGeneralOpenAccountRequest(refNum, accNum,isSnkOk);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(url, konfirmasiRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralConfirmationResponse confirmRes = response.getData(GeneralConfirmationResponse.class);
                                    getView().getDataSuccessConfirm(confirmRes);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();

                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                        getView().onException93(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }

    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault, saldoHold);
    }

    @Override
    public void start() {
        super.start();
        getDefaultSaldo();
    }
}