package id.co.bri.brimons.presenters.loaninapp

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.loaninapp.ILoanInAppFormPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.loaninapp.ILoanInAppFormView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.AmountHelper.Companion.parsingSaldoString
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.DetailCcSofRequest
import id.co.bri.brimons.models.apimodel.request.loaninapp.LoanInAppFormRequest
import id.co.bri.brimons.models.apimodel.request.loaninapp.LoanInAppTermRequest
import id.co.bri.brimons.models.apimodel.response.SnkResponse
import id.co.bri.brimons.models.apimodel.response.applyccrevamp.ApplyCcDataFormResponse
import id.co.bri.brimons.models.apimodel.response.applyccrevamp.ApplyCcSofListResponse
import id.co.bri.brimons.models.apimodel.response.cc.DetailCcSofResponse
import id.co.bri.brimons.models.apimodel.response.loaninapp.LoanInAppFormResponse
import id.co.bri.brimons.models.apimodel.response.loaninapp.LoanInAppTermResponse
import id.co.bri.brimons.models.apimodel.response.other.TitleDescriptionResponse
import id.co.bri.brimons.models.applyccrevamp.toApplyCCDataFormModel
import id.co.bri.brimons.models.loaninapp.toLoanInAppFormModel
import id.co.bri.brimons.models.loaninapp.toLoanInAppTermModel
import id.co.bri.brimons.models.other.toTitleDescriptionModel
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequest
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequestState
import id.co.bri.brimons.util.extension.handleError01
import id.co.bri.brimons.util.extension.onError
import id.co.bri.brimons.util.extension.onLoading
import id.co.bri.brimons.util.extension.onSuccess
import io.reactivex.disposables.CompositeDisposable

class LoanInAppFormPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ILoanInAppFormPresenter<V> where V : IMvpView, V : ILoanInAppFormView {

    private var ccSofListResponse: ApplyCcSofListResponse? = null

    private var count = 0
    private var isLoading = false

    override fun start() {
        super.start()
        getDefaultSaldo()
        getAccountWithSaldo()
    }

    override fun getAccountWithSaldo() {
        getView().getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_cc_sof_v3),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            false,
            onApiCallError = { restResponse ->
                if (restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                    ++count
                    getView().onSessionEnd(restResponse.desc)
                } else {
                    getView().onException(restResponse.desc)
                }
            },
        ) { response ->
            when {
                response.code.equals(Constant.RC_SUCCESS, ignoreCase = true) -> {
                    val data = response.getData(ApplyCcSofListResponse::class.java)
                    ccSofListResponse = data
                    getView().onSuccessGetListCcSof(data)
                }

                response.code.equals(Constant.RE01, ignoreCase = true) -> {
                    getView().onSuccess01(response.desc)
                }
            }
        }
    }

    override fun getFormData(pin: String, cardToken: String) {
        val request = LoanInAppFormRequest(pin, brImoPrefRepository.username, cardToken)
        getView().getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_loan_in_app_form),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            request,
            onApiCallError = { restResponse ->
                if (restResponse.code.equals(
                        Constant.RE_SESSION_END,
                        ignoreCase = true
                    )
                ) getView().onSessionEnd(restResponse.desc)
                else if (restResponse.code.equals(
                        Constant.RE24,
                        ignoreCase = true
                    )
                ) getView().onApplicationLimitInformation()
                else if (restResponse.code.equals(
                        Constant.RE35,
                        ignoreCase = true
                    )
                ) getView().onApplicationRejected()
                else getView().onException(restResponse.desc)
            }
        ) { response ->
            val data = response.getData(LoanInAppFormResponse::class.java)
            getView().onSuccessGetFormData(data.toLoanInAppFormModel())
        }

    }

    override fun getTermData(loanInAppTermRequest: LoanInAppTermRequest) {
        getView().getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_loan_in_app_term),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            loanInAppTermRequest
        ) { response ->
            val data = response.getData(LoanInAppTermResponse::class.java)
            getView().onSuccessGetTermData(data.toLoanInAppTermModel())
        }

    }

    override fun getUsername() = brImoPrefRepository.username ?: ""
    override fun getTerm() {
        getView().getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_cc_binding_form),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            onFailureHttp = {
                view.onException(it)
            },
            onApiCallError = {
                if (it.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                    ++count
                    checkSession(it.desc)
                } else {
                    view.onException(it.desc)
                }
            }
        ) { response ->
            val snkResponse: SnkResponse = response.getData(SnkResponse::class.java)
            view.onGetTermSuccess(snkResponse)
        }
    }

    override fun getProductList() {
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_get_data_from),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
        ) {
            val data = it
            data.onSuccess { successResponse ->
                when {
                    successResponse.handleError01() -> {
                        val titleDescriptionModel =
                            successResponse.getData(TitleDescriptionResponse::class.java)
                                .toTitleDescriptionModel()
                        view.showErrorGetDataFormDEDialog(
                            titleDescriptionModel.title,
                            titleDescriptionModel.description
                        )
                    }

                    else -> view.onGetProductListSuccessState(
                        successResponse.getData(
                            ApplyCcDataFormResponse::class.java
                        ).toApplyCCDataFormModel()
                    )
                }
            }
                .onError { errorResponse -> view.onException(errorResponse.desc) }
                .onLoading { isLoadingData -> if (isLoadingData) view.showProgress() else view.hideProgress() }
        }
    }

    override fun getDetailCc(
        cardToken: String,
        itemPosition: Int,
        account: ApplyCcSofListResponse.Account
    ) {
        val request = DetailCcSofRequest(cardToken)
        view.getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_cc_sof_detail),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            false,
            request,
            onApiCallError = { restResponse ->
                var detailCcSofResponse = DetailCcSofResponse()
                detailCcSofResponse.balanceString = restResponse.code
                detailCcSofResponse.name = restResponse.desc
                account.detailCcResponse = detailCcSofResponse
                view.getDetailCcError(account, itemPosition)
            }
        ) {
            val detailCcSofResponse = it.getData(DetailCcSofResponse::class.java)
            account.detailCcResponse = detailCcSofResponse
            view.getDetailCcSuccess(account, itemPosition)
        }
    }

    private fun checkSession(msg: String) {
        if (count == 1) {
            view.onSessionEnd(msg)
        } else {
            stop()
        }
    }

    protected fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtamaString
        saldo = parsingSaldoString(saldoText)
        val saldoHold = brImoPrefRepository.saldoHold

        val defaultAcc = brImoPrefRepository.accountDefault

        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }
}