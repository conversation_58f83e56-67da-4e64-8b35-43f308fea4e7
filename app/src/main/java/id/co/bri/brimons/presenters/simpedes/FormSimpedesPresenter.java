package id.co.bri.brimons.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.simpedes.IFormSimpedesPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IFormSimpedesView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryImpianRequest;
import id.co.bri.brimons.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormSimpedesPresenter<V extends IMvpView & IFormSimpedesView>
        extends MvpPresenter<V> implements IFormSimpedesPresenter<V> {

    protected String urlAddImpian;
    protected String urlAddImpianInquiry;

    public FormSimpedesPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                 BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                 TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlAddImpianInquiry(String urlAddImpianInquiry) {
        this.urlAddImpianInquiry = urlAddImpianInquiry;
    }

    @Override
    public void getDataInquiry(String account, long targetAmount, int month, String dreamName) {
        if (isViewAttached()) {
            //initiate param with getter from view

            InquiryImpianRequest inquiryImpianRequest = new InquiryImpianRequest(account, targetAmount, month, dreamName);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlAddImpianInquiry, inquiryImpianRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InquiryOpenRencanaResponse inquiryRes = response.getData(InquiryOpenRencanaResponse.class);
                                    getView().getSuccessInquiryImpian(inquiryRes);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }


}