package id.co.bri.brimons.presenters.base

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.base.IBaseTransactionPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.base.IBaseTransactionView
import id.co.bri.brimons.contract.IView.pulsarevamp.reskin.AccountModelNs
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.api.observer.ApiReskinObserver
import id.co.bri.brimons.data.api.observer.ResExceptionErr
import id.co.bri.brimons.data.api.observer.mapping
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.AccountModel
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimons.models.apimodel.response.ExceptionResponse
import id.co.bri.brimons.models.apimodel.response.GeneralResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SaldoReponse
import id.co.bri.brimons.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.DecodeResult
import id.co.bri.brimons.util.ResponseDecoder
import id.co.bri.brimons.util.subscribeWithObserver
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable

abstract class BaseTransactionPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IBaseTransactionPresenter<V> where V : IMvpView {
    private var account: AccountModel?= null
    private var accountList: MutableList<AccountModel> = mutableListOf()
    private var isFetching = false
    private var isFm = false

    override fun getAccountList() {
        val mUrl = if(!isFm) GeneralHelper.getString(R.string.url_v5_account_list) else GeneralHelper.getString(R.string.url_v5_account_list_fm) ?: return
        val seqNum = brImoPrefRepository.seqNumber

        val sObserve = if(isFm)
            apiSource.getData(mUrl, getFastMenuRequest(), seqNum)
        else apiSource.getDataForm(mUrl, seqNum)

        sObserve.subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object : ApiReskinObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        val list = response
                            .getData(AccountModelNs::class.java)
                            .account.toMutableList()

                        accountList = list
                        account = list.find { it.isDefault == 1 } ?: accountList.firstOrNull()

                        if (!isFm) {
                            getSaldo()
                        } else {
                            account?.let{
                                it.saldoReponse = SaldoReponse().apply {
                                    balanceString = "TO"
                                }
                                (getView() as IBaseTransactionView).onSuccessAccountList(accountList, it)
                            }
                        }
                    }
                    override fun onApiCallError(errRes: ResExceptionErr) {
                    }
                }
            }
        )
    }

    override fun getSaldo() {
        val mUrl = GeneralHelper.getString(R.string.url_v2_saldo_normal)
        val seqNum = brImoPrefRepository.seqNumber

        apiSource.getData(
            mUrl,
            SaldoRequestNS(account = account?.acoount?: ""),
            seqNum
        ).subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object : ApiReskinObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        val result = response.getData(SaldoReponse::class.java)
                        account?.let{
                            it.saldoReponse = result
                            (getView() as IBaseTransactionView).onSuccessAccountList(accountList, it)
                        }
                    }
                    override fun onApiCallError(errRes: ResExceptionErr) {
                        account?.let{
                            it.saldoReponse = SaldoReponse().apply {
                                balanceString = "TO"
                            }
                            (getView() as IBaseTransactionView).onSuccessAccountList(accountList, it)
                        }
                    }
                }
            }
        )
    }

    override fun isFromFastMenu(isFm: Boolean) {
        this.isFm = isFm
    }
}

class SaldoRequestNS (
    @SerializedName("account") @Expose
    var account: String = "",
)