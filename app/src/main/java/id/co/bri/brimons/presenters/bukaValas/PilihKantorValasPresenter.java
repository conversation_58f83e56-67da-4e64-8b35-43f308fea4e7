package id.co.bri.brimons.presenters.bukaValas;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.bukaValas.IPilihKantorBukaValasPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.bukaValas.IPilihKantorValasView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.LocationRequest;
import id.co.bri.brimons.models.apimodel.request.SearchLocationRequest;
import id.co.bri.brimons.models.apimodel.response.ListKantorResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PilihKantorValasPresenter<V extends IMvpView & IPilihKantorValasView> extends MvpPresenter<V>
        implements IPilihKantorBukaValasPresenter<V> {
    protected String url;
    protected String searchUrl;

    public PilihKantorValasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void sendLokasiSendiri(LocationRequest locationRequest) {
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(url, locationRequest,seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            ListKantorResponse listKantorResponse = response.getData(ListKantorResponse.class);
                            getView().onSuccessLocationDefault(listKantorResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onFailedLocation();
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }

    }

    @Override
    public void sendSearchLocation(SearchLocationRequest searchLocationRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(searchUrl, searchLocationRequest,seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            ListKantorResponse listKantorResponse = response.getData(ListKantorResponse.class);
                            getView().onSuccessLocationDefault(listKantorResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onFailedLocation();
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setSearchUrl(String searchUrl) {
        this.searchUrl = searchUrl;
    }
}