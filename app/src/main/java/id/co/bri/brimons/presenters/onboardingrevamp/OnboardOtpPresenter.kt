package id.co.bri.brimons.presenters.onboardingrevamp

import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardOtpPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardOtpView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.OnboardingConfig.StatusNewOnboarding
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.response.OnboardingResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.newskinonboarding.OnboardingErrorResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardOtpPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardOtpPresenter<V> where V : IMvpView, V : IOnboardOtpView {
    private var urlOtpNoHp = ""
    private var urlResendOtpNoHp = ""

    override fun setUrlResendOtpNoHp(url: String) {
        urlResendOtpNoHp = url
    }

    override fun setUrlOtpNoHp(url: String) {
        urlOtpNoHp = url
    }


    override fun sendOtpNoHp(otp: String) {
        if (urlOtpNoHp.isEmpty() || !isViewAttached) return
        getView().showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlOtpNoHp, "", seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val onboardRes = response.getData(OnboardingResponse::class.java)
                        if (onboardRes.status == StatusNewOnboarding.READY_TO_LOGIN.name) {
                            val errorResponse =
                                response.getData(OnboardingErrorResponse::class.java)
                            getView().onReadyToLogin(errorResponse)
                        } else {
                            getView().onSuccessOtpNoHp()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when (restResponse.code) {
                            "12" -> getView().onErrorWrongOtp()
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun sendResendOtpNoHp(method: String) {
        if (urlResendOtpNoHp.isEmpty() || !isViewAttached) return
        getView().showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlResendOtpNoHp, "", seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                })
        )
    }
}