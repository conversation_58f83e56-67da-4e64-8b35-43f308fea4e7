package id.co.bri.brimons.presenters.onboardingnewskin

import id.co.bri.brimons.contract.IPresenter.newskinonboarding.ICheckPassPresenter
import id.co.bri.brimons.contract.IView.newskinonboarding.IChangePassView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.PassChangeRequest
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.PassCheckResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.PinCheckSuccessData
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ChangePassPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), ICheckPassPresenter<V> where V : IChangePassView {

    private var urlValidatePass = ""
    private var code = ""
    override fun setValidatePassUrl(url: String) {
        urlValidatePass = url
    }

    override fun onCheckPassSubmit(pass: String) {
        if (!isViewAttached || urlValidatePass.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = PassChangeRequest(password = pass)

        compositeDisposable.add(
            apiSource.getData(urlValidatePass, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PinCheckSuccessData::class.java)
                        getView().onPassValid(checkResponse.referenceNumber, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PassCheckResponse::class.java)
                        code = restResponse.code
                        getView().onPassCheckFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailure(true, code)
                    }
                })
        )
    }
}