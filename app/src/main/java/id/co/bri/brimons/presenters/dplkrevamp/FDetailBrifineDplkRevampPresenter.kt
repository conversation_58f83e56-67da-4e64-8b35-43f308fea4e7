package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IFDetailBrifineDplkRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IFDetailBrifineDplkRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.RiwayatSetoranDplkRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.InquirySettingAutoPaymentResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.RiwayatSetoranResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FDetailBrifineDplkRevampPresenter<V>(schedulerProvider: SchedulerProvider?,
                                           compositeDisposable: CompositeDisposable?,
                                           mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                           categoryPfmSource: CategoryPfmSource?,
                                           transaksiPfmSource: TransaksiPfmSource?,
                                           anggaranPfmSource: AnggaranPfmSource?)  : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IFDetailBrifineDplkRevampPresenter<V> where V : IMvpView?, V : IFDetailBrifineDplkRevampView {

    private var mUrlRiwayatSetoranBrifine: String = ""
    private var mUrlInquiryAutoPayment: String? = null

    override fun setUrlInquiryAutoPayment(urlAddAutoPayment: String) {
        mUrlInquiryAutoPayment = urlAddAutoPayment
    }

    override fun setUrlRiwayatSetoranBrifine(urlRiwayatSetoranBrifine: String) {
        mUrlRiwayatSetoranBrifine = urlRiwayatSetoranBrifine
    }

    override fun getInquiryAutoPayment(request: InquiryAutoPaymentRequest) {
        if (mUrlInquiryAutoPayment == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiryAutoPayment, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(
                                InquirySettingAutoPaymentResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessInquiryAutoPayment(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView()!!.onSessionEnd(restResponse.desc)
                                else -> getView()!!.onException(restResponse.desc)
                            }
                        }
                    })
            )
    }


    override fun getRiwayatSetoranBrifine(request: RiwayatSetoranDplkRequest) {
        if (mUrlRiwayatSetoranBrifine == "" || !isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlRiwayatSetoranBrifine, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(
                                RiwayatSetoranResponse::class.java
                            )
                            if (restResponse.code.equals("00", ignoreCase = true)) {
                                getView().onSuccessRiwayatSetoran(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

}