package id.co.bri.brimons.presenters.pfmPickCategory;

import id.co.bri.brimons.contract.IPresenter.pickCategory.IPickKategoryPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.pickCategory.IPickKategoryView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.presenters.MvpPresenter;

import javax.inject.Inject;

import io.reactivex.disposables.CompositeDisposable;

public class PickKategoryFragmentPresenter<V extends IMvpView & IPickKategoryView> extends MvpPresenter<V> implements IPickKategoryPresenter<V> {

    private static final String TAG = "PickKategoryFragmentPresenter";
    private CategoryPfmSource mCategoryPfmSource;

    @Inject
    public PickKategoryFragmentPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        mCategoryPfmSource = categoryPfmSource;
    }


    @Override
    public void loadDataCategory() {
        //Rx Observer
        getCompositeDisposable().add(mCategoryPfmSource.getCategoryTransaksi()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(categories -> {
                    if (categories != null) {
                        getView().onLoadData(categories);
                    }
                }, throwable -> {
                    getView().onException(throwable.getMessage().toString());

                }));
    }

    @Override
    public void start() {
        super.start();
        loadDataCategory();
    }

}
