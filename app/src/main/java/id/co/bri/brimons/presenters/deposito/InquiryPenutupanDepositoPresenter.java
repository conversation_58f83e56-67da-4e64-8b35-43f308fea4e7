package id.co.bri.brimons.presenters.deposito;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.deposito.IInquiryPenutupanDepositoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.deposito.IInquiryPenutupanDepositoView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ConfirmationPenutupanDepositoRequest;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class InquiryPenutupanDepositoPresenter<V extends IMvpView & IInquiryPenutupanDepositoView> extends MvpPresenter<V> implements IInquiryPenutupanDepositoPresenter<V> {

    private String url;

    public InquiryPenutupanDepositoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void sendInquiryPenutupan(String pin, String refnum) {
        if (getView() == null) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        ConfirmationPenutupanDepositoRequest request = new ConfirmationPenutupanDepositoRequest(refnum,pin);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                        PendingResponse pendingResponse = response.getData(PendingResponse.class);
                                        getView().onSuccessGetResponse(pendingResponse);
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                        getView().onException(response.getDesc());
                                    }

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                        getView().onException99(restResponse.getDesc());
                                    }else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );


        }
    }



}