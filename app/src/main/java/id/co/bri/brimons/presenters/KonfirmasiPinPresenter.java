package id.co.bri.brimons.presenters;

import android.util.Log;

import javax.inject.Inject;

import id.co.bri.brimons.contract.IPresenter.IKonfirmasiPinPresenter;
import id.co.bri.brimons.contract.IView.IKonfirmasiPinView;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ChangeDeviceRequest;
import id.co.bri.brimons.models.apimodel.response.LoginResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiPinPresenter<V extends IMvpView & IKonfirmasiPinView> extends MvpPresenter<V> implements IKonfirmasiPinPresenter<V> {

    private static final String TAG = "KonfirmasiPinPresenter";

    private String urlChange;

    @Inject
    public KonfirmasiPinPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onKirimUlang() {

    }

    @Override
    public void onClickLanjut(String pin) {

    }


    @Override
    public void checkpin(String pin) {
        if (getView() != null) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().createPin(pin, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().onEditUsername(response.getDesc());
                            } else {
                                //update flag login
                                updateLoginFlag(true);
                                getView().onPinCreated();
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void changeDevice(String pin, String refNum) {
        if (urlChange == null) {
            if (!GeneralHelper.isProd()) {
                Log.d(TAG, "changeDevice: ");
            }
            return;
        }

        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(urlChange, new ChangeDeviceRequest(pin, refNum), seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            LoginResponse loginResponse = response.getData(LoginResponse.class);
                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                //parsing data akan ada disini
                                getBRImoPrefRepository().saveTokenKey(loginResponse.getTokenKey());

                                getView().onChangeDevice(loginResponse);
                                if (!getBRImoPrefRepository().getFreshInstallFlag()) {
                                    getBRImoPrefRepository().deleteUserAlias();
                                }
                            } else if (response.getCode().equals(Constant.RE_CHANGE_DEVICE_MNV)) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                                getView().onChangeMNV(response.getDesc(), loginResponse);
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                getView().onSalahPin(restResponse.getDesc());
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    public void setUrlChange(String urlChange) {
        this.urlChange = urlChange;
    }
}