package id.co.bri.brimons.presenters.bukaValas;


import android.util.Log;

import com.google.gson.Gson;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.bukaValas.IInquiryBukaValasPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.bukaValas.IInquiryBukaValasView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.KonfimasiBukaValasRequest;
import id.co.bri.brimons.models.apimodel.response.KonfirmasiBukaValasResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryBukaValasPresenter<V extends IMvpView & IInquiryBukaValasView> extends MvpPresenter<V> implements IInquiryBukaValasPresenter<V> {
    protected static final String TAG = "InquiryBukaValasPresenter";
    protected String urlConfirmation;
    protected Object confirmationRequest;

    public InquiryBukaValasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataKonfirmasi(KonfimasiBukaValasRequest request) {
        if (isViewAttached() && urlConfirmation != null) {

            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlConfirmation, request, seqNum)//function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {

                            try {
                                getView().hideProgress();
                                KonfirmasiBukaValasResponse brivaResponse = response.getData(KonfirmasiBukaValasResponse.class);
                                getView().onSuccesConfirmation(brivaResponse);
                                Gson gson = new Gson();
                                String chuck = gson.toJson(brivaResponse);
                                Log.d("Chuck", chuck);
                            } catch (Exception e) {
                                getView().onException("Koneksi Terputus");
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());


//                            GeneralHelper.responseChuck(restResponse);

                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    /**
     * @param urlConfirmation
     */
    @Override
    public void setUrlConfirmation(String urlConfirmation) {
        this.urlConfirmation = urlConfirmation;
    }


    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean isSaldoHold = getBRImoPrefRepository().getSaldoHold();
        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, isSaldoHold);
    }

}