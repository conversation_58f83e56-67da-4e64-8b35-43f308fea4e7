package id.co.bri.brimons.presenters.asuransiRevamp

import id.co.bri.brimons.contract.IPresenter.asuransiRevamp.IFormAsuransiRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.asuransiRevamp.IFormAsuransiRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.asuransirevamp.KonfirmasiAsuransiRequest
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormAsuransiRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormAsuransiRevampPresenter<V> where V : IMvpView, V : IFormAsuransiRevampView {

    var url: String? = null
    override fun setUrlKonfirmasi(urlKonfirmasi: String) {
        url = urlKonfirmasi
    }

    override fun getDataKonfirmasi(request: KonfirmasiAsuransiRequest) {
        if (isViewAttached) {
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(url, request, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val formAsuransiResponse = response.getData(
                                InquiryBrivaRevampResponse::class.java
                            )
                            getView()!!.onSuccessKonfirmasiAsuransi(formAsuransiResponse)


                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }
}