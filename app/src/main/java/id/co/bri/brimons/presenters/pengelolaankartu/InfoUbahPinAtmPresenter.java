package id.co.bri.brimons.presenters.pengelolaankartu;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.pengelolaankartu.IInfoUbahPinAtmPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.pengelolaankartu.IInfoUbahPinAtmView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.PinBrimoRequest;
import id.co.bri.brimons.models.apimodel.response.RefnumResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoUbahPinAtmPresenter<V extends IMvpView & IInfoUbahPinAtmView>
        extends MvpPresenter<V> implements IInfoUbahPinAtmPresenter<V> {

    private String urlPinBrimo;

    public InfoUbahPinAtmPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlPinBrimo(String urlPinBrimo) {
        this.urlPinBrimo = urlPinBrimo;
    }

    @Override
    public void sendDataPinBrimo(PinBrimoRequest request) {
        if (urlPinBrimo != null && isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlPinBrimo, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            RefnumResponse refnumResponse = response.getData(RefnumResponse.class);
                            getView().getDataSuccess(refnumResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }
}