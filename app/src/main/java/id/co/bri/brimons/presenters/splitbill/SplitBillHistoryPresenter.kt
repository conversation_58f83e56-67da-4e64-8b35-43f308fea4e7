package id.co.bri.brimons.presenters.splitbill

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.splitbill.ISplitBillHistoryPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.splitbill.ISplitBillHistoryView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant.SplitBillHistoryPageType
import id.co.bri.brimons.domain.config.LifestyleConfig
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.LifestyleHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.splitbill.ListSplitBillRequest
import id.co.bri.brimons.models.apimodel.request.splitbill.SplitBillItemRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.FilterSplitBillResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.SplitBillDetailResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.SplitBillHistoryResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.viewentity.BillEntity
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit

class SplitBillHistoryPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ), ISplitBillHistoryPresenter<V> where V : IMvpView?, V : ISplitBillHistoryView? {

    private var pageType: SplitBillHistoryPageType =
        SplitBillHistoryPageType.ON_PROGRESS

    private var mUrlDeleteItem = ""
    private var mUrlDetailItem = ""
    private var mUrlHistoryBillList = ""

    private var originTransactions = mutableListOf<BillEntity>()
    private var viewTransactions = mutableListOf<BillEntity>()
    private var filterData = mutableListOf<FilterSplitBillResponse>()
    private var mFilterData: LifestyleConfig.FilterSplitBill =
        LifestyleConfig.FilterSplitBill.FILTER_ALL

    private var billsData = mutableListOf<BillEntity>()

    override fun setPageType(pageType: SplitBillHistoryPageType) {
        this.pageType = pageType
    }

    override fun setUrlDeleteBillItem(urlDetele: String) {
        mUrlDeleteItem = urlDetele
    }

    override fun setUrlDetailItem(urlDetail: String) {
        mUrlDetailItem = urlDetail
    }

    override fun setUrlHistory(urlHistory: String) {
        mUrlHistoryBillList = urlHistory
    }

    override fun setBills(models: List<BillEntity>) {
        view?.showSkeleton(true)

        billsData = models.toMutableList()

        val billsFiltered: List<BillEntity> = when (pageType) {
            SplitBillHistoryPageType.ON_PROGRESS -> {
                models.filter { it.billStatus != LifestyleConfig.BillStatusConst.COMPLETE }
            }

            else -> {
                models.filter { it.billStatus == LifestyleConfig.BillStatusConst.COMPLETE }
            }
        }
        viewTransactions.clear()
        viewTransactions.addAll(billsFiltered.sortedBy { it.billStatus })
        originTransactions.addAll(viewTransactions)

        when (pageType) {
            SplitBillHistoryPageType.ON_PROGRESS ->
                if (viewTransactions.isEmpty()) {
                    view?.onHistoryEmpty(false)
                } else {
                    view?.showHistories(viewTransactions)
                }

            else -> {
                if (viewTransactions.isEmpty()) {
                    view?.onHistoryEmpty(true)
                } else {
                    view?.showHistories(viewTransactions)
                }
            }
        }
        view?.showSkeleton(false)

    }

    fun setBillsAfterRefresh(
        models: List<BillEntity>,
        filterSplitBillResponse: LifestyleConfig.FilterSplitBill
    ) {
        view?.showSkeleton(true)

        billsData = models.toMutableList()

        val dataFiltered: List<BillEntity> = if (pageType == SplitBillHistoryPageType.ON_PROGRESS) {
            when (filterSplitBillResponse) {
                LifestyleConfig.FilterSplitBill.FILTER_ALL -> {
                    models.filter { it.billStatus != LifestyleConfig.BillStatusConst.COMPLETE }
                }

                LifestyleConfig.FilterSplitBill.FILTER_IN_PROGRESS -> {
                    models.filter {
                        it.billStatus == LifestyleConfig.BillStatusConst.ON_PROGRESS
                    }
                }

                LifestyleConfig.FilterSplitBill.FILTER_DRAFT ->
                    models.filter {
                        it.billStatus == LifestyleConfig.BillStatusConst.DRAFT
                    }
            }
        } else {
            models.filter {
                it.billStatus == LifestyleConfig.BillStatusConst.COMPLETE
            }
        }

        viewTransactions.clear()
        viewTransactions.addAll(dataFiltered.sortedBy { it.billStatus })
        originTransactions.clear()
        originTransactions.addAll(viewTransactions)

        when (pageType) {
            SplitBillHistoryPageType.ON_PROGRESS ->
                if (viewTransactions.isEmpty()) {
                    view?.onHistoryEmpty(false)
                } else {
                    view?.showHistories(viewTransactions)
                }

            else -> {
                if (viewTransactions.isEmpty()) {
                    view?.onHistoryEmpty(true)
                } else {
                    view?.showHistories(viewTransactions)
                }
            }
        }
        view?.showSkeleton(false)

    }

    override fun fetchHistories() {
        if (isViewAttached) {

            view?.showSkeleton(true)

            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(
                    mUrlHistoryBillList,
                    ListSplitBillRequest(),
                    seqNum
                )
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.showSkeleton(false)
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.showSkeleton(false)
                            val billHisResponse = response.getData(
                                SplitBillHistoryResponse::class.java
                            )

                            setBillsAfterRefresh(
                                LifestyleHelper.mapToBillEntity(billHisResponse),
                                mFilterData
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.showSkeleton(false)
                        }
                    })
            )
        }
    }

    override fun searchHistories(searchQuery: String) {
        val listViewTransaction = if (searchQuery.isEmpty()) {
            originTransactions
        } else {
            val keyWords = searchQuery.lowercase().trim()
            originTransactions.filter { data ->
                data.title.contains(keyWords, true)
            }
        }
        view?.showHistories(listViewTransaction, searchQuery)
    }

    override fun onClickBillItem(billEntity: BillEntity) {
        if (mUrlDetailItem.isEmpty() || !isViewAttached)
            return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        val disposable: Disposable = apiSource.getData(
            mUrlDetailItem,
            SplitBillItemRequest(billEntity.billId),
            seqNum
        )
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String?) {
                    getView()?.hideProgress()
                    getView()?.onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView()?.hideProgress()

                    val detailSplitbill = response.getData(SplitBillDetailResponse::class.java)

                    detailSplitbill.let {
                        if (it.billModel.confirmation != null) {
                            LifestyleHelper.mapToConfirmationStep(detailSplitbill)
                                ?.let { confimData ->
                                    getView()?.openBillOnConfirmationStep(confimData, it)
                                }
                        } else if (it.billModel.detail != null && it.billModel.split != null) {
                            getView()?.openBillOnShareStep(it)
                        } else if (it.billModel.detail != null && it.billModel.confirmation == null) {
                            getView()?.openBillOnDetailStep(it)
                        } else {
                            getView()?.openBillOnGeneratedStep(
                                LifestyleHelper.mapToGeneratedStep(it)
                            )
                        }
                    }

                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView()?.hideProgress()
                    getView()?.onException(restResponse.desc)
                }
            })

        compositeDisposable.add(disposable)
    }

    override fun onDeleteBillItem(billEntity: BillEntity) {
        if (mUrlDeleteItem.isEmpty() || !isViewAttached)
            return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        val disposable: Disposable = apiSource.getData(
            mUrlDeleteItem,
            SplitBillItemRequest(billEntity.billId),
            seqNum
        )
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String?) {
                    getView()?.hideProgress()
                    getView()?.onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView()?.hideProgress()

                    viewTransactions.remove(billEntity)

                    when (pageType) {
                        SplitBillHistoryPageType.ON_PROGRESS ->
                            if (viewTransactions.isEmpty()) {
                                getView()?.onHistoryEmpty(false)
                            } else {
                                getView()?.refreshBillData(viewTransactions)
                            }

                        else -> {
                            if (viewTransactions.isEmpty()) {
                                getView()?.onHistoryEmpty(true)
                            } else {
                                getView()?.showHistories(viewTransactions)
                            }
                        }
                    }

                    getView()?.refreshBillData(viewTransactions)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView()?.hideProgress()
                    getView()?.onException(restResponse.desc)
                }
            })

        compositeDisposable.add(disposable)
    }

    override fun setupFilter() {
        filterData.clear()
        filterData.addAll(
            listOf(
                FilterSplitBillResponse(
                    name = GeneralHelper.getString(R.string.txt_filter_all),
                    isSelected = true
                ),

                FilterSplitBillResponse(
                    name = GeneralHelper.getString(R.string.txt_filter_on_progress),
                    isSelected = false
                ),
                FilterSplitBillResponse(
                    name = GeneralHelper.getString(R.string.txt_filter_draft),
                    isSelected = false
                )
            )
        )

        view?.showFilter(filterData, isFilterShow(pageType))
    }

    override fun onClickFilter(filterSplitBillResponse: LifestyleConfig.FilterSplitBill) {
        view?.showSkeleton(true)

        mFilterData = filterSplitBillResponse

        val dataFiltered: List<BillEntity> = when (filterSplitBillResponse) {
            LifestyleConfig.FilterSplitBill.FILTER_ALL -> {
                billsData.filter { it.billStatus != LifestyleConfig.BillStatusConst.COMPLETE }
            }

            LifestyleConfig.FilterSplitBill.FILTER_IN_PROGRESS -> {
                billsData.filter {
                    it.billStatus == LifestyleConfig.BillStatusConst.ON_PROGRESS
                }
            }

            LifestyleConfig.FilterSplitBill.FILTER_DRAFT ->
                billsData.filter {
                    it.billStatus == LifestyleConfig.BillStatusConst.DRAFT
                }
        }

        viewTransactions.clear()
        viewTransactions.addAll(dataFiltered.sortedBy { it.billStatus })
        originTransactions.addAll(viewTransactions)

        if (viewTransactions.isEmpty()) {
            view?.onHistoryEmpty(false)
        } else {
            view?.showHistories(viewTransactions)
        }

        view?.showSkeleton(false)
    }

    private fun isFilterShow(pageType: SplitBillHistoryPageType): Boolean {
        val isShow: Boolean = pageType == SplitBillHistoryPageType.ON_PROGRESS

        return isShow
    }

}
