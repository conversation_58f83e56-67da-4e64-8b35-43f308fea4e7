package id.co.bri.brimons.presenters.remittence;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.remittence.ISearchBankPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.remittence.ISearchBankView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.SearchBankRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SearchBankResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FragmentBankSearchPresenter <V extends IMvpView & ISearchBankView> extends MvpPresenter<V> implements ISearchBankPresenter<V> {

    String mUrl;

    public FragmentBankSearchPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.mUrl = url;
    }

    @Override
    public void getListBank(SearchBankRequest searchBankRequest) {
        if (isViewAttached()) {
            getView().showSkeleton();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(mUrl, searchBankRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideSkeleton();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideSkeleton();

                            SearchBankResponse searchBankResponse = response.getData(SearchBankResponse.class);
                            getView().onSuccessGetJalur(searchBankResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideSkeleton();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onExceptionNF();
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }
}