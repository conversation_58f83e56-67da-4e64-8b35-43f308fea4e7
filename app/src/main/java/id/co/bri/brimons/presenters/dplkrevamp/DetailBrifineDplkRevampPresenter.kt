package id.co.bri.brimons.presenters.dplkrevamp


import android.util.Log
import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IDetailBrifineDplkRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IDetailBrifineDplkRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.InquiryDplkRequest
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.DetailBrifineDplkRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.KlaimDplkRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.PendingResponse
import id.co.bri.brimons.models.apimodel.response.QuestionResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.DetailBrifineDplkResponse
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.presenters.revamp.dashboard.DashboardRevampPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailBrifineDplkRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                                          compositeDisposable: CompositeDisposable?,
                                          mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                          categoryPfmSource: CategoryPfmSource?,
                                          transaksiPfmSource: TransaksiPfmSource?,
                                          anggaranPfmSource: AnggaranPfmSource?)  : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IDetailBrifineDplkRevampPresenter<V> where V : IMvpView?, V : IDetailBrifineDplkRevampView {


    var urlDataInquiryTopUp: String = ""
    var mUrlClaimDplk : String = ""
    private var urlPusatBantuan : String = ""
    private var urlDetail: String = ""
    private var mUrlDetailClaimBrifine = ""


    override fun getDataInquiryTopUp(no_dplk: String) {
        if (isViewAttached) {
            //initiate param with getter from view
            view.showProgress()
            val inquiryRequest = InquiryDplkRequest(no_dplk)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable
                .add(
                    apiSource.getData(urlDataInquiryTopUp, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                //TO-DO onSuccess
                                getView().hideProgress()
                                val responsebriva = response.getData(
                                    InquiryDompetDigitalResponse::class.java
                                )
                                getView().onSuccessGetInquiry(
                                    responsebriva
                                )

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                getView().onException(
                                    restResponse.desc
                                )
                            }
                        })
                )
        }

    }

    override fun getDataInquiryClaimDplk(request: KlaimDplkRequest) {
        if (isViewAttached) {
            view?.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlClaimDplk,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.hideProgress()
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view?.hideProgress()
                            val data = response.getData(GeneralConfirmationResponse::class.java)

                            getView()?.onSuccessInquiryClaimDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onExceptionClaim(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getPusatBantuanSafety(code: String) {
        if (urlPusatBantuan == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(code)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlPusatBantuan, safetyPusatBantuanRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val topicQuestionResponse = response.getData(
                                QuestionResponse::class.java
                            )
                            if (urlPusatBantuan != null) getView().onSuccessGetPusatBantuan(
                                topicQuestionResponse
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(
                                    "05",
                                    ignoreCase = true
                                )
                            ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                restResponse.desc
                            )
                        }
                    })
            )

    }

    override fun setInquiryUrlTopUp(url: String) {
        urlDataInquiryTopUp = url
    }


    override fun setUrlDetailDplk(urlDetail: String) {
        this.urlDetail = urlDetail
    }

    override fun setUrlClaimDplk(urlDetail: String) {
        mUrlClaimDplk = urlDetail
    }

    override fun setUrlPusatBantuan(url: String) {
        urlPusatBantuan = url
    }

    override fun getDetailDplk(request: DetailBrifineDplkRequest) {
        if (isViewAttached) {
            view?.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlDetail, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.hideProgress()
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view?.hideProgress()
                            val data = response.getData(DetailBrifineDplkResponse::class.java)

                            getView()?.onSuccessGetDetailBrfineDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getClaimDplkUrl(url: String) {
        mUrlDetailClaimBrifine = url
    }

    override fun getDetailClaimBrifine(request: DetailKlaimDplkRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlDetailClaimBrifine,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(ReceiptRevampResponse::class.java)

                            getView().onSuccessGetHistoryDetailClaimDplk(data)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }


}