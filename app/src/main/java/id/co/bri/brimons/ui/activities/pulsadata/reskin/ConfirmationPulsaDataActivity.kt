package id.co.bri.brimons.ui.activities.pulsadata.reskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.View
import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimons.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimons.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimons.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimons.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimons.data.api.observer.ResExceptionErr
import id.co.bri.brimons.databinding.ActivityOpenBillConfirmationReskinBinding
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimons.domain.helpers.reskin.ReceiptType
import id.co.bri.brimons.models.AccountModel
import id.co.bri.brimons.models.ParameterModel
import id.co.bri.brimons.models.apimodel.request.FastPaymentOpenRevampRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.SavedResponse
import id.co.bri.brimons.presenters.pulsarevamp.reskin.PaymentNS
import id.co.bri.brimons.ui.activities.base.BaseTransactionActivity
import id.co.bri.brimons.ui.activities.transaction_process.TransactionProcessActivity
import id.co.bri.brimons.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimons.ui.fragments.pin.reskin.PinReskinFragment
import javax.inject.Inject

class ConfirmationPulsaDataActivity: BaseTransactionActivity<ActivityOpenBillConfirmationReskinBinding>(
    ActivityOpenBillConfirmationReskinBinding::inflate),
    PinReskinFragment.SendPin,
//    PinFragment.SendPin,
    IPulsaDataReskinView {
    private var isCompletedPin = false
    private var pin = ""

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    companion object {
        const val TAG = "ConfirmationPulsaDataActivity"
        private var dataConfirm: GeneralConfirmationResponse?= null
        private var dataSaved: MutableList<SavedResponse> = mutableListOf()

        private var dataAccount: AccountModel?= null

        private const val ANIMATE_DUR: Long = 300

        @JvmStatic
        fun launchIntent(
            caller: Activity, fromFastMenu: Boolean,
            response: GeneralConfirmationResponse, sumberDana: AccountModel,
            savedList: MutableList<SavedResponse>
        ) {
            dataConfirm = response
            dataAccount = sumberDana
            isFromFastMenu = fromFastMenu
            dataSaved = savedList

            Intent(caller, ConfirmationPulsaDataActivity::class.java).let { intent ->
                caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@ConfirmationPulsaDataActivity
            start()
        }
    }

    override fun onBindView() {
        GeneralHelper.setToolbarNs(this, binding.toolbar, "Konfirmasi")

        binding.btnLihatLebih.visibility = View.GONE
        binding.btnSubmit.setOnItemClickListener {
            if(binding.switchSave.isChecked() && binding.bivSaveName.getText().isEmpty()) {
                binding.bivSaveName.setError("Tidak boleh kosong")
                return@setOnItemClickListener
            }

            val pinFragment = PinReskinFragment(this@ConfirmationPulsaDataActivity, this)
            pinFragment.show()
        }
        binding.switchSave.setOnCheckedChangeListener { isChecked ->
//            val transitionSet: TransitionSet = TransitionSet()
//                .addTransition(Fade())
//                .addTransition(Slide(Gravity.Left))
//                .setDuration(ANIMATE_DUR)
//
//            TransitionManager.beginDelayedTransition(binding.bivSaveName.parent as ViewGroup, transitionSet)

            if(isChecked) {
                binding.bivSaveName.visibility = View.VISIBLE
                binding.bivSaveName.setFocusable(true)
            } else {
                binding.bivSaveName.visibility = View.GONE
            }
        }

        initViews()
    }

    private fun initViews() {
        val billingDetail = dataConfirm!!.billingDetail
//        val mSaved = dataSaved.find { it.subtitle == billingDetail.description }
        val mSaved = findSaved(billingDetail.description, false)
        binding.rlFavorit.visibility = if(mSaved!=null || isFromFastMenu) View.GONE else View.VISIBLE
        binding.llSavedAs.visibility = if(mSaved!=null) View.VISIBLE else View.GONE

        binding.tvSavedAs.text = String.format(
            GeneralHelper.getString(R.string.txt_tersimpan_sebagai),
            mSaved?.title
        )

        GeneralHelper.loadIconTransaction(
            this,
            billingDetail.iconPath,
            billingDetail.iconName.split("\\.".toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().get(0),
            binding.ivArea,
            GeneralHelper.getImageId(this, "bri")
        )

        binding.tvNameCust.text = checkDataPackageFromText(billingDetail.subtitle)
        binding.tvNumberCust.text = String.format(
            GeneralHelper.getString(R.string.transaction_detail_content),
            billingDetail.title,
            billingDetail.description
        )

        binding.tvNominal.text = dataConfirm!!.amountString
        binding.tvAdminFee.text = dataConfirm!!.adminFeeString

        binding.bivSaveName.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {

                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                    clearError()
                    if (s.toString().isNotEmpty()) {
                        removeAllEndIcons()
                        addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24) {
                            clearText()
                        }
                    }

                    val findFromName = findSaved(s.toString(), true)
                    if(findFromName != null) {
                        setError("Nama sudah terdaftar. Coba nama lain, yuk.")
                    }
                }

                override fun afterTextChanged(editable: Editable?) {
                    clearError()
                    if (editable.toString().isNotEmpty()) {
                        removeAllEndIcons()
                        addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24) {
                            clearText()
                        }
                    }

                    val findFromName = findSaved(editable.toString(), true)
                    if(findFromName!=null) {
                        setError("Nama sudah terdaftar. Coba nama lain, yuk.")
                    }
                }
            })
//            addEndIcon(R.drawable.ic_clear_ns) {
//                clearText()
//            }
        }

        binding.btnSubmit.setAmountText(dataConfirm!!.payAmountString)
        binding.totalTagihan.text = dataConfirm!!.payAmountString

        dataAccount?.let {
            binding.tvNumberAccount.text = it.alias.ifEmpty { it.name }
            binding.tvNominalAccount.text = it.acoountString

            GeneralHelper.loadIconTransaction(
                this,
                it.imagePath,
                it.imageName,
                binding.ivRekening,
                R.drawable.img_card_bg)
        }
    }

    private fun checkDataPackageFromText(content: String): String {
        if(content == "Data Package") {
            dataConfirm?.let {
                val detailDataView = it.detailDataView.find { it.name == "Package Name" }

                return detailDataView?.value?: ""
            }
        }
        return content
    }

    override fun onSessionEnd(message: String?) {
        message?.let {
            if (message.contains(Constant.AKUN_TERBLOKIR_SALAH_PIN)) {
                GeneralHelperNewSkin.showErrorBlokir(this)
            } else {
                super.onSessionEnd(message)
            }
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        showBackConfirmationBottomSheet()
    }

    private fun showBackConfirmationBottomSheet() {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            fragmentManager = supportFragmentManager,
            imgDrawable = R.drawable.ic_sad_illustration,
            imgName = "ic_sad_illustration",
            titleTxt = GeneralHelper.getString(R.string.dialog_exit_title_wv_paket),
            subTitleTxt = GeneralHelper.getString(R.string.txt_back_from_confirmation),
            btnFirstFunction = {
                // Close the dialog
            },
            btnSecondFunction = {
                navigateBackToForm()
            },
            isClickableOutside = true,
            withBgSecondBtn = false,
            firstBtnTxt = GeneralHelper.getString(R.string.txt_lanjutkan_transaksi),
            secondBtnTxt = GeneralHelper.getString(R.string.kembali),
            showCloseButton = true,
            showPill = true
        )
    }

    private fun navigateBackToForm() {
        val intent = Intent(this, FormPulsaDataReskinActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        startActivity(intent)
        finish()
    }

    private fun findSaved(data: String, fromNumber: Boolean): SavedResponse?= dataSaved.find {
        if(!fromNumber) it.subtitle == data else it.title == data
    }

    private fun execPayment() {
        val param = PaymentNS(
            dataConfirm!!.referenceNumber,
            pin!!,
            dataAccount?.acoount!!,
            dataConfirm!!.pfmCategory.toString(),
            binding.bivSaveName.getText()
        )

        presenter.executeRequest(
            url = if(isFromFastMenu)
                GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp)
            else "PjBhVJD2oNh6zzQCIznEbX7TmHgDoFnuO92DaIukU4M=",
            requestParam = if(isFromFastMenu) FastPaymentOpenRevampRequest(
                presenter.getFastMenuRequest(),
                param.referenceNumber,
                param.pin,
                param.accountNumber,
                param.pfmCategory.toString(),
            ) else param,
            responseType = ReceiptRevampResponse::class.java,
        )
    }

    override fun onSendPinComplete(pin: String) {
        isCompletedPin = true
        this.pin = pin

        showProgress()
        execPayment()
    }

    override fun onLupaPin() {
    }

    fun mParameterModel(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal)
        parameterModel.defaultIcon = R.drawable.ic_default_pulsa
        return parameterModel
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Receipt -> {
                val receiptRevampResponse = res.data

                finish()
                TransactionProcessActivity.launchIntent(this,
                    isFromFastMenu, data = receiptRevampResponse, ReceiptType.OTHER
                )
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        when (exception) {
            is PulsaDataException.KnownError -> handlingError(exception.body)
            is PulsaDataException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {
                        showGeneralErrorDialog {
                            showProgress()
                        }
                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    private fun showGeneralErrorDialog(
        title: String = "Terjadi Kesalahan",
        message: String = "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
        imgName: String = "ic_sad_illustration",
        onRetry: (() -> Unit)? = null
    ) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_sad_illustration,
            imgName,
            title,
            message,
            btnFirstFunction = { onRetry?.invoke() },
            btnSecondFunction = { /* optional */ },
            false,
            firstBtnTxt = getString(R.string.retry),
            secondBtnTxt = getString(R.string.close),
            false,
            showCloseButton = true,
            showPill = true
        )
    }

    private fun handlingError(data: ResExceptionErr) {
        when (data.code) {
            PulsaErrorCode.EXCEPTION_93.code -> {
                showGeneralErrorDialog()
            }
            PulsaErrorCode.EXCEPTION_12.code -> {
                hideProgress()
                if(data.desc.contains("PIN")) {
                    showSnackbarErrorMessage(data.desc, ALERT_ERROR, this, false)
                } else {
                    showGeneralErrorDialog {
                    }
                }
            }
        }
    }
}