package id.co.bri.brimons.presenters.brizzi;

import android.util.Log;

import java.util.Arrays;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.brizzi.ICekBrizzi2Presenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.brizzi.ICekBrizzi2View;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.PendingBrizziResponse;
import id.co.bri.brimons.models.apimodel.request.CommitRequest;
import id.co.bri.brimons.models.apimodel.request.FastCommitBrizziRequest;
import id.co.bri.brimons.models.apimodel.request.FastInquiryBrizziTopUpRequest;
import id.co.bri.brimons.models.apimodel.request.FastValidateBrizziRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryBrizziTopUpRequest;
import id.co.bri.brimons.models.apimodel.request.ValidateRequest;
import id.co.bri.brimons.models.apimodel.request.ValidateTersimpanRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryBrizziResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;

import id.co.bri.brizzi.Brizzi;
import id.co.bri.brizzi.CardData;
import id.co.bri.brizzi.RCOptions;
import id.co.bri.brizzi.callbacks.BrizziCallback;
import id.co.bri.brizzi.callbacks.BrizziTrxCallback;
import id.co.bri.brizzi.exception.BrizziException;
import id.co.bri.brizzi.exception.ExceptionList;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class CekBrizzi2Presenter<V extends IMvpView & ICekBrizzi2View> extends MvpPresenter<V> implements ICekBrizzi2Presenter<V> {

    private static final String TAG = "CekBrizzi2Presenter";
    private String inquiryUrl = null;
    private String paymentUrl = null;
    private String validateUrl = null;
    protected Object inquiryRequest = null;
    protected Object commitRequest = null;
    protected Object validateRequest = null;
    private boolean isLoading, isLoadingContinue = false;
    private String descRespPayment = "-";
    private String codePayment = "";

    public CekBrizzi2Presenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    /**
     * [1]  Method yang digunakan untuk get Random Host CEK SALDO BRIZZI
     *
     * @param request
     * @param isFormFast
     */

    @Override
    public void getSetRandomHost(InquiryBrizziTopUpRequest request, boolean isFormFast) {
        if (isViewAttached()) {
            if (isFormFast) {
                inquiryRequest = new FastInquiryBrizziTopUpRequest(getFastMenuRequest(), request.getCardNumber(), request.getRandomString());
            } else {
                inquiryRequest = request;
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                            isLoading = false;
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            InquiryBrizziResponse inquiryBrizziResponse = response.getData(InquiryBrizziResponse.class);
                            getView().onSuccesGetRandomHost(inquiryBrizziResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            isLoading = false;
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    /**
     * [2] Method yang digunakan untuk PEMBAYARAN KE HOST
     *
     * @param pin                         PIN User
     * @param note                        Catatan
     * @param cardNumber                  Nomor Kartu BRIZZI
     * @param randomString                random string dari AAR
     * @param generalConfirmationResponse Balikan dari halaman Konfirmasi (halaman Input PIN)
     * @param isFormFast
     */
    @Override
    public void getCommitBrizzi(String pin, String note, String cardNumber, String randomString, GeneralConfirmationResponse generalConfirmationResponse, boolean isFormFast) {
        if (isViewAttached()) {
            getView().hideProgress();
            getView().showProgress();
            if (isFormFast) {
                commitRequest = new FastCommitBrizziRequest(getFastMenuRequest(), generalConfirmationResponse.getReferenceNumber(), pin, String.valueOf(generalConfirmationResponse.getPfmCategory()), note, cardNumber, randomString);
            } else {
                commitRequest = new CommitRequest(generalConfirmationResponse.getReferenceNumber(), pin, String.valueOf(generalConfirmationResponse.getPfmCategory()), note, cardNumber, randomString);
            }
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(paymentUrl, commitRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            isLoading = false;
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            descRespPayment = response.getDesc();
                            codePayment = response.getCode();
                            //save PFM
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    generalConfirmationResponse.getBillingDetail().getSubtitle())
                            );


                            PendingBrizziResponse inquiryBrizziResponse = response.getData(PendingBrizziResponse.class);
//                          commitContinuePres
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue()) || response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().onSuccesGetCommit(inquiryBrizziResponse);
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            descRespPayment = restResponse.getDesc();
                            codePayment = restResponse.getCode();
//                            reff = reff num
                            isLoading = false;
                            getView().hideProgress();
                            getView().onExceptionGagalDefault(restResponse.getDesc());
                        }
                    }));
        }
    }

    /**
     * [3] Method yang digunakan untuk VALIDASI ke HOST (FORCE UPDATE)
     *
     * @param request
     * @param isFormFast
     */

    @Override
    public void getValidateBrizzi(ValidateRequest request, boolean isFormFast) {
        if (isViewAttached()) {
            if (isFormFast) {
                validateRequest = new FastValidateBrizziRequest(getFastMenuRequest(), request.getCardNumber(), request.getRandomString(), request.getBalance(), request.getReff(), request.getReferenceNumber());
            } else {
                validateRequest = request;
            }
            getView().hideProgress();
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(validateUrl, validateRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();

                            try {
                                ValidateRequest validateRequestSimpan = null;
                                if(validateRequest instanceof FastValidateBrizziRequest){
                                    FastValidateBrizziRequest requestFast = (FastValidateBrizziRequest) validateRequest;
                                    validateRequestSimpan = new ValidateRequest(requestFast.getCardNumber(), requestFast.getRandomString(), requestFast.getBalance(), requestFast.getReff(), requestFast.getReferenceNumber());
                                } else {
                                    validateRequestSimpan = (ValidateRequest) validateRequest;
                                }

                                if(validateRequestSimpan!= null) {
                                    ValidateTersimpanRequest validateTersimpanRequest = new ValidateTersimpanRequest(validateRequestSimpan, CalendarHelper.getCurrentDateString());
                                    getBRImoPrefRepository().saveBrizziValidate(validateTersimpanRequest);
                                }
                            } catch (Exception e) {
                                if (!GeneralHelper.isProd()) {
                                    Log.e(TAG, "onFailureHttp: ", e);
                                }
                            }
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
//                            jika payment 00, validate 01 : hanya menampilkan snackbar dari validate
                            if (codePayment.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue()) && !response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                getView().onExceptionGagalDefault(response.getDesc());
                            }else{
//                              jika payment gagal, validate 01 : tampilkan double snackbar
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
    //                              muncul snackbar, tidak ke struck, kembali ke form, dan merubah status catatan aktivitas
    //                              muncul snackbar, tidak ke struck dan kembali ke form
                                    getView().onException01OnValidate(descRespPayment, response.getDesc());
                                }
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (codePayment.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                getView().onExceptionGagalDefault(restResponse.getDesc());
                            }else{
                                getView().onExceptionGagalDefault(descRespPayment);
                            }

                        }
                    }));
        }
    }


    public void setInquiryUrl(String inquiryUrl) {
        this.inquiryUrl = inquiryUrl;
    }

    public void setPaymentUrl(String paymentUrl) {
        this.paymentUrl = paymentUrl;
    }

    @Override
    public void setValidateUrl(String validateUrl) {
        this.validateUrl = validateUrl;
    }


    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {

                        }

                        @Override
                        public void onError(Throwable e) {
                            Log.d(TAG, "onError: " + e.toString());
                        }
                    })
            );
        }
    }

    @Override
    public void topUpOnlinePres(Brizzi brizzi, String topupAmount, boolean isFromFastMenu){
        if (isLoading){
            return;
        }
        isLoading = true;
        getView().hideProgress();
        getView().showProgress();
        brizzi.initTopupOnline(topupAmount, new BrizziCallback() {
            @Override
            public void onSuccess(CardData cardData) {
                getSetRandomHost(new InquiryBrizziTopUpRequest(cardData.getCardNumber(), cardData.getRandomSAM()), isFromFastMenu);
            }

            @Override
            public void onFailure(BrizziException e) {
                isLoading = false;
                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_membaca_kartu));
            }
        });
    }

    /**
     * Step pertama setelah dapat host rand dari service backend -> Check balance Brizzi
     */
    @Override
    public void continueTopUpOnlinePres(Brizzi brizzi, InquiryBrizziResponse inquiryBrizziResponse, String mPin, GeneralConfirmationResponse mbrivaResponse, boolean isFromFastMenu) {
        if (isLoadingContinue){
            return;
        }
        if (isViewAttached()){
            isLoadingContinue = true;
            try {
                brizzi.topupOnline(inquiryBrizziResponse.getKey() + inquiryBrizziResponse.getRcHost(), new BrizziCallback() {
                    @Override
                    public void onSuccess(CardData cardData) {
                        isLoadingContinue = false;
                        getCommitBrizzi(mPin, "", cardData.getCardNumber(), cardData.getRandomSAM(), mbrivaResponse, isFromFastMenu);
                        isLoading = true;
                    }

                    @Override
                    public void onFailure(BrizziException e) {
                        isLoading = false;
                        isLoadingContinue = false;
                        getView().hideProgress();
                        switch (e.getErrorCode()){
                            case ExceptionList.cardReadFailed :
                                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_gagal_terbaca));
                                break;
                            case ExceptionList.generalError:
                                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
                                break;
                            default:
                                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
                                break;
                        }
                    }
                });


            } catch (Exception e) {
                isLoading = false;
                isLoadingContinue = false;
                getView().hideProgress();
                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_membaca_kartu));
            }
        }

    }

    /**
     * Step kedua setelah dapat host rand dari service backend -> Commit Brizzi
     */
    @Override
    public void commitContinuePres(PendingBrizziResponse inquiryBrizziResponse, Brizzi brizzi, boolean isFromFastMenu) {
        if (isViewAttached()){
            getView().hideProgress();
            getView().showProgress();
            try {
//                if ==  Q4 || 404 || 04
                String keyParam = inquiryBrizziResponse.getKey() == null ? inquiryBrizziResponse.getRc_host() : inquiryBrizziResponse.getKey();
                String hostParam =
                        (inquiryBrizziResponse.getRc_host().equalsIgnoreCase("Q4")
                                || inquiryBrizziResponse.getRc_host().equalsIgnoreCase("44")
                                || inquiryBrizziResponse.getRc_host().equalsIgnoreCase("Q1")
                                || inquiryBrizziResponse.getRc_host().equalsIgnoreCase("404"))
                        ? RCOptions.RC_ERROR_TIMEOUT : keyParam + inquiryBrizziResponse.getRc_host();
                if (Arrays.asList(Constant.LIST_RC_NON_VALIDATE_GAGAL).contains(inquiryBrizziResponse.getRc_host())){
//                  jika RC host ada di LIST_RC_NON_VALIDATE_GAGAL tidak perlu commitTopupOnline maupun validate
                    getView().onExceptionGagalDefault(descRespPayment);
                }else {
                    brizzi.commitTopupOnline(hostParam, new BrizziTrxCallback() {
                        @Override
                        public void onSuccess(CardData cardData) {
//                        final step
                            isLoading = false;
                            getView().hideProgress();
    //                    ketika berhasil commit topup onine harus memanggil getvalidate brizzi, lalu buka receipt
                            String reffP = inquiryBrizziResponse.getReff().equalsIgnoreCase("") ? inquiryBrizziResponse.getReferenceNumber() : inquiryBrizziResponse.getReff();
                            getValidateBrizzi(new ValidateRequest(cardData.getCardNumber(), cardData.getValidateRandom(), cardData.getCardBalance(), reffP, inquiryBrizziResponse.getReferenceNumber()), isFromFastMenu);
                            getView().onSuccesGetValidate();
                        }

                        @Override
                        public void onValidate(BrizziException e, String cardNumber,String cardBalance, String validateRandom) {
                            String reffP = inquiryBrizziResponse.getReff().equalsIgnoreCase("") ? inquiryBrizziResponse.getReferenceNumber() : inquiryBrizziResponse.getReff();
                            isLoading = false;
                            getView().hideProgress();
                            getValidateBrizzi(new ValidateRequest(cardNumber, validateRandom, cardBalance, reffP, inquiryBrizziResponse.getReferenceNumber()), isFromFastMenu);
                        }

                        @Override
                        public void onFailure(BrizziException e) {
                            isLoading = false;
                            getView().hideProgress();
                            switch (e.getErrorCode()){
                                case ExceptionList.cardReadFailed :
                                    getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_gagal_terbaca));
                                    break;
                                case ExceptionList.generalError:
                                    getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
                                    break;
                                default:
                                    getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
                                    break;
                            }
                        }
                    });
                }
            } catch (Exception e) {
                isLoading = false;
                getView().hideProgress();
                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_update_saldo));
            }

        }
    }
}