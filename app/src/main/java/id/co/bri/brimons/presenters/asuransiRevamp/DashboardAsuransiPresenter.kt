package id.co.bri.brimons.presenters.asuransiRevamp

import id.co.bri.brimons.contract.IPresenter.asuransiRevamp.IDashboardAsuransiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.asuransiRevamp.IDashboardAsuransiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.WebviewBrilifeLamaRequest
import id.co.bri.brimons.models.apimodel.request.WebviewBrilifeRequest
import id.co.bri.brimons.models.apimodel.request.asuransirevamp.DetailInsuranceRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.WebviewBrilifeLamaResponse
import id.co.bri.brimons.models.apimodel.response.WebviewBrilifeResponse
import id.co.bri.brimons.models.apimodel.response.asuransi.DashboardAsuransiResponse
import id.co.bri.brimons.models.apimodel.response.asuransi.DetailAsuransiResponse
import id.co.bri.brimons.models.apimodel.response.asuransi.ListAsuransiResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DashboardAsuransiPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDashboardAsuransiPresenter<V> where V : IMvpView, V : IDashboardAsuransiView {

    private lateinit var mUrl: String
    private lateinit var mUrlPolis: String
    private lateinit var mUrlDetailInsurance: String
    private lateinit var mUrlProduct : String
    private lateinit var mUrlProductLama : String

    override fun setUrlDashboard(url: String) {
        mUrl = url
    }


    override fun getDataDashboard() {
        if (isViewAttached) {
            view!!.showSkeleton()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataForm(mUrl, seqNum).subscribeOn(
                    schedulerProvider.io()
            )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val dashboardAsuransiResponse = response.getData(
                                    DashboardAsuransiResponse::class.java
                            )
                            getView()!!.hideSkeleton()
                            getView()!!.onSuccessGetData(dashboardAsuransiResponse)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlList(urlList: String) {
        mUrlPolis=urlList
    }

    override fun getDataList() {
        if (isViewAttached) {
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataForm(mUrlPolis, seqNum).subscribeOn(
                    schedulerProvider.io()
            )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val listAsuransiResponse = response.getData(
                                    ListAsuransiResponse::class.java
                            )
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                getView()!!.onSuccessGetList(listAsuransiResponse)
                            }else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)){
                                getView()!!.onSuccess01()
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            }else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)){
                                getView()!!.onSuccess01()
                            }
                            else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlInsuranceDetail(url: String) {
        mUrlDetailInsurance =url
    }

    override fun getDataDetailInsurance(request: DetailInsuranceRequest) {
        if (isViewAttached) {
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(mUrlDetailInsurance,request, seqNum).subscribeOn(
                    schedulerProvider.io()
            )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val listAsuransiResponse = response.getData(
                                    DetailAsuransiResponse.InformationSection::class.java
                            )
                            getView()!!.onSuccessDetailInsurance(listAsuransiResponse)


                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            }else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)){
                                getView()!!.onSuccess01()
                            }
                            else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlProduct(urlWebview: String) {
        mUrlProduct = urlWebview
    }

    override fun getProduct(patnerId: String) {

        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val request = WebviewBrilifeRequest(patnerId)
            compositeDisposable.add(apiSource.getData(mUrlProduct, request, seqNum)
                    .subscribeOn(schedulerProvider.single())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val webviewBrilifeResponse = response.getData(WebviewBrilifeResponse::class.java)
                            getView().onSuccessWebview(webviewBrilifeResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else getView().onException(restResponse.desc)
                        }
                    }))
        }
    }

    override fun getProdukLama(patnerId: String?) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val request = WebviewBrilifeLamaRequest(patnerId!!.toInt())
            compositeDisposable.add(apiSource.getData(mUrlProductLama, request, seqNum)
                    .subscribeOn(schedulerProvider.single())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val webviewBrilifeResponse = response.getData(WebviewBrilifeLamaResponse::class.java)
                            getView().onSuccessWebviewLama(webviewBrilifeResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else getView().onException(restResponse.desc)
                        }
                    }))
        }
    }

    override fun setUrlProdukLama(urlProdukLama: String?) {
        mUrlProductLama = urlProdukLama!!
    }

}