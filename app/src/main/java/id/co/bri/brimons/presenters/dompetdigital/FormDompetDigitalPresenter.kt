package id.co.bri.brimons.presenters.dompetdigital

import id.co.bri.brimons.contract.IPresenter.dompetdigitalrevamp.IFormDompetDigitalRevPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimons.contract.IView.dompetdigitalrevamp.IFormDompetDigitalRevView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.enumconfig.JourneyType
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest
import id.co.bri.brimons.models.apimodel.response.*
import id.co.bri.brimons.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimons.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit


class FormDompetDigitalPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : BaseFormRevampPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormDompetDigitalRevPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormDompetDigitalRevView {

    override fun setUpdateItem(
        url: String,
        savedResponse: SavedResponse,
        position: Int,
        type: Int,
        journeyType: JourneyType?
    ) {
        if (url.isEmpty() || !isViewAttached || onLoad) {
            return
        }
        view?.showProgress()
        onLoad = true
        val s = savedResponse.value
        val str1 = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        val saveId = str1[0]
        val productId = str1[1]
        val updateSavedRequest = UpdateSavedRequest(saveId, productId)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        onLoad = false
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView().hideProgress()
                        getView().onSuccessUpdate(savedResponse, position, type)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquirySaved(isFromFastMenu: Boolean, walletCode: String, corpCode: String, purchaseNumber: String) {
        if (mUrlInquiry.isEmpty() && !isViewAttached) return

        view?.showProgress()

        val inquiryRequest = if (isFromFastMenu)
            InquiryDompetDigitalRequest(
                brImoPrefRepository.username,
                brImoPrefRepository.tokenKey,
                walletCode,
                corpCode,
                purchaseNumber
            )
        else
            InquiryDompetDigitalRequest(
                walletCode,
                corpCode,
                purchaseNumber
            )

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiry, inquiryRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()?.hideProgress()
                            val responsebriva = response.getData(
                                InquiryDompetDigitalResponse::class.java
                            )

                            getView()?.onSuccessGetInquiryDompet(
                                responsebriva,
                                mUrlConfirm,
                                mUrlPayment
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, true))
                                getView()?.onSessionEnd(restResponse.desc)
                            else getView()?.onException(restResponse.desc)
                        }
                    })
            )
    }

}