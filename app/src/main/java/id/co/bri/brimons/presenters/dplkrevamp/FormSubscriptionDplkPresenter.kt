package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IFormSubscriptionDplkPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IFormDataIuranDplkView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.ConfirmDplkRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.ConfirmRegistDplkResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormSubscriptionDplkPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
),
    IFormSubscriptionDplkPresenter<V> where V : IMvpView, V : IFormDataIuranDplkView {
    private var murlConfirmFtu = ""
    override fun setUrlConfirmFtuDplk(urlConfirmFtu: String) {
        murlConfirmFtu = urlConfirmFtu
    }

    override fun getDataConfirmFtuDplk(request: ConfirmDplkRequest) {
        if (isViewAttached) {
            view?.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(murlConfirmFtu, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.hideProgress()
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(ConfirmRegistDplkResponse::class.java)
                            getView()?.onSuccessConfirmFtuDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDefaultBalance() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold

        view?.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }
}