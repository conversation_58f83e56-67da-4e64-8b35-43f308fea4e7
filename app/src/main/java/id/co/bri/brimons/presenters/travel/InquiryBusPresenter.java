package id.co.bri.brimons.presenters.travel;


import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.travel.IInquiryBusPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.travel.IInquiryBusView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.GetPointRequest;
import id.co.bri.brimons.models.apimodel.request.PickRouteRequest;
import id.co.bri.brimons.models.apimodel.response.GetPointResponse;
import id.co.bri.brimons.models.apimodel.response.PickRouteResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryBusPresenter <V extends IMvpView & IBaseFormView & IInquiryBusView> extends BaseFormPresenter<V>
        implements IInquiryBusPresenter<V> {

    String inquiryUrl, pointUrl;

    public InquiryBusPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.inquiryUrl = url;
    }

    @Override
    public void setUrlGetPoint(String url) {
        this.pointUrl = url;
    }

    @Override
    public void getDetailBus(PickRouteRequest pickRouteRequest) {
        inquiryRequest = pickRouteRequest;
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                PickRouteResponse pickRouteResponse = response.getData(PickRouteResponse.class);
                                getView().onSuccessGetDetailBus(pickRouteResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getPoint(GetPointRequest getPointRequest) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(pointUrl, getPointRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GetPointResponse getPointResponse = response.getData(GetPointResponse.class);
                                getView().onSuccessGetPoint(getPointResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }


}