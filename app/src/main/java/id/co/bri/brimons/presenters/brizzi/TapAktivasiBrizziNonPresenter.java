package id.co.bri.brimons.presenters.brizzi;

import android.util.Log;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.brizzi.ITapAktivasiBrizziNonPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.brizzi.ITapAktivasiBrizziNonView;

import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;

import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.BrizziAktivasiDepositRequest;
import id.co.bri.brimons.models.apimodel.request.FastBrizziAktivasiRequest;
import id.co.bri.brimons.models.apimodel.request.FastInquiryBrizziTopUpRequest;
import id.co.bri.brimons.models.apimodel.request.FastValidateBrizziRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryBrizziTopUpRequest;
import id.co.bri.brimons.models.apimodel.request.ValidateRequest;
import id.co.bri.brimons.models.apimodel.request.ValidateTersimpanRequest;
import id.co.bri.brimons.models.apimodel.response.AktivasiBrizziResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryBrizziResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import id.co.bri.brizzi.Brizzi;
import id.co.bri.brizzi.CardData;
import id.co.bri.brizzi.RCOptions;
import id.co.bri.brizzi.callbacks.BrizziCallback;
import id.co.bri.brizzi.callbacks.BrizziTrxCallback;
import id.co.bri.brizzi.exception.BrizziException;
import id.co.bri.brizzi.exception.ExceptionList;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TapAktivasiBrizziNonPresenter<V extends IMvpView & ITapAktivasiBrizziNonView> extends MvpPresenter<V> implements ITapAktivasiBrizziNonPresenter<V> {

    private static final String TAG = "TapAktivasiBrizziNonPre";
    private String inquiryUrl = null;
    private String paymentUrl = null;
    private String validateUrl = null;
    protected Object inquiryRequest = null;
    protected Object paymentRequest = null;
    protected Object validateRequest = null;

    public TapAktivasiBrizziNonPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getAktivasiBrizzi(Brizzi brizzi, InquiryBrizziTopUpRequest request, boolean isFormFast) {
        if (isViewAttached()) {
            getView().showProgress();
            if (isFormFast) {
                inquiryRequest = new FastInquiryBrizziTopUpRequest(getFastMenuRequest(), request.getCardNumber(), request.getRandomString());
            } else {
                inquiryRequest = request;
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            InquiryBrizziResponse aktivasiBrizziResponse = response.getData(InquiryBrizziResponse.class);
                            continueUpdateBalancePres(brizzi, aktivasiBrizziResponse, isFormFast);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }

    }

    @Override
    public void getCommitBrizzi(BrizziAktivasiDepositRequest request, boolean isFormFast) {
        if (isViewAttached()) {
            getView().showProgress();


            if (isFormFast) {
                inquiryRequest = new FastBrizziAktivasiRequest(getFastMenuRequest(), request.getBalance(), request.getCardNumber(), request.getRandomString());
            } else {
                inquiryRequest = request;
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(paymentUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            AktivasiBrizziResponse inquiryBrizziResponse = response.getData(AktivasiBrizziResponse.class);
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue()) || response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().onSuccesGetCommitAktivasi(inquiryBrizziResponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12Validate(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getValidateBrizzi(ValidateRequest request, boolean isFormFast) {
        if (isViewAttached()) {
            getView().showProgress();

            if (isFormFast) {
                validateRequest = new FastValidateBrizziRequest(getFastMenuRequest(), request.getCardNumber(), request.getRandomString(), request.getBalance(), request.getReff(), request.getReferenceNumber());
            } else {
                validateRequest = request;
            }
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(validateUrl, validateRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                            try {
                                ValidateRequest validateRequestSimpan = null;
                                if (validateRequest instanceof FastValidateBrizziRequest) {
                                    FastValidateBrizziRequest requestFast = (FastValidateBrizziRequest) validateRequest;
                                    validateRequestSimpan = new ValidateRequest(requestFast.getCardNumber(), requestFast.getRandomString(), requestFast.getBalance(), requestFast.getReff(), requestFast.getReferenceNumber());
                                } else {
                                    validateRequestSimpan = (ValidateRequest) validateRequest;
                                }

                                if (validateRequestSimpan != null) {
                                    ValidateTersimpanRequest validateTersimpanRequest = new ValidateTersimpanRequest(validateRequestSimpan, CalendarHelper.getCurrentDateString());
                                    getBRImoPrefRepository().saveBrizziValidate(validateTersimpanRequest);
                                }
                            } catch (Exception e) {
                                if (!GeneralHelper.isProd()) {
                                    Log.e(TAG, "onFailureHttp: ", e);
                                }
                            }
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().onException01(response.getDesc());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException01(restResponse.getDesc());
                        }
                    }));
        }
    }


    public void setInquiryUrl(String inquiryUrl) {
        this.inquiryUrl = inquiryUrl;
    }

    @Override
    public void setPaymentUrl(String paymentUrl) {
        this.paymentUrl = paymentUrl;
    }

    @Override
    public void setValidateUrl(String validateUrl) {
        this.validateUrl = validateUrl;
    }

    @Override
    public void initUpdateBalancePres(Brizzi brizzi, boolean isFromFastMenu) {
        getView().showProgress();
        brizzi.initUpdateBalance(new BrizziCallback() {
            @Override
            public void onSuccess(CardData cardData) {
                getView().hideProgress();
                getAktivasiBrizzi(brizzi, new InquiryBrizziTopUpRequest(cardData.getCardNumber(), cardData.getRandomSAM()), isFromFastMenu);
            }

            @Override
            public void onFailure(BrizziException e) {
                getView().hideProgress();
                if (e.getErrorCode().equalsIgnoreCase(ExceptionList.cardReadFailed)) {
                    getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_gagal_terbaca));
                } else {
                    getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
                }
            }
        });
    }

    /**
     * Step pertama setelah dapat host rand dari service backend -> Check balance Brizzi
     */
    @Override
    public void continueUpdateBalancePres(Brizzi brizzi, InquiryBrizziResponse inquiryBrizziResponse, boolean isFromFastMenu) {
        getView().showProgress();
        brizzi.updateBalance(inquiryBrizziResponse.getKey() + inquiryBrizziResponse.getRcHost(), new BrizziCallback() {
            @Override
            public void onSuccess(CardData cardData) {
                getView().hideProgress();
                getCommitBrizzi(new BrizziAktivasiDepositRequest(cardData.getCardBalance(), cardData.getCardNumber(), cardData.getRandomSAM()), isFromFastMenu);
            }

            @Override
            public void onFailure(BrizziException e) {
                getView().hideProgress();
                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca));
            }
        });
    }

    /**
     * Step kedua setelah dapat host rand dari service backend -> Commit Brizzi
     */
    @Override
    public void commitContinuePres(AktivasiBrizziResponse aktivasiBrizziResponse, Brizzi brizzi, boolean isFromFastMenu) {
        getView().showProgress();
        try {
            String hostParam = (aktivasiBrizziResponse.getRcHost().equalsIgnoreCase("Q4") || aktivasiBrizziResponse.getRcHost().equalsIgnoreCase("44") || aktivasiBrizziResponse.getRcHost().equalsIgnoreCase("404")) ? RCOptions.RC_ERROR_TIMEOUT : aktivasiBrizziResponse.getKey() + aktivasiBrizziResponse.getRcHost();
            brizzi.commitUpdateBalance(hostParam, new BrizziTrxCallback() {
                @Override
                public void onSuccess(CardData cardData) {
                    getView().hideProgress();
                    getView().onSuccesGetValidateAtktivasi();
                }

                @Override
                public void onValidate(BrizziException e, String cardNumber, String cardBalance, String validateRandom) {
                    getView().hideProgress();
                    String reffP = (aktivasiBrizziResponse.getReff() != null && !aktivasiBrizziResponse.getReff().trim().isEmpty()) ? aktivasiBrizziResponse.getReff() : aktivasiBrizziResponse.getReferenceNumber();
                    getValidateBrizzi(new ValidateRequest(brizzi.getCardData().getCardNumber(), brizzi.getCardData().getValidateRandom(), brizzi.getCardData().getCardBalance(), reffP, aktivasiBrizziResponse.getReferenceNumber()), isFromFastMenu);
                }

                @Override
                public void onFailure(BrizziException e) {
                    getView().hideProgress();
                    getValidateBrizzi(new ValidateRequest(brizzi.getCardData().getCardNumber(), brizzi.getCardData().getValidateRandom(), brizzi.getCardData().getCardBalance(), aktivasiBrizziResponse.getReff(), aktivasiBrizziResponse.getReferenceNumber()), isFromFastMenu);
                    switch (e.getErrorCode()) {
                        case ExceptionList.cardReadFailed:
                            getView().onException93("Kartu Tidak Terbaca , Ulangi");
                            break;
                        case ExceptionList.generalError:
                            getView().onException93("Gagal Koneksi");
                            break;
                        default:
                            getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
                            break;
                    }
                }
            });
        } catch (Exception e) {
            getView().hideProgress();
            getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_update_saldo));
        }
    }

    @Override
    public void commitUpdateBalancePres(Brizzi brizzi, String hostResponse, boolean isFromFastMenu) {
        getView().showProgress();
        brizzi.commitUpdateBalance(RCOptions.RC_ERROR + RCOptions.RC_ERROR, new BrizziTrxCallback() {
            @Override
            public void onSuccess(CardData cardData) {
                getView().hideProgress();
                getValidateBrizzi(new ValidateRequest(cardData.getCardNumber(), cardData.getValidateRandom(), cardData.getCardBalance(), "", ""), isFromFastMenu);
            }

            @Override
            public void onValidate(BrizziException e, String cardNumber, String cardBalance, String validateRandom) {
                getView().hideProgress();
                getValidateBrizzi(new ValidateRequest(brizzi.getCardData().getCardNumber(), brizzi.getCardData().getValidateRandom(), brizzi.getCardData().getCardBalance(), "", ""), isFromFastMenu);
            }

            @Override
            public void onFailure(BrizziException e) {
                getView().hideProgress();
                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi));
            }
        });
    }


}