package id.co.bri.brimons.presenters.qrmpm;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.qrmpm.IQrMPMCodePresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.qrmpm.IQrMPMCodeView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.observer.ApiObserverQR;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.BaseFormRequest;
import id.co.bri.brimons.models.apimodel.response.GenerateQrResponse;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class QrMPMCodePresenter<V extends IMvpView & IQrMPMCodeView> extends MvpPresenter<V> implements IQrMPMCodePresenter<V> {

    private static final String TAG = "GeneralConfirmationPres";

    protected String urlPayment;
    protected String deleteUrl;
    protected Object requestPayment;
    protected int timer;

    public QrMPMCodePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getGetAutoQR(boolean fromFast) {
        if (urlPayment == null) {
            Log.d(TAG, "getDataPayment: urlInformasi payment null");
            return;
        }

        if (!isViewAttached()) {
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {
            //initiate param with getter from view
            if (fromFast)
                requestPayment = getFastMenuRequest();
            else
                requestPayment = new BaseFormRequest("");

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlPayment, requestPayment, seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserverQR(seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            try {
                                PendingResponse brivaResponse = response.getData(PendingResponse.class);
                                GenerateQrResponse generateQrResponse = response.getData(GenerateQrResponse.class);

                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    getView().onSuccesGetStatus(brivaResponse);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    getView().onGenerateRequest(generateQrResponse);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    getView().onException02(response.getDesc());
                                }
                            } catch (Exception e) {
                                getView().onException("Koneksi Terputus");
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void getDeleteQr(boolean fromFast) {
        if (deleteUrl == null || !isViewAttached()) {
            Log.d(TAG, "getData: form null");
            return;
        }

        if (fromFast)
            requestPayment = getFastMenuRequest();
        else
            requestPayment = new BaseFormRequest("");

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(deleteUrl, requestPayment, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
//                                getView().isHideSkeleton(true);
                                getView().onDeleteSuccess();

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
//                                else if (restResponse.getCode().equalsIgnoreCase("12"))
//                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void setDeleteUrl(String deleteUrl) {
        this.deleteUrl = deleteUrl;
    }
}