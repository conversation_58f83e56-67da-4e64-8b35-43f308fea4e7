package id.co.bri.brimons.presenters.transferVallas;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.transferVallas.IConfirmationKonversiValasPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transferVallas.IConfirmationKonversiValasView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastPaymentRequest;
import id.co.bri.brimons.models.apimodel.request.PaymentRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class ConfirmationKonversiValas<V extends IMvpView & IConfirmationKonversiValasView> extends MvpPresenter<V> implements IConfirmationKonversiValasPresenter<V> {

    private static final String TAG = "GeneralConfirmationPres";

    protected String urlPayment;
    protected Object paymentRequest;

    public ConfirmationKonversiValas(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataPayment(String pin, String note, GeneralConfirmationResponse generalConfirmationResponse, boolean fromfastMenu) {
        if (urlPayment == null) {
            Log.d(TAG, "getDataPayment: urlInformasi payment null");
            return;
        }

        if (!isViewAttached()) {
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {

            getView().showProgress();
            if (fromfastMenu)
                paymentRequest = new FastPaymentRequest(getFastMenuRequest(), generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString(), note);
            else
                paymentRequest = new PaymentRequest(generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString(), note);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserverKonfirmasi(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                                PendingResponse brivaResponse = response.getData(PendingResponse.class);
                                if(brivaResponse.getImmediatelyFlag())
                                    onSaveTransaksiMasukPfm(generateTransaksiModel(
                                            generalConfirmationResponse.getPfmCategory(),
                                            generalConfirmationResponse.getPfm_amount_masuk().longValue(),
                                            generalConfirmationResponse.getReferenceNumber(),
                                            generalConfirmationResponse.getPfm_description_masuk(),
                                            DbConfig.TRX_IN)
                                    );
                                    onSaveTransaksiPfm(generateTransaksiModel(
                                            generalConfirmationResponse.getPfm_category_keluar(),
                                            generalConfirmationResponse.getPfm_amount_keluar().longValue(),
                                            generalConfirmationResponse.getReferenceNumber(),
                                            generalConfirmationResponse.getPfm_description_keluar(),
                                            DbConfig.TRX_OUT)
                                    );


                                getView().onSuccessGetPayment(brivaResponse);

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("05"))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase("93"))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }


    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName,String trxType) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    trxType,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            if (!GeneralHelper.isProd()){
                Log.d(TAG, "masuk sini");
            }
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {


                        }

                        @Override
                        public void onError(Throwable e) {
                            if (!GeneralHelper.isProd()){
                                Log.d(TAG, "onError: " + e.toString());
                            }

                        }
                    })
            );
        }
    }

    public void onSaveTransaksiMasukPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {
                        }

                        @Override
                        public void onError(Throwable e) {
                            if (!GeneralHelper.isProd()){
                                Log.d(TAG, "onError: " + e.toString());
                            }

                        }
                    })
            );
        }
    }


    @Override
    public void start() {
        super.start();
        setDisablePopup(true);
    }

    @Override
    public void stop() {
        super.stop();
    }
}