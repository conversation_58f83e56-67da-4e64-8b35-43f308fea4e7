package id.co.bri.brimons.presenters.smarttransfer

import id.co.bri.brimons.contract.IPresenter.smarttransfer.IDashboardSmartTransferPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.smarttransfer.IDashboardSmartTransferView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.smarttransfer.ConfirmAccountBindingSmartTransferRequest
import id.co.bri.brimons.models.apimodel.request.smarttransfer.SmartTransferDeleteAccBindRequest
import id.co.bri.brimons.models.apimodel.request.smarttransfer.SmartTransferManageUserConsentRequest
import id.co.bri.brimons.models.apimodel.request.smarttransfer.SmartTransferUpdateAcountBindRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.smarttransfer.BankAccountList
import id.co.bri.brimons.models.apimodel.response.smarttransfer.SmartTransferAccountListConsentResponse
import id.co.bri.brimons.models.apimodel.response.smarttransfer.SmartTransferUserConsentData
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DashboardSmartTransferPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDashboardSmartTransferPresenter<V> where V : IMvpView, V : IDashboardSmartTransferView {

            var urlUserConsent : String = ""
            var urlUserUpdateUserConsent : String = ""
            var urlDeleteUserConsent : String = ""
            var urlUserUpdateAccBind : String = ""
            var urlConfirmAccountBindingSmartTransfer: String = ""
            var urlAccountListConsent: String = ""

    override fun getUserConsent() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getDataTanpaRequest(urlUserConsent, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse?) {
                            getView().hideProgress()
                            val responses = response?.getData(
                                SmartTransferUserConsentData::class.java
                            )
                            if (responses != null) {
                                getView().onSuccessUserConsent(responses)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse?) {
                            getView().hideProgress()
                            getView().onException(restResponse?.desc)
                        }
                    })
            )
        }
    }

    override fun updateAccBind(briAccount: String, nonBriAccount: String, action: Boolean, status: Boolean, dest: String?, userConsent: Boolean, nonBriCode: String?) {
        if (isViewAttached) {
            view.showProgress()

            val request = SmartTransferUpdateAcountBindRequest(
                briAccount,
                nonBriAccount,
                action,
                status,
                dest,
                userConsent,
                nonBriCode
            )
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlUserUpdateAccBind, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse?) {
                            getView().hideProgress()
                            val responses = response?.getData(
                                BankAccountList::class.java
                            )
                            if (responses != null) {
                                if (action) {
                                    if (userConsent) {
                                        getView().onSuccessUpdateSwitchAcc(responses)
                                    } else getView().onSuccessUpdateAccBind(responses)
                                } else {
                                    getView().onSuccessUpdateSwitchAcc(responses)
                                }
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse?) {
                            getView().hideProgress()
                            getView().onException(restResponse?.desc)
                        }
                    })
            )
        }
    }

    override fun manageUserConsent(isDisable: Boolean) {
        if (isViewAttached) {
            view.showProgress()

            val request = SmartTransferManageUserConsentRequest(isDisable)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlUserUpdateUserConsent, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse?) {
                            getView().hideProgress()
                            val responses = response?.getData(
                                BankAccountList::class.java
                            )
                            if (responses != null) {
                                getView().onSuccesUpdateUserConsent(responses)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse?) {
                            getView().hideProgress()
                            getView().onException(restResponse?.desc)
                        }
                    })
            )
        }
    }

    override fun deleteAccBind(nonBriAccount: String?, briAccount: String?, nonBriCode: String?) {
        if (isViewAttached) {
            view.showProgress()

            val request = SmartTransferDeleteAccBindRequest(nonBriAccount, briAccount, nonBriCode)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlDeleteUserConsent, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                            getView().hideProgress()
                        }

                        override fun onApiCallSuccess(response: RestResponse?) {
                            getView().hideProgress()
                            val responses = response?.getData(
                                BankAccountList::class.java
                            )
                            if (responses != null) {
                                getView().onSuccessDeleteBind(responses)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse?) {
                            getView().hideProgress()
                            getView().onException(restResponse?.desc)
                        }
                    })
            )
        }
    }

    override fun getAccountListConsent() {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getDataForm(urlAccountListConsent, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val accountListConsentResponse = response.getData(
                                SmartTransferAccountListConsentResponse::class.java
                            )
                            getView().onSuccessAccountListConsent(accountListConsentResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun confirmAccountBindingSmartTransfer(confirmBind: ConfirmAccountBindingSmartTransferRequest) {
        if (isViewAttached()) {
            getView().showProgress()

            val seqNum = getBRImoPrefRepository().getSeqNumber()
            getCompositeDisposable().add(getApiSource().getData(
                urlConfirmAccountBindingSmartTransfer,
                confirmBind,
                seqNum
            )
                .subscribeOn(getSchedulerProvider().io())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val responses = response.getData(BankAccountList::class.java)
                        if (responses != null) {
                            getView().onSuccessConfirmAccountBindingSmartTransfer(responses)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                })
            )
        }
    }

    override fun setUserConsentUrl(url: String) {
        urlUserConsent = url
    }

    override fun setUpdateUserConsentUrl(url: String) {
        urlUserUpdateUserConsent = url
    }

    override fun setDeleteAccBindUrl(url: String) {
        urlDeleteUserConsent = url
    }

    override fun setUpdateAccBindUrl(url: String) {
        urlUserUpdateAccBind = url
    }

    override fun setBindingSmartTransferUrl(url: String) {
        urlConfirmAccountBindingSmartTransfer = url
    }

    override fun setAccListConsentUrl(url: String) {
        urlAccountListConsent = url
    }
}