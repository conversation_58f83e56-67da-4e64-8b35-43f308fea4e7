package id.co.bri.brimons.presenters.ssc;


import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.ssc.IPengaduanTrxGagalPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.ssc.IPengaduanTrxGagalView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InformasiSscRequest;
import id.co.bri.brimons.models.apimodel.request.MutasiSscRequest;
import id.co.bri.brimons.models.apimodel.request.RevokeSessionRequest;
import id.co.bri.brimons.models.apimodel.request.SscMutasiDefaultRequest;
import id.co.bri.brimons.models.apimodel.response.TokenVoipResponse;
import id.co.bri.brimons.models.apimodel.response.ssc.ComplaintGroupResponse;
import id.co.bri.brimons.models.apimodel.response.ssc.ComplaintInformasiResponse;
import id.co.bri.brimons.models.apimodel.response.ssc.ComplaintMutasiResponse;
import id.co.bri.brimons.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PengaduanTrxGagalPresenter<V extends IMvpView & IPengaduanTrxGagalView>
        extends MvpPresenter<V> implements IPengaduanTrxGagalPresenter<V> {

    protected String urlRekening;
    protected String urlMutasiDefault;
    protected String urlMutasi;
    protected String urlInformasi;
    protected String urlPinjamanListForm;
    protected String urlVoip;
    protected String urlRevoke;
    protected String voipSession;

    public PengaduanTrxGagalPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void urlRekening(String urlRekening) {
        this.urlRekening = urlRekening;
    }

    @Override
    public void urlMutasiDefault(String urlMutasiDefault) {
        this.urlMutasiDefault = urlMutasiDefault;
    }

    @Override
    public void urlMutasi(String urlMutasi) {
        this.urlMutasi = urlMutasi;
    }

    @Override
    public void urlInformasi(String urlInformasi) {
        this.urlInformasi = urlInformasi;
    }

    @Override
    public void urlPinjamanListForm(String urlPinjamanListForm) {
        this.urlPinjamanListForm = urlPinjamanListForm;
    }

    @Override
    public void urlVoip(String urlVoip) {
        this.urlVoip = urlVoip;
    }

    @Override
    public void urlRevoke(String urlRevoke) {
        this.urlRevoke = urlRevoke;
    }

    @Override
    public void getDataRekening() {
        if (isViewAttached()) {

            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlRekening, seq)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    ListRekeningResponse listRekeningResponse = response.getData(ListRekeningResponse.class);
                                    getView().onSuccessGetAccount(listRekeningResponse.getAccount());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onExceptionErrorGetAccount(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void sendDataMutasiDefault(String id) {
        if (isViewAttached()) {

            SscMutasiDefaultRequest defaultRequest = new SscMutasiDefaultRequest(id);
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlMutasiDefault, defaultRequest, seq)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ComplaintMutasiResponse complaintMutasiResponse = response.getData(ComplaintMutasiResponse.class);
                                        getView().onSuccessGetMutasi(complaintMutasiResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        getView().onSuccessNotFound();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void sendDataMutasi(MutasiSscRequest mutasiSscRequest) {
        if (isViewAttached()) {

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlMutasi, mutasiSscRequest, seq)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ComplaintMutasiResponse complaintMutasiResponse = response.getData(ComplaintMutasiResponse.class);
                                        getView().onSuccessGetMutasi(complaintMutasiResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        getView().onSuccessNotFound();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void sendDataInformasi(InformasiSscRequest informasiSscRequest, ComplaintMutasiResponse.Transaction transaction) {
        if (isViewAttached()) {

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlInformasi, informasiSscRequest, seq)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    ComplaintInformasiResponse complaintInformasiResponse = response.getData(ComplaintInformasiResponse.class);
                                    getView().onSuccessGetInfo(complaintInformasiResponse, transaction);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void getPinjamanListForm(ComplaintInformasiResponse complaintInformasiResponse, ComplaintMutasiResponse.Transaction transaction) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlPinjamanListForm, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    ComplaintGroupResponse complaintResponse = response.getData(ComplaintGroupResponse.class);
                                    getView().getDataSuccessPinjamanList(complaintResponse.getAccount(), complaintInformasiResponse, transaction);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }

    @Override
    public void getVoip() {
        if (urlVoip.isEmpty() || !isViewAttached()) return;
        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(urlVoip, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                TokenVoipResponse voipResponse = response.getData(
                                        TokenVoipResponse.class
                                );
                                getView().onSuccessVoip(voipResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));

    }

    @Override
    public void revokeSession(RevokeSessionRequest request) {
        if (urlRevoke.isEmpty() || !isViewAttached()) return;
        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(urlRevoke, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onVoipCallEnd();
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getView().onVoipCallEnd();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().onVoipCallEnd();
                            }
                        }));
    }

    @Override
    public void setVoipSession(String voipSession) {
        this.voipSession = voipSession;
    }

    @Override
    public RevokeSessionRequest request() {
        return new RevokeSessionRequest(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey(), voipSession);
    }
}