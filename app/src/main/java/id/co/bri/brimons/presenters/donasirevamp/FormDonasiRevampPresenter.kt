package id.co.bri.brimons.presenters.donasirevamp

import id.co.bri.brimons.contract.IPresenter.donasirevamp.IFormDonasiRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.donasirevamp.IFormDonasiRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.donasirevamp.InquiryDonasiRevampRequest
import id.co.bri.brimons.models.apimodel.response.*
import id.co.bri.brimons.models.apimodel.response.donasirevamp.FormDonasiRevampResponse
import id.co.bri.brimons.models.apimodel.response.donasirevamp.InquiryDonasiRevampResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormDonasiRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    IFormDonasiRevampPresenter<V> where V : IMvpView, V : IFormDonasiRevampView {

    private var mUrlForm = ""

    private var mUrlInquiry = ""

    override fun setUrlInquiry(url: String) {
        this.mUrlInquiry = url
    }

    override fun setUrlForm(url: String) {
        this.mUrlForm = url
    }

    override fun getDataForm() {

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataForm(mUrlForm, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(restResponse: RestResponse) {
                        if (restResponse.data != null) {
                            val response =
                                restResponse.getData(FormDonasiRevampResponse::class.java)
                            getView().onSuccessResponseForm(response)
                        }
                        getView().hideProgress()


                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView().onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, true)) {
                            getView().onException99(restResponse.desc)
                        } else getView().onException(restResponse.desc)
                    }
                })
        )
    }

    override fun getDataInquiry(inquiryRequest: InquiryDonasiRevampRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiry, inquiryRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(restResponse: RestResponse) {
                            if (restResponse.data != null) {
                                val response =
                                    restResponse.getData(InquiryDonasiRevampResponse::class.java)
                                getView().onSuccessResponseInquiry(response)
                            }
                            getView().hideProgress()
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, true)) {
                                getView().onException99(restResponse.desc)
                            } else getView().onException(restResponse.desc)
                        }
                    })
            )
    }


}