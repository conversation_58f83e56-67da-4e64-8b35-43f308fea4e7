package id.co.bri.brimons.presenters.splitbill

import android.os.Handler
import android.os.Looper
import id.co.bri.brimons.contract.IPresenter.splitbill.ISplitBillEditFormPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.splitbill.ISplitBillEditFormView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.splitbill.EditItemDetailRequest
import id.co.bri.brimons.models.apimodel.request.splitbill.ProcessSplitBillRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.ProcessSplitBillResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.SplitBillDetailResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.TempBillViewModel
import id.co.bri.brimons.models.splitbill.SplitBillEditFormItemViewModel
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import java.util.Date

class SplitBillEditFormPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ), ISplitBillEditFormPresenter<V> where V : IMvpView?, V : ISplitBillEditFormView? {

    private val eventButtonValidationHandler = Handler(Looper.getMainLooper())

    private val billModel = TempBillViewModel()

    private var productItemsViews =
        mutableListOf<SplitBillEditFormItemViewModel>()

    private var itemData: MutableList<EditItemDetailRequest> = mutableListOf()

    private var mUrlProcess = ""
    private var mUrlDraft = ""
    private var mBillId = 0
    private var mIsFromDraft = false

    private var requestProcess: ProcessSplitBillRequest? = null

    private var mBillName = ""
    private var mBillDate = ""

    override fun setUrlProcessBill(urlProcess: String) {
        mUrlProcess = urlProcess
    }

    override fun setUrlDraftDetails(urlDraftDetails: String) {
        mUrlDraft = urlDraftDetails
    }

    override fun setItems(
        splitBillDetailResponse: SplitBillDetailResponse,
        mIsFromHistory: Boolean
    ) {
        if (splitBillDetailResponse.billModel != null &&
            splitBillDetailResponse.billId != 0 &&
            splitBillDetailResponse.billModel.detail?.items != null
        ) {

            mBillId = splitBillDetailResponse.billId
            mIsFromDraft = mIsFromHistory

            productItemsViews = splitBillDetailResponse.billModel.detail?.items?.map {
                SplitBillEditFormItemViewModel(
                    id = Math.random().toLong(),
                    name = it.name,
                    quantity = it.quantity,
                    price = it.price
                )
            }?.toMutableList() ?: mutableListOf()

            view?.apply {
                if (!productItemsViews.isNullOrEmpty()) {
                    showProductItems(productItemsViews)
                }

                updateBillName(splitBillDetailResponse.billModel.detail?.billName.orEmpty())
                updateBillDate(splitBillDetailResponse.billModel.detail?.transactionDate.orEmpty())
                splitBillDetailResponse.let {
                    updateSubTotalAmount(it.amountDetail[0].value)
                    updateTaxAmount(it.amountDetail[1].value)
                    updateServiceAmount(it.amountDetail[2].value)
                    updateOtherAmount(it.amountDetail[3].value)
                    updateTotalAmount(it.amountDetail[4].value)
                    billModel.apply {
                        subTotal = it.amountDetail[0].value
                        taxAmount = it.amountDetail[1].value
                        servicesAmount = it.amountDetail[2].value
                        otherAmount = it.amountDetail[3].value
                        totalBill = it.amountDetail[4].value
                    }
                }
            }
        }
    }

    override fun addItem() {
        productItemsViews.add(
            SplitBillEditFormItemViewModel(
                id = Date().time,
                name = "",
                quantity = 0,
                price = 0
            )
        )
        view?.showProductItems(productItemsViews)
    }

    override fun onBillNameChanged(billName: String) {
        billModel.billName = billName
        mBillName = billName
        doValidateButtonSubmit()
        onBillDataEmpty()
    }

    override fun onBillDateChanged(billDate: String) {
        billModel.billDate = billDate
        mBillDate = billDate
    }

    override fun onItemChanged(itemModel: SplitBillEditFormItemViewModel) {
        calculateSubTotal()
    }

    override fun onTaxAmountChanged(amount: Long) {
        billModel.taxAmount = amount
        calculateTotalAmount()
    }

    override fun onServicesAmountChanged(amount: Long) {
        billModel.servicesAmount = amount
        calculateTotalAmount()
    }

    override fun onTotalAmountChanged(amount: Long) {
        billModel.apply {
            totalBill = amount
            otherAmount = totalBill - (subTotal + taxAmount + servicesAmount)
        }
        view?.updateOtherAmount(billModel.otherAmount)
        view?.shouldEnableSubmitButton(
            billModel.billName.isNotEmpty() &&
                    billModel.subTotal != 0L &&
                    billModel.totalBill != 0L &&
                    !productItemsViews.any { it.isFieldsEmpty() }
        )
    }

    override fun onDeleteItemProduct(itemModel: SplitBillEditFormItemViewModel) {
        productItemsViews.removeAt(productItemsViews.indexOfFirst { itemModel.id == it.id })
        view?.showProductItems(productItemsViews)
        calculateSubTotal()
    }

    override fun doValidateButtonSubmit(itemModel: SplitBillEditFormItemViewModel?) {
        eventButtonValidationHandler.removeCallbacksAndMessages(null)
        eventButtonValidationHandler.postDelayed({
            itemModel?.let {
                val index = productItemsViews.indexOfFirst { it.id == itemModel.id }
                if (index != -1) {
                    val oldItem = productItemsViews[index]
                    productItemsViews[index] = oldItem.copy(
                        name = itemModel.name,
                        price = itemModel.price,
                        quantity = itemModel.quantity
                    )
                }
            }

            billModel.subTotal = productItemsViews.sumOf { it.price }
            calculateTotalAmount()
            view?.updateSubTotalAmount(billModel.subTotal)

            view?.shouldEnableSubmitButton(
                billModel.billName.isNotEmpty() &&
                        billModel.subTotal != 0L &&
                        billModel.totalBill != 0L &&
                        !productItemsViews.any { it.isFieldsEmpty() })
        }, 500)
    }

    private fun calculateSubTotal() {
        billModel.subTotal = productItemsViews.sumOf { it.price }
        calculateTotalAmount()
        view?.updateSubTotalAmount(billModel.subTotal)
    }

    private fun calculateTotalAmount() {
        billModel.apply {
            totalBill = subTotal + taxAmount + servicesAmount
            otherAmount = 0
        }
        view?.updateOtherAmount(billModel.otherAmount)
        view?.updateTotalAmount(billModel.totalBill)
    }

    override fun replaceDotInForm(rawInput: String): String {
        val inputAfterReplace = if (rawInput.contains(".")) {
            rawInput.replace(".", "")
        } else {
            rawInput
        }

        return inputAfterReplace
    }

    override fun onBillDataEmpty() {
        if (billModel.billName.isNotEmpty()) {
            view?.onBillEmpty(true)
        } else {
            view?.onBillEmpty(false)
        }
    }

    override fun onSaveDraftClicked() {
        if (billModel.billName.isNotEmpty()) {
            view?.onBillNameNotEmpty()
        } else if (productItemsViews.any { it.name.isNotEmpty() || it.price != 0L || it.quantity != 0 }) {
            view?.onBillItemNotEmpty()
        }
    }

    override fun onQuantityEmpty(itemModel: SplitBillEditFormItemViewModel) {
        itemModel.quantity = 1
        doValidateButtonSubmit(itemModel)
    }

    private fun setRequest(): ProcessSplitBillRequest {
        itemData = productItemsViews.map { productData ->
            EditItemDetailRequest(
                itemName = productData.name,
                itemQuantity = productData.quantity,
                itemPrice = productData.price
            )
        }.toMutableList()

        if (mIsFromDraft) {
            requestProcess = ProcessSplitBillRequest(
                billId = mBillId,
                billName = billModel.billName,
                transactionDate = billModel.billDate,
                items = itemData,
                subtotal = billModel.subTotal,
                tax = billModel.taxAmount,
                serviceFee = billModel.servicesAmount,
                otherFee = billModel.otherAmount,
                grandTotal = billModel.totalBill
            )
        } else {
            requestProcess = ProcessSplitBillRequest(
                billName = billModel.billName,
                transactionDate = billModel.billDate,
                items = itemData,
                subtotal = billModel.subTotal,
                tax = billModel.taxAmount,
                serviceFee = billModel.servicesAmount,
                otherFee = billModel.otherAmount,
                grandTotal = billModel.totalBill
            )
        }

        return requestProcess as ProcessSplitBillRequest
    }

    override fun getSplitBillProcess() {
        if (mUrlProcess.isEmpty() || !isViewAttached) {
            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable = apiSource.getData(mUrlProcess, setRequest(), seqNum)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView()?.hideProgress()
                    getView()?.onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView()?.hideProgress()

                    val processSplitBillResponse = restResponse.getData(
                        ProcessSplitBillResponse::class.java
                    )

                    brImoPrefRepository.saveIsBillCreated(true)

                    processSplitBillResponse.userData.isBrimo = true
                    processSplitBillResponse.billName = mBillName
                    processSplitBillResponse.billDate = mBillDate
                    getView()?.onSuccessGetProcess(processSplitBillResponse, productItemsViews)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    view?.hideProgress()

                    if (restResponse.code == Constant.RE12) {
                        getView()?.onExceptionSnackbar(restResponse.desc)
                    } else {
                        getView()?.onException(restResponse.desc)
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getSplitBillDraftDetail() {
        if (mUrlDraft.isEmpty() || !isViewAttached) {
            return
        }

        if (view != null) {

            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable = apiSource.getData(mUrlDraft, setRequest(), seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        if (response.code == Constant.RE_SUCCESS) {

                            brImoPrefRepository.saveIsBillCreated(true)

                            getView()?.onSuccessGetDraftDetails()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view?.hideProgress()

                        if (restResponse.code == Constant.RE12) {
                            getView()?.onExceptionSnackbar(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
            compositeDisposable.add(disposable)
        }
    }

    override fun isBillCreated(): Boolean {
        return brImoPrefRepository.isBillCreated
    }

}