package id.co.bri.brimons.presenters.cetakToken;


import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.cetakToken.ICetakTokenPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.cetakToken.ICetakTokenView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.CetakTokenRequest;
import id.co.bri.brimons.models.apimodel.request.StatusRequest;
import id.co.bri.brimons.models.apimodel.response.InboxResponse;
import id.co.bri.brimons.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class CetakTokenPresenter <V extends IMvpView & IBaseFormView & ICetakTokenView> extends BaseFormPresenter<V> implements ICetakTokenPresenter<V> {

    private String urlCetak, urlDetailCetak;
    private InboxResponse inboxResponse;
    private ReceiptRevampInboxResponse receiptResponse;
    private StatusRequest statusRequest;
    private CetakTokenRequest cetakTokenRequest;

    private boolean isLoading, isLoadingItem = false;


    public CetakTokenPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void setUrlCetak(String urlCetak) {
        this.urlCetak = urlCetak;
    }

    @Override
    public void setUrlDetailCetak(String urlDetailCetak) {
        this.urlDetailCetak = urlDetailCetak;
    }

    @Override
    public void getCetakToken(String lastId,boolean isRefresh) {
        if (isLoading) {
            return;
        }

        cetakTokenRequest = new CetakTokenRequest(lastId);

        if (isViewAttached()) {
            isLoading = true;
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getData(urlCetak, cetakTokenRequest, seqNum)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    isLoading = false;
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    isLoading = false;
                                    getView().hideProgress();
                                    //TO-DO onSuccess
                                    try {
                                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                            inboxResponse = response.getData(InboxResponse.class);
                                            getView().onSuccessGetCetakToken(inboxResponse, isRefresh);
                                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                            getView().onCetakEnd(response.getDesc());
                                        }
                                    } catch (Exception e) {
                                        getView().onCetakEnd(e.getMessage());
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    isLoading = false;
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                        getView().onException12();
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93();
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
            );
        }
    }

    @Override
    public void getCetakDetail(String refnumber) {
        if (isLoadingItem) {
            return;
        } else {

            if (isViewAttached()) {
                isLoadingItem = true;
                getView().showProgress();

                statusRequest = new StatusRequest(refnumber);
                String seqNum = getBRImoPrefRepository().getSeqNumber();
                getCompositeDisposable().add(
                        getApiSource().getData(urlDetailCetak, statusRequest, seqNum)//function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.single())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(),seqNum) {

                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        isLoadingItem = false;
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        getView().hideProgress();
                                        isLoadingItem = false;


                                        try {
                                            receiptResponse = response.getData(ReceiptRevampInboxResponse.class);
                                            if (receiptResponse.getReceiptRevampResponse().getSourceAccountDataView() != null){
                                                getView().onSuccessGetDetailToken(receiptResponse);
                                            }else{
                                                ReceiptResponse pendingResponse = response.getData(ReceiptResponse.class);
                                                getView().onSuccessGetDetailTokenNonRevamp(pendingResponse);
                                            }
                                        }catch (Exception e){
                                            // do nothing
                                        }
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                            getView().onSessionEnd(restResponse.getDesc());
                                        } else
                                            getView().onException(restResponse.getDesc());
                                    }
                                })
                );
            }
        }
    }
}