package id.co.bri.brimons.presenters.ibbiz;


import id.co.bri.brimons.contract.IPresenter.ibbiz.IOnboardingIbbizPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.ibbiz.IOnboardingIbbizView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.AccountIbbizResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class OnboardingIbbizPresenter<V extends IMvpView & IOnboardingIbbizView>
        extends MvpPresenter<V> implements IOnboardingIbbizPresenter<V> {

    private String url;

    public OnboardingIbbizPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onGetDataAccount() {
        if (url != null && isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(url, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        AccountIbbizResponse accountResponse = response.getData(AccountIbbizResponse.class);
                                        getView().onSuccessGetAccount(accountResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                        getView().onSuccessGoRek();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }
}