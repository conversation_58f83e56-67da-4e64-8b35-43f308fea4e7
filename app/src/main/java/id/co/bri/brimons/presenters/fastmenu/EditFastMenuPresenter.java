package id.co.bri.brimons.presenters.fastmenu;

import java.util.List;

import id.co.bri.brimons.contract.IPresenter.fastmenu.IEditFastMenuPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.fastmenu.IEditFastMenuView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.fastmenu.FastMenuSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.daomodel.FastMenu;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.CompletableObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class EditFastMenuPresenter<V extends IMvpView & IEditFastMenuView> extends MvpPresenter<V> implements IEditFastMenuPresenter<V> {

    private FastMenuSource fastMenuSource;

    public EditFastMenuPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, FastMenuSource fastMenuSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.fastMenuSource = fastMenuSource;
    }

    public void getDefaultFastMenu() {
        getCompositeDisposable().add(fastMenuSource.getFastMenu()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenus -> {
                    if (fastMenus != null) {
                        getView().onLoadDefaultFastmenu(fastMenus);
                    }
                }, throwable -> {
                    getView().onException(throwable.getMessage());
                }));

    }

    @Override
    public void getFisrtFastMenu(List<FastMenu> fastMenuList) {
        fastMenuSource.saveFastMenu(fastMenuList)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {

                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onError(@NonNull Throwable e) {

                    }
                });

    }
}
