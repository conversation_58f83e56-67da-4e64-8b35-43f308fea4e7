package id.co.bri.brimons.presenters.brivarevamp

import android.util.Log
import id.co.bri.brimons.contract.IPresenter.brivarevamp.IInquiryKonfirmasiBrivaRevampClosePresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.brivarevamp.IInquiryKonfirmasiBrivaRevampCloseView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.config.DbConfig

import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.cashback.CashbackRevFilterFMRequest
import id.co.bri.brimons.models.apimodel.request.cashback.CashbackRevFilterRequest
import id.co.bri.brimons.models.apimodel.request.cashback.PilihCashbackFMRequest
import id.co.bri.brimons.models.apimodel.request.cashback.PilihCashbackRequest
import id.co.bri.brimons.models.apimodel.request.revampbriva.FastPayBrivaRevampRequest
import id.co.bri.brimons.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimons.models.apimodel.response.GeneralResponse
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.ListAllCashbackFilterResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SelectCashbackResponse
import id.co.bri.brimons.models.daomodel.Transaksi
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver
import io.reactivex.schedulers.Schedulers
import id.co.bri.brimons.models.apimodel.request.autograbfund.PaymentAutoGrabFundRequest

class InquiryKonfirmasiBrivaRevampClosePresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IInquiryKonfirmasiBrivaRevampClosePresenter<V> where V : IMvpView, V : IInquiryKonfirmasiBrivaRevampCloseView {

    private lateinit var mUrlPay: String
    private var mUrlAutoGrabFund: String = ""
    private lateinit var paymentRequest: Any
    private var idPayment: Int = 0
    private lateinit var urlGetCashback: String
    private var cashbackRequest: Any? = null
    private lateinit var selectCashbackUrl: String
    private var redeemCashbackRequest: Any? = null

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold

        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun setUrlPayment(url: String) {
        mUrlPay = url
    }

    override fun setIdPayment(id: Int) {
        idPayment = id
    }

    override fun setUrlGetCashback(url: String) {
        this.urlGetCashback = url
    }

    override fun getCashbackAll(
        accountNumber: String,
        referenceNumber: String,
        isFromFastMenu: Boolean
    ) {
        if (urlGetCashback == null || !isViewAttached) {
            return
        }

        cashbackRequest = if (isFromFastMenu) {
            CashbackRevFilterFMRequest(getFastMenuRequest(), referenceNumber, accountNumber)
        } else {
            CashbackRevFilterRequest(
                accountNumber = accountNumber,
                referenceNumber = referenceNumber
            )
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlGetCashback, cashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                            val responseData = response.getData(
                                ListAllCashbackFilterResponse::class.java
                            )
                            getView().onSuccessGetCashback(responseData)
                        } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) {
                            getView().onCashbackBlank(response.desc)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) getView()!!.onSessionEnd(restResponse.desc)
                        else getView().onException(restResponse.desc)
                    }
                })
        )
    }

    override fun setRedeemCashbackUrl(url: String) {
        selectCashbackUrl = url
    }

    override fun getRedeemCashback(refNum: String, code: String, fromFastMenu: Boolean) {
        if (selectCashbackUrl == null || !isViewAttached) {
            return
        }

        redeemCashbackRequest = if (fromFastMenu) {
            PilihCashbackFMRequest(getFastMenuRequest(), refNum, code)
        } else {
            PilihCashbackRequest(refNum, code)
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(selectCashbackUrl, redeemCashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val selectCashbackResponse = response.getData(
                            SelectCashbackResponse::class.java
                        )
                        getView().onSuccessClearSelectedCashback(selectCashbackResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView().onSessionEnd(restResponse.desc) else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_12.value,
                                ignoreCase = true
                            )
                        ) getView().onException12(restResponse.desc) else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                ignoreCase = true
                            )
                        ) getView().onException99(restResponse.desc) else getView().onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getDataPaymentRevamp(
        pin: String, mInquiryBrivaRevampResponse: InquiryBrivaRevampResponse,
        account: String, saveAs: String, note: String, fromFastMenu: Boolean
    ) {
        if (mUrlPay.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        paymentRequest = if (fromFastMenu) FastPayBrivaRevampRequest(
            getFastMenuRequest(), mInquiryBrivaRevampResponse.referenceNumber,
            account, mInquiryBrivaRevampResponse.amount.toString(), saveAs,
            note, pin, mInquiryBrivaRevampResponse.pfmCategory.toString()
        )
        else PayBrivaRevampRequest(
            mInquiryBrivaRevampResponse.referenceNumber,
            account, mInquiryBrivaRevampResponse.amount.toString(), saveAs,
            note, pin, mInquiryBrivaRevampResponse.pfmCategory.toString()
        )

        val disposable: Disposable =
            apiSource.getData(mUrlPay, paymentRequest, seqNum) //function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val receiptRevampResponse =
                            response.getData(ReceiptRevampResponse::class.java)

                        if (receiptRevampResponse.immediatelyFlag) onSaveTransaksiPfm(
                            generateTransaksiModel(
                                mInquiryBrivaRevampResponse.pfmCategory,
                                mInquiryBrivaRevampResponse.payAmount,
                                mInquiryBrivaRevampResponse.referenceNumber,
                                mInquiryBrivaRevampResponse.pfmDescription
                            )
                        )
                        getView().onSuccessGetPaymentRevamp(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) getView().onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) getView().onException93(
                            restResponse.desc
                        )
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) getView().onException01(
                            restResponse.desc
                        )
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value) getView().onException99(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value)
                            getView().onExceptionLimitExceed(
                                restResponse.getData(
                                    GeneralResponse::class.java
                                )
                            )
                        else getView().onException(restResponse.desc)
                    }
                })

        compositeDisposable.add(disposable)
    }

    fun onSaveTransaksiPfm(transaksi: Transaksi?) {
        if (transaksi != null) {
            compositeDisposable.add(
                transaksiPfmSource
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : DisposableSingleObserver<Long?>() {
                        override fun onSuccess(aLong: Long) {}
                        override fun onError(e: Throwable) {
                            if (!GeneralHelper.isProd())
                                Log.d("saveTransaksiPfm", "onError: $e")
                        }
                    })
            )
        }
    }

    fun generateTransaksiModel(
        kategoriId: Int,
        amount: Long,
        referenceNumber: String?,
        billingName: String?
    ): Transaksi? {
        var transaksi: Transaksi? = null
        try {
            transaksi = Transaksi(
                kategoriId.toLong(),
                1,
                billingName,
                "",
                DbConfig.TRX_OUT,
                brImoPrefRepository.user,
                amount,
                CalendarHelper.getCurrentDate(),
                CalendarHelper.getCurrentTime(),
                java.lang.Long.valueOf(referenceNumber.toString()),
                idPayment.toLong()
            )
        } catch (e: Exception) {
            if (!GeneralHelper.isProd())
                Log.d("Error", "generateTransaksiModel: $e")
        }
        return transaksi
    }

    override fun getDataPaymentRevampAgf(
        pin: String,
        refNum: String,
        accountNum: String,
        pfmCategory: String,
        agfDateStart: String,
        agfDateStop: String,
        agfFrequency: String,
        agfType: String,
        saveAs: String,
        note: String
    ) {
        if (mUrlAutoGrabFund.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        paymentRequest = PaymentAutoGrabFundRequest(
            refNum, pin, accountNum, pfmCategory, agfDateStart, agfDateStop, agfFrequency, agfType,saveAs, note
        )

        val disposable: Disposable =
            apiSource.getData(mUrlAutoGrabFund, paymentRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val receiptRevampResponse = response.getData(ReceiptRevampResponse::class.java)
                        getView().onSuccessGetPaymentRevamp(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.CODE_ALERT_FINISH))
                            getView().onException93(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value))
                            getView().onException01(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                            getView().onException99(restResponse.desc)
                        else getView().onException(restResponse.desc)
                    }
                })

        compositeDisposable.add(disposable)

    }

    override fun setUrlPaymentAgf(url: String) {
        mUrlAutoGrabFund = url
    }

    override fun getIsFirstTimeShowAgf(page: String): Boolean {
        return brImoPrefRepository.getIsFirstTimeShowAgf(page)
    }

    override fun setIsFirstTimeShowAgf(page: String, isFirstTime: Boolean) {
        brImoPrefRepository.setIsFirstTimeShowAgf(page, isFirstTime)
    }
}