package id.co.bri.brimons.presenters.esbn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.esbn.IConfirmationSbnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.esbn.IConfirmationSbnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.esbn.InquiryMpnSbnRequest;
import id.co.bri.brimons.models.apimodel.request.esbn.SubmitOfferSbnRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.ConfirmationSbnResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.ConfirmationSbnLimitResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class ConfirmationSbnPresenter<V extends IMvpView & IConfirmationSbnView> extends MvpPresenter<V> implements IConfirmationSbnPresenter<V> {
    private String url_submit;
    private String url_submit2;

    public ConfirmationSbnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlBayar(String urlGetDetail) {
        this.url_submit = urlGetDetail;
    }

    @Override
    public void bayarSbn(SubmitOfferSbnRequest model) {
        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_submit,model , seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                ConfirmationSbnResponse dataResponse = response.getData(ConfirmationSbnResponse.class);
                                getView().onSuccesSubmit(dataResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setUrlBayar2(String url) {
        this.url_submit2 = url;
    }

    @Override
    public void bayarSbn2(InquiryMpnSbnRequest model, boolean isNull) {

        if (isNull){
            getView().showProgress();
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_submit2,model , seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    GeneralInquiryResponse dataResponse = response.getData(GeneralInquiryResponse.class);
//                                    InquiryMpnSbnResponse dataResponse = response.getData(InquiryMpnSbnResponse.class);
                                    getView().onSuccesSubmit2(dataResponse);
                                }else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                    ConfirmationSbnLimitResponse dataResponse = response.getData(ConfirmationSbnLimitResponse.class);
                                    getView().onSuccesSubmitLimit(dataResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setUrlGetDetailOrder(String url) {

    }

    @Override
    public void getDetailOrder() {

    }


    @Override
    public void start() {
        super.start();
    }

    @Override
    public void stop() {
        super.stop();
    }

}