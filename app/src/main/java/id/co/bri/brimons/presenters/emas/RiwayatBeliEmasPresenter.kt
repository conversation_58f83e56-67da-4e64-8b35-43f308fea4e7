package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IRiwayatBeliEmasPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IRiwayatBeliEmasView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimons.models.apimodel.response.emas.RiwayatTransaksiEmasResponse
import id.co.bri.brimons.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimons.models.apimodel.response.onExceptionWH
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RiwayatBeliEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                  compositeDisposable: CompositeDisposable?,
                                  mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                  categoryPfmSource: CategoryPfmSource?,
                                  transaksiPfmSource: TransaksiPfmSource?,
                                  anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IRiwayatBeliEmasPresenter<V> where V : IMvpView?, V : IRiwayatBeliEmasView {

    private var url :String = ""
    private var mUrlFilter :String = ""
    private var urlRiwayat : String = ""

    override fun setUrlGetFormBeli(urlBeli: String) {
        url = urlBeli
    }

    override fun setUrlFilter(urlFilter: String) {
        mUrlFilter = urlFilter
    }

    override fun getBeliEmas() {
        if (url == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(url, "", seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()!!.hideProgress()
                                        val data = response.getData(
                                                InquiryOpenEmasResponse::class.java
                                        )
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                            getView()!!.onSuccessFormBeliEmas(data)
                                        }

                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                            val onExceptionCase02: onExceptionWH =
                                                    restResponse.getData(
                                                        onExceptionWH::class.java
                                                    )
                                            getView()!!.exceptionEODEOM(onExceptionCase02)
                                        }
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()!!.hideProgress()
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SM.value, ignoreCase = true)) {
                                            val response = restResponse.getData(
                                                    SafetyModeDrawerResponse::class.java
                                            )
                                            getView()!!.onSafetyMode(response)
                                        } else getView()!!.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun getMutationRange(mutationDateRangeFilterRequest: RiwayatFilterRequest?) {
        if (mUrlFilter == null || !isViewAttached) {
            return
        }

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlFilter, mutationDateRangeFilterRequest, seq) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seq) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val mutasiResponse = response.getData(RiwayatTransaksiEmasResponse::class.java)
                            getView().onSuccessGetMutation(mutasiResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        when(restResponse.code){
                            RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException(restResponse.desc)
                            else -> onApiError(restResponse)
                        }
                    }
                })
        )
    }

    override fun setUrlRiwayatTransaksi(url: String) {
        urlRiwayat = url
    }

    override fun getDataRiwayatTransaksi() {
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
            apiSource.getDataForm(urlRiwayat, seqNum) //function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        val responseRiwayat = response.getData(
                            RiwayatTransaksiEmasResponse::class.java
                        )
                        getView()?.onSuccessGetMutation(responseRiwayat)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        getView()?.onException(
                            restResponse.desc
                        )
                    }
                })
        compositeDisposable.add(disposable)
    }
}