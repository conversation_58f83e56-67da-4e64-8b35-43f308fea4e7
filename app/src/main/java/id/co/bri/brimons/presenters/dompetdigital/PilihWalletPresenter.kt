package id.co.bri.brimons.presenters.dompetdigital

import id.co.bri.brimons.contract.IPresenter.dompetdigitalrevamp.IPilihWalletPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dompetdigitalrevamp.IPilihWalletView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class PilihWalletPresenter <V>(schedulerProvider: SchedulerProvider?,
                               compositeDisposable: CompositeDisposable?,
                               mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                               categoryPfmSource: CategoryPfmSource?,
                               transaksiPfmSource: TransaksiPfmSource?,
                               anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IPilihWalletPresenter<V> where V : IMvpView?, V : IPilihWalletView? {


}