package id.co.bri.brimons.presenters.onboardingnewskin

import android.util.Log
import id.co.bri.brimons.contract.IPresenter.newskinonboarding.IChangePassCreatePresenter
import id.co.bri.brimons.contract.IView.newskinonboarding.IChangePassCreateView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.PassChangeCreateRequest
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.RequestChangePass
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.PassCheckData
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.PassCheckResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.PassSubmitSuccessData
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit
import kotlin.code
import kotlin.text.toLong

class ChangePassCreatePresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), IChangePassCreatePresenter<V> where V : IChangePassCreateView {

    private var urlValidatePass = ""
    private var urlSubmitPass = ""
    private var codeSubmitPass = ""
    private var codeValidatePass = ""
    override fun setValidatePassUrl(url: String) {
        urlValidatePass = url
    }

    override fun setSubmitPassUrl(url: String) {
        urlSubmitPass = url
    }

    override fun onCheckPassCreate(pass: String, referenceNumber: String) {
        if (!isViewAttached || urlValidatePass.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val requestBody = PassChangeCreateRequest(
            new_password = pass,
            reference_number = referenceNumber
        )

        compositeDisposable.add(
            apiSource.getData(urlValidatePass, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PassCheckData::class.java)
                        getView().onPassValid(checkResponse, code = response.code)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PassCheckResponse::class.java)
                        codeValidatePass = restResponse.code
                        getView().onPassCheckFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailureCheck(errorMessage, codeValidatePass)
                    }
                })
        )
    }


    override fun onCreatePass(referenceNumber: String) {
        if (!isViewAttached || urlSubmitPass.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = RequestChangePass(reference_number = referenceNumber)

        compositeDisposable.add(
            apiSource.getData(urlSubmitPass, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PassSubmitSuccessData::class.java)
                        getView().onSubmitSuccess(checkResponse, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PassSubmitSuccessData::class.java)
                        codeSubmitPass = restResponse.code
                        getView().onPassSubmitFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailureSubmit(errorMessage, codeSubmitPass)
                    }
                })
        )
    }
}


