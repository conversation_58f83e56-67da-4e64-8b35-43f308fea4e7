package id.co.bri.brimons.presenters.cardless;


import id.co.bri.brimons.contract.IPresenter.cardless.IFormSetorTunaiPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.cardless.IFormSetorTunaiView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastSetorTunaiPaymentRequest;
import id.co.bri.brimons.models.apimodel.request.SetorTunaiRequest;
import id.co.bri.brimons.models.apimodel.response.PaymentSetorResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SetorTunaiResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormSetorTunaiPresenter<V extends IMvpView & IFormSetorTunaiView> extends MvpPresenter<V> implements IFormSetorTunaiPresenter<V> {

    private static final String TAG = "FormSetorPresenter";
    protected String inquiryUrl;
    protected String paymentUrl;
    protected String formUrl;
    protected Object formRequest = null;
    protected Object paymentRequest = null;

    public FormSetorTunaiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onClickSubmit() {
        if (getView() != null) {
            if (validateInput()) {


            }
        }

    }

    @Override
    public void getFormSetor() {
        if (formUrl == null || !isViewAttached()) {
            return;
        }

        if (onLoad)
            return;

        onLoad = true;
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataForm(formUrl, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                onFailure(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                onLoad = false;
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    SetorTunaiResponse setorTunaiResponse = response.getData(SetorTunaiResponse.class);
                                    getView().onSuccessGetAccount(setorTunaiResponse);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    PaymentSetorResponse paymentSetorResponse = response.getData(PaymentSetorResponse.class);
                                    getView().onTokenActive(paymentSetorResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));

    }

    @Override
    public void getFormSetorFm(boolean fromFastMenu) {
        if (formUrl == null || !isViewAttached()) {
            return;
        }

        formRequest = getFastMenuRequest();

        if (onLoad)
            return;

        if (!fromFastMenu)
            return;

        onLoad = true;
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(formUrl, formRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                onFailure(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                onLoad = false;
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    SetorTunaiResponse setorTunaiResponse = response.getData(SetorTunaiResponse.class);
                                    getView().onSuccessGetAccount(setorTunaiResponse);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    PaymentSetorResponse paymentSetorResponse = response.getData(PaymentSetorResponse.class);
                                    getView().onTokenActive(paymentSetorResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));

    }


    @Override
    public void getPayment(String accountNumber, String pin, String pfmCategory, String note, boolean isFromFastMenu) {
        if (paymentUrl == null || !isViewAttached()) {
            return;
        }

        if (onLoad)
            return;

        onLoad = true;

        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        if (isFromFastMenu){
            paymentRequest = new FastSetorTunaiPaymentRequest(getFastMenuRequest(), accountNumber, pin, pfmCategory, note);
        }else{
            paymentRequest = new SetorTunaiRequest(accountNumber, pin, pfmCategory, note);
        }

        getCompositeDisposable()
                .add(getApiSource().getData(paymentUrl, paymentRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                onLoad = false;
                                getView().hideProgress();

                                onFailure(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                onLoad = false;
                                getView().hideProgress();
                                PaymentSetorResponse paymentSetorResponse = response.getData(PaymentSetorResponse.class);
                                getView().onSuccessGetSetorTunai(paymentSetorResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));

    }



    protected boolean validateInput() {
        boolean valid = true;

        try {

//            if (getView().getAmount() < 1) {
//                getView().showInputError("Harga tidak valid");
//                getView().hideProgress();
//                return false;
//            }
        } catch (Exception e) {
//            getView().showInputError(e.getMessage());
            return false;
        }

        return valid;
    }


    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }


    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault);
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }

    @Override
    public void setPaymentUrl(String url) {
        paymentUrl = url;
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }
}