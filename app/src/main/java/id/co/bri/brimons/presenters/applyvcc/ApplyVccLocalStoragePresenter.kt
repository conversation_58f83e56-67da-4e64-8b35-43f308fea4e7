package id.co.bri.brimons.presenters.applyvcc

import id.co.bri.brimons.contract.IPresenter.applyvcc.IApplyVccLocalStoragePresenter
import id.co.bri.brimons.contract.IView.applyvcc.IApplyVccLocalStorageView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.applyccrevamp.ApplyCcSubmitDataLocalRequest
import id.co.bri.brimons.models.applyccrevamp.ApplyCcDataFormModel
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.fromJsonToObject
import id.co.bri.brimons.util.extension.fromObjectToJson
import io.reactivex.disposables.CompositeDisposable

class ApplyVccLocalStoragePresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IApplyVccLocalStoragePresenter<V> where V : IApplyVccLocalStorageView {
    override fun setApplyVccDataFormLocal(applyCcDataFormModel: ApplyCcDataFormModel) {
        val applyCcDataFormModelJson = applyCcDataFormModel.fromObjectToJson()
        brImoPrefRepository.setApplyVccDataForm(applyCcDataFormModelJson)
    }

    override fun getApplyVccDataFormLocal() = brImoPrefRepository.getApplyVccDataForm().fromJsonToObject<ApplyCcDataFormModel>(ApplyCcDataFormModel())

    override fun setApplyVccRequestLocal(applyVccRequestModel: ApplyCcSubmitDataLocalRequest) {
        val applyVccRequestModelJson = applyVccRequestModel.fromObjectToJson()
        brImoPrefRepository.setApplyVccRequest(applyVccRequestModelJson)
    }

    override fun getApplyVccRequestLocal(): ApplyCcSubmitDataLocalRequest = brImoPrefRepository.getApplyVccRequest().fromJsonToObject<ApplyCcSubmitDataLocalRequest>(ApplyCcSubmitDataLocalRequest())

    override fun resetApplyVccRequestLocal() = setApplyVccRequestLocal(ApplyCcSubmitDataLocalRequest())

    override fun getPhoneNumberLocal(): String = brImoPrefRepository.phoneNumber ?: ""

    override fun getName(): String = brImoPrefRepository.name

    override fun getUsernameLocal(): String {
        return brImoPrefRepository.username
    }

    override fun setRiplayUrl(url: String) {
        brImoPrefRepository.setRiplayUrl(url)
    }

    override fun getRiplayUrl(): String {
        return brImoPrefRepository.riplayUrl
    }

}