package id.co.bri.brimons.presenters.esbn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.esbn.IBeliSbnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.esbn.IBeliSbnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.esbn.DetailOfferSbnRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.DataItemBeliSbnResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.DataTentangSbnResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.EsbnExceptionResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.BeliSbnResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class BeliSbnPresenter<V extends IMvpView & IBeliSbnView> extends MvpPresenter<V> implements IBeliSbnPresenter<V> {
    private String url_detail;
    private String url_offer;
    private String url_desc;

    public BeliSbnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlGetOffer(String url) {
        this.url_offer = url;
    }

    @Override
    public void getAboutSbn() {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_desc, "", seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                DataTentangSbnResponse dataResponse = response.getData(DataTentangSbnResponse.class);
                                getView().onSuccesGetAboutSbn(dataResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setIUrlGetDescSbn(String url) {
        this.url_desc = url;
    }

    @Override
    public void setUrlGetDetail(String urlGetDetail) {
        this.url_detail = urlGetDetail;
    }

    @Override
    public void getDetailOffer(int idSeri) {
        getView().showProgress();
        DetailOfferSbnRequest req = new DetailOfferSbnRequest(idSeri);

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_detail,req , seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                BeliSbnResponse dataResponse = response.getData(BeliSbnResponse.class);
                                getView().onSuccessGetDetail(dataResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getDataOffer() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_offer, "", seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    DataItemBeliSbnResponse dataResponse = response.getData(DataItemBeliSbnResponse.class);
                                    getView().onSuccesGetOffer(dataResponse);
                                }
                                else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                    EsbnExceptionResponse esbnExceptionResponse = response.getData(EsbnExceptionResponse.class);
                                    getView().onException01(esbnExceptionResponse);
                                    getView().hideProgress();
                                }
                                else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                                    EsbnExceptionResponse esbnExceptionResponse = response.getData(EsbnExceptionResponse.class);
                                    getView().onException02(esbnExceptionResponse);
                                    getView().hideProgress();
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void start() {
        super.start();
        getDataOffer();
    }

    @Override
    public void stop() {
        super.stop();
    }
}