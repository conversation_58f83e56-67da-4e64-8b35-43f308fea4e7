package id.co.bri.brimons.presenters.virtualdebitcard

import id.co.bri.brimons.contract.IPresenter.virtualdebitcard.IVirtualDebitLabelPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.virtualdebitcard.IVirtualDebitLabelView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.ChipVirtualDebitUIModel
import id.co.bri.brimons.models.apimodel.request.InquiryBrizziRequest
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class VirtualDebitLabelPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVirtualDebitLabelPresenter<V> where V : IMvpView, V : IVirtualDebitLabelView {

    private var chipList: List<ChipVirtualDebitUIModel> = emptyList()

    override fun getDataLabel() {
        val suggestLabelCard = "Hiburan, Liburan, Belanja, Jajan, Tabungan, Beli Game"

        val data = suggestLabelCard
            .split(",")
            .map { it.trim() }
            .map { ChipVirtualDebitUIModel(title = it) }

        chipList = data
        view.showChips(chipList)

    }

    override fun onClickChip(data: ChipVirtualDebitUIModel, position: Int) {
        chipList = chipList.mapIndexed { index, chip ->
            chip.copy(isSelected = (index == position))
        }
        view.showChips(chipList)
    }

}