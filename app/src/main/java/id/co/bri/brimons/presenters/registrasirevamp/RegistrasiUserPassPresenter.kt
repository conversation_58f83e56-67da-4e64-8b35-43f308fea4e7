package id.co.bri.brimons.presenters.registrasirevamp

import id.co.bri.brimons.contract.IPresenter.registrasirevamp.IRegistrasiUserPassPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.registrasirevamp.IRegistrasiUserPassView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.RegisIdModel
import id.co.bri.brimons.models.apimodel.request.registrasi.RegisUserPassRequest
import id.co.bri.brimons.models.apimodel.request.registrasi.RegisUsernameRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.registrasi.RegisUserResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiUserPassPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiUserPassPresenter<V> where V : IMvpView, V : IRegistrasiUserPassView? {

    private var urlUsername: String? = null
    private var urlUserPass: String? = null

    override fun getDeviceId() {
        view.getRegisId(brImoPrefRepository.deviceId)
    }

    override fun setUrlUsername(url: String) {
        urlUsername = url
    }

    override fun setUrlUserPass(url: String) {
        urlUserPass = url
    }

    override fun sendUserCheck(regisUsernameRequest: RegisUsernameRequest) {
        if (urlUsername != null && isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(urlUsername, regisUsernameRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val regisUserResponse =
                                response.getData(RegisUserResponse::class.java)
                            getView().onSuccessCheckUser(regisUserResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().onExceptionUserExist()
                        }
                    })
            )
        }
    }

    override fun sendUserPass(regisUserPassRequest: RegisUserPassRequest) {
        if (urlUserPass != null && isViewAttached) {

            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(urlUserPass, regisUserPassRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisIdModel =
                                response.getData(RegisIdModel::class.java)
                            getView().onSuccessUserPass(regisIdModel)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }
}