package id.co.bri.brimons.presenters.transactionlimitinformation

import id.co.bri.brimons.contract.IPresenter.transactionlimitinformation.ITransactionLimitInformationPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.transactionlimitinformation.ITransactionLimitInformationView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimons.models.apimodel.response.QuestionResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SafetyModeResponse
import id.co.bri.brimons.models.apimodel.response.transactionlimitinformation.TransactionLimitInformationResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class TransactionLimitInformationPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    ITransactionLimitInformationPresenter<V> where V : IMvpView, V : ITransactionLimitInformationView {

    internal lateinit var urlGetSafetyMode: String
    internal lateinit var urlGetHelpSafetyMode: String
    internal lateinit var urlGetLimitInformation: String
    override fun setUrlGetSafetyMode(url: String) {
        this.urlGetSafetyMode = url
    }

    override fun getSafetyMode() {
        if (!isViewAttached || urlGetSafetyMode.isEmpty()) return

        val seqNum = brImoPrefRepository.seqNumber

        view.showSkeleton()

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlGetSafetyMode, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                            val safetyModeResponse = response.getData(
                                SafetyModeResponse::class.java
                            )
                            getView().onSuccessGetSafetyMode(safetyModeResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)) {
                            getView().onSafetyMode01()
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun setUrlGetHelpSafetyMode(url: String) {
        this.urlGetHelpSafetyMode = url
    }

    override fun getHelpSafetyMode(id: String) {
        if (urlGetHelpSafetyMode == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val request = SafetyPusatBantuanRequest(id)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGetHelpSafetyMode, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val topicQuestionResponse =
                                response.getData(QuestionResponse::class.java)
                            getView().onSuccessGetHelpSafetyMode(topicQuestionResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(
                                restResponse.desc
                            )
                        }
                    })
            )
    }

    override fun setUrlGetLimitInformation(url: String) {
        this.urlGetLimitInformation = url
    }

    override fun getLimitInformation() {
        if (!isViewAttached || urlGetLimitInformation.isEmpty()) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlGetLimitInformation, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                            val mResponse = response.getData(
                                TransactionLimitInformationResponse::class.java
                            )
                            getView().onSuccessGetLimitInformation(mResponse)
                        } else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_01.value) {
                            val mResponse = restResponse.getData(
                                EmptyStateResponse::class.java
                            )
                            getView().failedToLoadData(mResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)) {
                            getView().onSafetyMode01()
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

}