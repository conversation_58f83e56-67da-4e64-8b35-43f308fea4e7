package id.co.bri.brimons.presenters.lifestyle

import id.co.bri.brimons.contract.IPresenter.lifestyle.IWebviewLifestylePresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.lifestyle.IWebviewLifestyleView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimons.models.apimodel.request.lifestyle.LifestylePatternRequest
import id.co.bri.brimons.models.apimodel.request.lifestyle.RequestMoliga
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class WebviewLifestylePresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IWebviewLifestylePresenter<V> where V : IMvpView?, V : IWebviewLifestyleView? {

    private lateinit var request: Any

    private lateinit var mUrlRevokeSessionLifestyle: String
    private lateinit var mUrlConfirmationLifestyle: String
    private lateinit var mUrlConfirmationMoliga: String
    private var mUrlPaymentMoliga: String = ""

    override fun setUrlRevokeSession(urlRevoke: String) {
        this.mUrlRevokeSessionLifestyle = urlRevoke
    }

    override fun setUrlConfirmation(urlConfirmation: String) {
        this.mUrlConfirmationLifestyle = urlConfirmation
    }

    override fun setUrlConfirmationMoliga(urlConfirmationMoliga: String) {
        this.mUrlConfirmationMoliga = urlConfirmationMoliga
    }

    override fun setUrlPaymentMoliga(urlPaymentMoliga: String) {
        this.mUrlPaymentMoliga = urlPaymentMoliga
    }

    override fun getRevokeSession(sessionId: String, isInquiry: Boolean) {
        view?.showProgress()

        if (mUrlRevokeSessionLifestyle.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        val username = brImoPrefRepository.username
        val tokenKey = brImoPrefRepository.tokenKey

        request = RevokeSessionRequest(username, tokenKey, sessionId)

        compositeDisposable.add(
            apiSource.getData(mUrlRevokeSessionLifestyle, request, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        getView()?.onSuccessRevokeSession(isInquiry)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        getView()?.onSuccessRevokeSession(isInquiry)
                    }
                })
        )
    }

    override fun getConfirmation(confirmationRequest: LifestylePatternRequest) {
        view!!.showProgress()

        if (mUrlConfirmationLifestyle.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmationLifestyle, confirmationRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val confirmationLifestyleResponse = response.getData(
                            ConfirmationLifestyleResponse::class.java
                        )
                        getView()?.onSuccessConfirmation(
                            confirmationLifestyleResponse
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                            getView()?.onException12(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun getConfirmationMoliga(requestMoliga: RequestMoliga) {
        view!!.showProgress()

        if (mUrlConfirmationMoliga.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmationMoliga, requestMoliga, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()?.onSuccessConfirmationMoliga(
                            inquiryBrivaRevampResponse,
                            mUrlPaymentMoliga
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                            getView()?.onException12(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        )
    }
}