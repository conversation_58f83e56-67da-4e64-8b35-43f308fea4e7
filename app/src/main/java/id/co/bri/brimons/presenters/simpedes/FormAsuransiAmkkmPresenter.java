package id.co.bri.brimons.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimons.contract.IPresenter.simpedes.IFormAsuransiAmkkmPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IFormAsuransiAmkkmView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryAmkkmRequest;
import id.co.bri.brimons.models.apimodel.response.InquiryAmkkmResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormAsuransiAmkkmPresenter<V extends IMvpView & IFormAsuransiAmkkmView> extends MvpPresenter<V> implements IFormAsuransiAmkkmPresenter<V> {

    private String url;
    @Inject
    public FormAsuransiAmkkmPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlFormAsuransiAmkkm(String url) {
        this.url = url;
    }

    @Override
    public void getInsurance() {
        InquiryAmkkmRequest inquiryAmkkmRequest = new InquiryAmkkmRequest(
                getView().getAccountNumber(),
                getView().getProductId(),
                getView().getHeirName(),
                getView().getHeirPhone(),
                getView().heirConnection(),
                getView().riskPostCode(),
                getView().riskAddress());

        getView().showProgress();
        String seq = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getData(url, inquiryAmkkmRequest, seq)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seq) {
                    @Override
                    protected void onFailureHttp(String type) {
                        getView().hideProgress();
                        getView().onException(type);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        InquiryAmkkmResponse inquiryAmkkmResponse = response.getData(InquiryAmkkmResponse.class);
                        getView().onGetInquiryAmkkm(inquiryAmkkmResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                            getView().onException12(restResponse.getDesc());
                        }
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                }));
    }

}