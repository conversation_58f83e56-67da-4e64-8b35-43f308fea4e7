package id.co.bri.brimons.presenters.loaninapp

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.loaninapp.ILoanInAppLandingPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.loaninapp.ILoanInAppLandingView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.loaninapp.LoanInAppUsernameRequest
import id.co.bri.brimons.models.apimodel.response.loaninapp.LoanInAppOnboardingResponse
import id.co.bri.brimons.models.loaninapp.toLoanInAppBenefitOnboardingModel
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class LoanInAppLandingPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ILoanInAppLandingPresenter<V> where V : IMvpView, V : ILoanInAppLandingView {
    override fun getOnboardingData(withLoading: Boolean) {
        val request = LoanInAppUsernameRequest(brImoPrefRepository.username)
        getView().getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_loan_in_app_onboarding),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            withLoading,
            request,
        ) { response ->
            val data = response.getData(LoanInAppOnboardingResponse::class.java)
            getView().onSuccessGetOnboardingData(data.toLoanInAppBenefitOnboardingModel())
        }
    }
}