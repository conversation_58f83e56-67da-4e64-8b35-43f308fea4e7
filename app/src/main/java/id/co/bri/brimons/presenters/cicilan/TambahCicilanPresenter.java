package id.co.bri.brimons.presenters.cicilan;

import id.co.bri.brimons.contract.IPresenter.cicilan.ITambahCicilanPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.cicilan.ITambahCicilanView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.CicilanFinanceRequest;
import id.co.bri.brimons.models.apimodel.request.cicilanrevamp.AddSavedListInstallmentRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.transferrevamp.SavedDataExistResponse;
import id.co.bri.brimons.models.apimodel.response.transferrevamp.SavedDataResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TambahCicilanPresenter<V extends IMvpView & ITambahCicilanView>
        extends MvpPresenter<V> implements ITambahCicilanPresenter<V> {

    private String url;

    public TambahCicilanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    /**
     * @param cicilanFinanceRequest
     */
    @Override
    public void getDataInquiry(CicilanFinanceRequest cicilanFinanceRequest) {
        if (isViewAttached()) {
            //initiate param with getter from view
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(url, cicilanFinanceRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    GeneralInquiryResponse responsecicilan = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessGetInquiry(responsecicilan);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    onApiError(restResponse);
                                }
                            }));

        }
    }

    @Override
    public void getDataInquiryAddSavedList(AddSavedListInstallmentRequest addSavedListInstallmentRequest) {
        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(url, addSavedListInstallmentRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        SavedDataExistResponse resData = response.getData(SavedDataExistResponse.class);
                                        getView().onExceptionAlreadySaved(resData.getTitle(), resData.getSubtitle());
                                    } else {
                                        SavedDataResponse resData = response.getData(SavedDataResponse.class);
                                        getView().onSuccessGetSavedDataInquiry(resData);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    onApiError(restResponse);
                                }
                            })
                    );
        }
    }

    @Override
    public void setInquiryUrl(String url) {
        this.url = url;
    }
}