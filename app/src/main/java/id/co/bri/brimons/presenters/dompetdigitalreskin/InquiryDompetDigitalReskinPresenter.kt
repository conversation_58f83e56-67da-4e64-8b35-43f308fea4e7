package id.co.bri.brimons.presenters.dompetdigitalreskin

import id.co.bri.brimons.contract.IPresenter.dompetdigitalreskin.IInquiryDompetDigitalReskinPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dompetdigitalreskin.IInquiryDompetDigitalReskinView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.ConfirmationDompetDigitalRevRequest
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.FastConfirmationDompetDigitalRevRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.base.BaseTransactionPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class InquiryDompetDigitalReskinPresenter <V>(schedulerProvider: SchedulerProvider,
                                              compositeDisposable: CompositeDisposable,
                                              mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                              categoryPfmSource: CategoryPfmSource,
                                              transaksiPfmSource: TransaksiPfmSource,
                                              anggaranPfmSource: AnggaranPfmSource): BaseTransactionPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IInquiryDompetDigitalReskinPresenter<V> where V : IMvpView, V: IInquiryDompetDigitalReskinView {

    private lateinit var mUrlConfirm: String
    private lateinit var confirmationRequest: Any

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun start() {
        super.start()
    }

    override fun getAccountDefault(): String {
        return brImoPrefRepository.accountDefault
    }

    override fun getSaldoRekeningUtama(): String {
        return brImoPrefRepository.saldoRekeningUtama
    }

}