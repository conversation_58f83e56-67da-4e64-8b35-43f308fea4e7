package id.co.bri.brimons.presenters.qrcrossborder

import id.co.bri.brimons.contract.IPresenter.qrcrossborder.IInquiryQrCrossborderPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.base.IBaseInquiryView
import id.co.bri.brimons.contract.IView.qrcrossborder.IInquiryCrossborderView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.ConfirmationRequest
import id.co.bri.brimons.models.apimodel.request.FastConfirmationRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.qr.KonfirmasiQrCrossBorderResponse
import id.co.bri.brimons.presenters.base.BaseInquiryPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class InquiryQrCrossborderPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    BaseInquiryPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        categoryPfmSource,
        transaksiPfmSource,
        anggaranPfmSource
    ),
    IInquiryQrCrossborderPresenter<V> where V : IMvpView?, V : IBaseInquiryView?, V : IInquiryCrossborderView? {


    override fun setUrlConfirmation(urlConfirmation: String?) {
        this.urlConfirmation = urlConfirmation
    }

    override fun getDataConfirmation(refNum: String?, accountNum: String?, amount: String?, save: String?, fromFast: Boolean) {
        if (isViewAttached && urlConfirmation != null) {
            //flag on Load true
            val seqNum = brImoPrefRepository.seqNumber
            view!!.showProgress()
            confirmationRequest =
            if (fromFast) FastConfirmationRequest(getFastMenuRequest(), refNum, accountNum, amount, save)
            else ConfirmationRequest(refNum, accountNum, amount, save)

            val disposable: Disposable =
                apiSource.getData(urlConfirmation, confirmationRequest, seqNum) //function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val konfirmasiQrCrossBorderResponse = response.getData(KonfirmasiQrCrossBorderResponse::class.java)
                            getView()!!.onSuccessGetConfirmationQr(konfirmasiQrCrossBorderResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView()!!.onSessionEnd(restResponse.desc) else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                    ignoreCase = true
                                )
                            ) getView()!!.onException93(restResponse.desc) else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value) {
                                getView()!!.onException99(restResponse.desc)
                            } else getView()!!.onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

}