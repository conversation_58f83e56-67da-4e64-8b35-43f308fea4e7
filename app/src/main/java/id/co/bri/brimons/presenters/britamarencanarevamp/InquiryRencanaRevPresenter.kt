package id.co.bri.brimons.presenters.britamarencanarevamp

import id.co.bri.brimons.contract.IPresenter.britamarencanarevamp.IInquiryBritamaRencanaRevPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.britamarencanarevamp.IInquiryBritamaRencanaRevView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.rencanarev.ConfirmationRencanaRevRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class InquiryRencanaRevPresenter<V>(schedulerProvider: SchedulerProvider,
                                    compositeDisposable: CompositeDisposable,
                                    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                    categoryPfmSource: CategoryPfmSource,
                                    transaksiPfmSource: TransaksiPfmSource,
                                    anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IInquiryBritamaRencanaRevPresenter<V> where V : IMvpView, V : IInquiryBritamaRencanaRevView {

    private var mUrl : String? = null
    private var generalConfirmationResponse = GeneralConfirmationResponse()


    override fun setUrlKonfirmasi(url: String) {
        mUrl = url
    }

    override fun getKonfirmasi(request: ConfirmationRencanaRevRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(mUrl, request, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView().hideProgress()
                        getView().onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        generalConfirmationResponse = response.getData(
                            GeneralConfirmationResponse::class.java
                        )

                        getView().onSuccessKonfirmasi(generalConfirmationResponse)

                        if(!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        }else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)){
                            getView().onException93(restResponse.desc)
                        }
                        else {
                            getView().onException(restResponse.desc)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoPref = brImoPrefRepository.saldoHold
        view.setDefaultSaldo(saldo, saldoString, defaultAcc,saldoPref)
    }


}