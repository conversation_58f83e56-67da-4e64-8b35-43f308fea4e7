package id.co.bri.brimons.presenters.revamp.dashboard;

import id.co.bri.brimons.contract.IPresenter.revamp.dashboard.IMenuListGeneralPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.revamp.dashboard.IMenuListGeneralView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.menudashboard.MenuDashboardSource;
import id.co.bri.brimons.data.repository.menukategori.MenuKategoriSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.MenuConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableCompletableObserver;

public class MenuListGeneralPresenter<V extends IMvpView & IMenuListGeneralView> extends MvpPresenter<V> implements IMenuListGeneralPresenter<V> {

    private MenuDashboardSource mMenuDashboardSource;
    private MenuKategoriSource mMenuKategoriSource;
    public String urlValidateMiniApp = "";

    public MenuListGeneralPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, MenuDashboardSource menuDashboardSource, MenuKategoriSource menuKategoriSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        mMenuDashboardSource = menuDashboardSource;
        mMenuKategoriSource = menuKategoriSource;
    }


    @Override
    public void updateIsNewFalse(int menuId, int menuStatus) {
        if (isViewAttached()) {
            getCompositeDisposable().add(mMenuDashboardSource.updateIsNew(menuId, menuStatus)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableCompletableObserver() {
                        @Override
                        public void onComplete() {
                            getView().onSuccessUpdate(menuId);
                        }

                        @Override
                        public void onError(@androidx.annotation.NonNull Throwable e) {
                            // do nothing
                        }
                    })
            );
        }
    }

    @Override
    public void updateKategori(int kategoriId) {
        if (isViewAttached()) {
            getCompositeDisposable().add(mMenuKategoriSource.updateKategori(kategoriId, MenuConfig.NewStatus.OLD)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableCompletableObserver() {
                        @Override
                        public void onComplete() {
                            //update object
                        }

                        @Override
                        public void onError(@androidx.annotation.NonNull Throwable e) {
                            // do nothing
                        }
                    })
            );
        }
    }
}
