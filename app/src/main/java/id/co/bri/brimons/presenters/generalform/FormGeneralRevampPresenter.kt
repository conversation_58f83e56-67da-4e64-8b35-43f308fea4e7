package id.co.bri.brimons.presenters.generalform

import id.co.bri.brimons.contract.IPresenter.generalform.IFormGeneralRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimons.contract.IView.generalform.IFormGeneralRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.enumconfig.JourneyType
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SavedResponse
import id.co.bri.brimons.models.apimodel.util.toRestResponseCodeEnum
import id.co.bri.brimons.presenters.base.BaseFormRevampPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequestState
import id.co.bri.brimons.util.extension.handleBaseResponseConvertData
import id.co.bri.brimons.util.extension.onError
import id.co.bri.brimons.util.extension.onLoading
import id.co.bri.brimons.util.extension.onSuccess
import io.reactivex.disposables.CompositeDisposable

class FormGeneralRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : BaseFormRevampPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource,
), IFormGeneralRevampPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormGeneralRevampView {
    override fun setUpdateItem(url: String, savedResponse: SavedResponse, position: Int, type: Int, journeyType: JourneyType?) {
        var saveId = ""
        var purchaseType = ""

        val updateSavedRequest =
            when (journeyType) {
                JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> {
                    val split = savedResponse.value.split("|")
                    val savedId = split.getOrElse(0) { "" }
                    UpdateSavedRequest(savedId)
                }

                JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> {
                    val split = savedResponse.value.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    if (split.size > 1) {
                        saveId = split[0]
                        purchaseType = split[1]
                    } else {
                        saveId = split[0]
                    }

                    UpdateSavedRequest(saveId, purchaseType)
                }

                else -> {
                    val split = savedResponse.value.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    if (split.size > 1) {
                        saveId = split[0]
                        purchaseType = split[1]
                    } else {
                        saveId = split[0]
                    }

                    UpdateSavedRequest(saveId, purchaseType)
                }
            }


        view.getDataWithOrWithoutRequestState(
            url,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            updateSavedRequest
        ) {
            it.handleBaseResponseConvertData { restResponse -> restResponse }
                .onSuccess { view.onSuccessUpdate(savedResponse, position, type) }
                .onError { restResponse -> onApiError(restResponse) }
                .onLoading { isShow -> if (isShow) view.showProgress() else view.hideProgress() }
        }
    }

    override fun getDataInquiry(request: Any?) {
        view.getDataWithOrWithoutRequestState(
            mUrlInquiry,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request
        ) {
            it.handleBaseResponseConvertData { restResponse -> restResponse.getData(InquiryBrivaRevampResponse::class.java) }
                .onSuccess { inquiryBrivaRevampResponse -> view.onSuccessGetInquiry(inquiryBrivaRevampResponse, mUrlPayment, mUrlConfirm) }
                .onError { restResponse ->
                    when (restResponse.code.toRestResponseCodeEnum()) {
                        RestResponse.ResponseCodeEnum.RC_58 -> getView().onException58(restResponse.desc)
                        RestResponse.ResponseCodeEnum.RC_59 -> getView().onException59(restResponse.desc)
                        RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID -> getView().onBillAlreadyPaid(restResponse.desc)
                        else -> onApiError(restResponse)
                    }
                }
                .onLoading { isShow -> if (isShow) view.showProgress() else view.hideProgress() }
        }
    }
}
