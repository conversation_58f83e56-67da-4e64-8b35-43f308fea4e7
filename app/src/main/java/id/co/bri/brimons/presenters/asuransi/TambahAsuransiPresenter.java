package id.co.bri.brimons.presenters.asuransi;

import id.co.bri.brimons.contract.IPresenter.asuransi.ITambahAsuransiPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.asuransi.ITambahAsuransiView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryAsuransiRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import io.reactivex.disposables.CompositeDisposable;

public class TambahAsuransiPresenter<V extends IMvpView & ITambahAsuransiView> extends MvpPresenter<V> implements ITambahAsuransiPresenter<V> {

    private static final String TAG = "TambahKreditPresenter";

    protected String inquiryUrl;

    public TambahAsuransiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    /**
     * Method yang digunakan untuk get Data Inquiry di setiap FORM PRESENTER
     * @param request
     */
    @Override
    public void getDataInquiry(InquiryAsuransiRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responKredit = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiry(responKredit);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }

    /**
     * Method yang digunakan untuk set Url yang akan digunakan di Halaman FORM
     * @param url
     */
    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }
}
