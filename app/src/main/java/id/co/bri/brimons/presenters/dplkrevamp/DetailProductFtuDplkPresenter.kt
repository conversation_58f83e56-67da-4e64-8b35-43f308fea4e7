package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IDetailProductFtuDplkPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IDetailProductFtuDplkView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.InquiryFormDataFtuDplkRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.InquiryRegistDplkResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.PersonalDataDplkResponse
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.fromJsonToObject
import id.co.bri.brimons.util.extension.fromObjectToJson
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailProductFtuDplkPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
),
    IDetailProductFtuDplkPresenter<V> where V : IMvpView, V : IDetailProductFtuDplkView {

    private var mUrlInquiryFormData = ""
    private var mUrlPersonalDataRegistration: String = ""

    override fun setUrlDataInquiryFormData(urlInquiryFormData: String) {
        mUrlInquiryFormData = urlInquiryFormData
    }

    override fun setUrlPersonalDataRegistration(url: String) {
        mUrlPersonalDataRegistration = url
    }

    override fun getDataInquiryFormDataFtuDplk(request: InquiryFormDataFtuDplkRequest) {
        if (isViewAttached) {
            view?.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlInquiryFormData, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(InquiryRegistDplkResponse::class.java)
                            getView().onSuccessInquiryFormDataFtuDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getPersonalDataRegistration() {
        if (!isViewAttached) {
            return
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getDataTanpaRequest(mUrlPersonalDataRegistration, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(PersonalDataDplkResponse::class.java)
                            getView().onSuccessPersonalDataDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun saveDataGraphicDplk(response: DetailPerformanceTabDplkResponse) {
        getBRImoPrefRepository().saveDataGrapichDplk(response.fromObjectToJson())
    }

    override fun getDataGraphicDplk(): DetailPerformanceTabDplkResponse {
        return getBRImoPrefRepository().getDataGraphicDplk()
            .fromJsonToObject(DetailPerformanceTabDplkResponse())
    }

    override fun deleteDataGraphicDplk() {
        getBRImoPrefRepository().deleteDataGrapichDplk()
    }
}