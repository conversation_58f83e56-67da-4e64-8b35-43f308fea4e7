package id.co.bri.brimons.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingInformationPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingInformationView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.StatusResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.OnboardingARDataRes
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.OnboardingReceiptResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingInformationPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingInformationPresenter<V> where V : IMvpView, V : IOnboardingInformationView {

    private var urlParam: String = ""

    private var urlAccount: String = ""

    override fun setUrlParam(url: String) {
        urlParam = url
    }

    override fun setUrlAccount(url: String) {
        urlAccount = url
    }

    override fun getParamAR() {
        if (urlParam.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlParam, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val arResponse = response.getData(OnboardingARDataRes::class.java)
                        if (arResponse.status == 85)
                            getView().onTermConditionData(
                                Gson().toJson(response.data),
                                arResponse.status
                            )
                        else getView().onARData(arResponse)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getAccount() {
        if (urlAccount.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlAccount, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                            getView().hideProgress()
                            val receiptResponse =
                                response.getData(OnboardingReceiptResponse::class.java)
                            getView().onSuccessCreateAccount(
                                receiptResponse
                            )
                        } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value)) {
                            val statusResponse = response.getData(StatusResponse::class.java)
                            getView().onCreateAccount(statusResponse.status)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals("C1"))
                            getView().onExceptionLogin()
                        else if (restResponse.code.equals("C2"))
                            getView().onExceptionRegistration()
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}