package id.co.bri.brimons.presenters.applyvcc

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.applyvcc.ISubmitOtpPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.applyvcc.ISubmitOtpView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.applyccrevamp.ApplyCcSubmitDataRequest
import id.co.bri.brimons.models.apimodel.request.applyccrevamp.SubmitOtpRequest
import id.co.bri.brimons.models.apimodel.response.applyccrevamp.ApplyCcSubmitDataResponse
import id.co.bri.brimons.models.applyccrevamp.toApplyCcSubmitDataModel
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequestState
import id.co.bri.brimons.util.extension.handleBaseResponseConvertData
import id.co.bri.brimons.util.extension.handleErrorEX
import id.co.bri.brimons.util.extension.handleErrorSK
import id.co.bri.brimons.util.extension.handleErrorTL
import id.co.bri.brimons.util.extension.onError
import id.co.bri.brimons.util.extension.onLoading
import id.co.bri.brimons.util.extension.onSuccess
import io.reactivex.disposables.CompositeDisposable

class SubmitOtpPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ISubmitOtpPresenter<V> where V : IMvpView, V : ISubmitOtpView {

    override fun onSubmitOtp(request: SubmitOtpRequest) {
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_submit_otp),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request,
        ) {
            val data = it

            data.onSuccess { successData -> view.onSubmitOtpSuccessState(successData) }
                    .onError {view.onSubmitOtpFailed(it)}
                    .onLoading { isLoadingData -> if (isLoadingData) view.showProgress() else view.hideProgress() }
        }
    }

    override fun onSubmitData(request: ApplyCcSubmitDataRequest) {
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_submit_data),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request,
        ) {
            val data = it.handleBaseResponseConvertData { restResponse ->
                restResponse.getData(ApplyCcSubmitDataResponse::class.java).toApplyCcSubmitDataModel()
            }

            data.onSuccess { successData -> view.onSubmitDataSuccessState(successData)}
                    .onError { errorData ->
                        when {
                            errorData.handleErrorTL() -> view.showErrorGetDataFormTLDialog(errorData.desc)
                            errorData.handleErrorSK() -> view.showErrorGetDataFormTLDialog(errorData.desc)
                            else -> view.onSubmitDataFailure(errorData.desc)
                        }}
                    .onLoading { isLoadingData -> if (isLoadingData) view.showProgress() else view.hideProgress() }
        }
    }
}