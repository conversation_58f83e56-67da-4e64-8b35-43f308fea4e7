package id.co.bri.brimons.presenters;


import id.co.bri.brimons.contract.IPresenter.IVerifikasiPinPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.IVerifikasiPinView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;

import io.reactivex.disposables.CompositeDisposable;

public class VerifikasiPresenter <V extends IMvpView & IVerifikasiPinView> extends MvpPresenter<V> implements IVerifikasiPinPresenter<V> {

    private String correctPin = "123456";

    public VerifikasiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void onKirimUlang() {

    }

    @Override
    public void onClickLanjut(String pin) {
        
    }

    @Override
    public void checkpin(String pin) {
        if(true){
            getView().onPinSucces();
        }else {
            getView().onWrongPin();
        }
    }

    @Override
    public void start() {
        //TODO : add listener for incoming SMS
    }
}
