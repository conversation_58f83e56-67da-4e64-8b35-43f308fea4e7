package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IOnboardingAutoPaymentRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IOnboardingAutoPaymentRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.InquiryAutoPaymentResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class OnboardingAutoPaymentRevampPresenter<V>(schedulerProvider: SchedulerProvider?,
                                              compositeDisposable: CompositeDisposable?,
                                              mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                              categoryPfmSource: CategoryPfmSource?,
                                              transaksiPfmSource: TransaksiPfmSource?,
                                              anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IOnboardingAutoPaymentRevampPresenter<V> where V : IMvpView?, V : IOnboardingAutoPaymentRevampView?  {

    var urlInquiryAutoPayment :String? = null


    override fun setUrlInquiryAddAutoPayment(urlAddAutopayment: String) {
        urlInquiryAutoPayment = urlAddAutopayment
    }

    override fun getInquiryAddPayment(request: InquiryAutoPaymentRequest) {
        if (urlInquiryAutoPayment == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlInquiryAutoPayment,request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            val data = response.getData(
                                InquiryAutoPaymentResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessInquiryAddAutoPayment(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView()!!.onSessionEnd(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_12.value -> getView()!!.onException12(restResponse.desc)
                                else -> getView()!!.onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

}