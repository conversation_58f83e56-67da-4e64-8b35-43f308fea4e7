package id.co.bri.brimons.ui.activities.transaction_process

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.activity.OnBackPressedCallback
import id.co.bri.brimons.R
import id.co.bri.brimons.databinding.ActivityTransactionProcessBinding
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.reskin.ReceiptType
import id.co.bri.brimons.models.AccountModel
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimons.ui.activities.receipt.reskin.ReceiptReskinActivity

class TransactionProcessActivity: NewSkinBaseActivity() {
    private var _binding: ActivityTransactionProcessBinding? = null
    protected val binding get() = _binding!!

    val animationFiles = listOf(
        "data/plane_in_lottie_reskin.json",
        "data/plane_pending_lottie_reskin.json",
        "data/plane_out_lottie_reskin.json"
    )

    companion object {
        private var receiptRevampResponse: ReceiptRevampResponse?= null

        private var dataAccount: AccountModel?= null
        private var TYPE = ReceiptType.OTHER

        @Deprecated("you can remove this function after change to on a new function")
        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, data: ReceiptRevampResponse?, sumberDana: AccountModel?, receiptType: ReceiptType = ReceiptType.OTHER) {
            isFromFastMenu = fromFastMenu
            receiptRevampResponse = data
            dataAccount = sumberDana
            TYPE = receiptType

            caller.apply {
                startActivityForResult(Intent(
                    this,
                    TransactionProcessActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, data: ReceiptRevampResponse?, receiptType: ReceiptType = ReceiptType.OTHER) {
            isFromFastMenu = fromFastMenu
            receiptRevampResponse = data
            TYPE = receiptType

            caller.apply {
                startActivityForResult(Intent(
                    this,
                    TransactionProcessActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityTransactionProcessBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onDelayHandler()

        injectDependency()

        onBindView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Tidak ngapa-ngapain
            }
        })
    }

    private fun onBindView() {
        val fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in_from_bottom)

        binding.llContent.startAnimation(fadeIn)
        binding.llContent.visibility = View.VISIBLE

        playLotties(0)
    }

    fun playLotties(index: Int) {
        if (index >= animationFiles.size) return

        binding.lottieView.setAnimation(animationFiles[index])
        binding.lottieView.playAnimation()

        binding.lottieView.addAnimatorListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                binding.lottieView.removeAnimatorListener(this)
                playLotties(index + 1)
                if(index == 1){
                    val fadeOut = AnimationUtils.loadAnimation(this@TransactionProcessActivity, R.anim.fade_out_to_top)
                    binding.llContent.startAnimation(fadeOut)
                    fadeOut.setAnimationListener(object : Animation.AnimationListener {
                        override fun onAnimationStart(animation: Animation?) {}

                        override fun onAnimationEnd(animation: Animation?) {
                            binding.llContent.visibility = View.GONE
                            Handler(Looper.getMainLooper()).postDelayed({
                                ReceiptReskinActivity.launchIntent(
                                    this@TransactionProcessActivity, isFromFastMenu,
                                    receiptRevampResponse!!, receiptType = TYPE
                                )
                                finish()
                            }, 300)
                        }

                        override fun onAnimationRepeat(animation: Animation?) {}
                    })
                }
            }
        })
    }


    private fun injectDependency() {
    }

    private fun onDelayHandler() {
//        Handler(Looper.getMainLooper()).postDelayed({
//            ReceiptReskinActivity.launchIntent(
//                this, isFromFastMenu,
//                receiptRevampResponse!!
//            )
//            finish()
//        }, 3000)
    }

    override fun onBackPressed() {

    }
}