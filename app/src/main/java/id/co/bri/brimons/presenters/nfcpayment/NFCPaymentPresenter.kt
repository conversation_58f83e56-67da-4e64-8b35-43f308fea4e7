package id.co.bri.brimons.presenters.nfcpayment

import id.co.bri.brimons.contract.IPresenter.nfcpayment.INFCPaymentPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.nfcpayment.INFCPaymentView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.BaseFormRequest
import id.co.bri.brimons.models.apimodel.request.FastMenuRequest
import id.co.bri.brimons.models.apimodel.request.nfcpayment.GeneratePayloadNfcRequest
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.nfcpayment.DetailRekeningResponse
import id.co.bri.brimons.models.apimodel.response.nfcpayment.NfcPayloadResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class NFCPaymentPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    brimoPreference: BRImoPrefSource,
    apiSource: ApiSource,
    transactionPfmSource: TransaksiPfmSource,
): MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brimoPreference,
    apiSource,
    transactionPfmSource,
), INFCPaymentPresenter<V> where V : IMvpView, V : INFCPaymentView {

    companion object {
        const val TIME_INTERVAL_CHECK_PAYMENT = 5000L
        private const val CODE_EXPIRED = "EX"
        private const val CODE_WAITING = "00"
        private const val CODE_INQUIRED = "01"
        private const val CODE_SUCCESS = "02"
    }

    private var urlCheckPayment: String? = null
    private var urlGeneratePayload: String? = null
    private var isFastMenu: Boolean? = null

    override fun init(isFastMenu: Boolean) {
        this.isFastMenu = isFastMenu
    }

    override fun setUrlCheckPayment(urlCheckPayment: String) {
        this.urlCheckPayment = urlCheckPayment
    }

    override fun setUrlGeneratePayload(urlGeneratePayload: String) {
        this.urlGeneratePayload = urlGeneratePayload
    }


    override fun startCheckPaymentStatus() {

        val seqNum = brImoPrefRepository.seqNumber

        val request = if (isFastMenu == true) {
            FastMenuRequest(brImoPrefRepository.username, brImoPrefRepository.tokenKey)
        } else BaseFormRequest("")

        compositeDisposable.add(apiSource.getData(urlCheckPayment, request, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.single())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum){
                override fun onFailureHttp(errMsg: String) {
                    getView().hideProgress()
                    getView().onException(errMsg)
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    response?.let {
                        when(it.code) {
                            CODE_EXPIRED -> {
                                getView().onPaymentExpired()
                            }
                            CODE_INQUIRED -> {
                                getView().onPaymentInquired()
                            }
                            CODE_SUCCESS -> {
                                val data = it.getData(ReceiptRevampResponse::class.java)
                                getView().onSuccessPayment(data)
                            }
                            CODE_WAITING -> {
                                getView().onWaitingPayment()
                            }
                            else -> {
                                it.getData(String::class.java)?.let { desc ->
                                    getView().onFailurePayment(desc)
                                }
                            }
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    getView().hideProgress()
                    restResponse?.let { response ->
                        if (response.code == CODE_EXPIRED) {
                            getView().onPaymentExpired()
                        } else {
                            getView().onFailurePayment(response.desc)
                        }
                    }
                }
            }))
    }

    override fun stopCheckPaymentStatus() {
        super.stop()
        if (compositeDisposable != null) {
            compositeDisposable.clear()
        }
    }

    override fun getDataPayloadNfc(accountNumber: String, token: String, pin: String, nfcType: String?) {
        getView()?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val username = brImoPrefRepository.username
        val tokenKey = brImoPrefRepository.tokenKey
        val type = nfcType?: ""

        val request = if (isFastMenu == true)
            GeneratePayloadNfcRequest(username, tokenKey, pin, accountNumber, token, type)
        else
            GeneratePayloadNfcRequest("", "", pin, accountNumber, token, type)

        compositeDisposable.add(
                apiSource.getData(urlGeneratePayload, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.newThread())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()?.hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView()?.hideProgress()
                                val data = response.getData(NfcPayloadResponse::class.java)
                                getView()?.onSuccessGetNfcPayload(data)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                view?.hideProgress()
                                if (restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else if (restResponse.code.equals(Constant.RE_FITUR_OFF, ignoreCase = true)) {
                                    val response = restResponse.getData(EmptyStateResponse::class.java)
                                    getView().onExceptionFO(response)
                                } else {
                                    getView().onException(restResponse.desc)
                                }
                            }
                        })
        )
    }

    override fun getSaldoNormal(account: String) {
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getSaldoNormal(account, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                val data = response.getData(DetailRekeningResponse::class.java)
                                getView()?.onSuccessGetSaldoNormal(data)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                if (restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else {
                                    getView().onException(restResponse.desc)
                                    getView().onErrorGetSaldo()
                                }
                            }
                        })
        )
    }

    override fun getSaldoNormalCC(account: String) {
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getSaldoCc(account, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                val data = response.getData(DetailRekeningResponse::class.java)
                                getView()?.onSuccessGetSaldoNormalCC(data)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                if (restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else {
                                    getView().onException(restResponse.desc)
                                    getView().onErrorGetSaldo()
                                }
                            }

                        })
        )
    }
}