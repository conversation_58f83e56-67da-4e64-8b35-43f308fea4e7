package id.co.bri.brimons.presenters.general;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.general.INotificationRouterPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.general.INotificationRouterView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class NotificationRouterPresenter<V extends IMvpView & INotificationRouterView> extends MvpPresenter<V> implements INotificationRouterPresenter<V> {

    private static final String TAG = "NotificationRouterPresenter";

    protected String inquiryUrl;

    public NotificationRouterPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void cekDisablePopUp() {
        if (getBRImoPrefRepository().getDisablePopupNotif()) {
            getView().onNotificationDisable();
        } else {

            //jika user adalah user IB
            if (getBRImoPrefRepository().getUserType().equals(Constant.IB_TYPE)) {
                getView().onNotificationEnable(true);
            } else {
                getView().onNotificationEnable(false);
            }

        }
    }

    @Override
    public void getDataInquiry(String reqSeq,String typeInquiry,String urlKonfirmasi, String urlPayment, String title) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataNotif(inquiryUrl, reqSeq, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse generalInquiryResponse = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiry(typeInquiry,generalInquiryResponse,urlKonfirmasi,urlPayment, title);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }

    /**
     * Method yang digunakan untuk set Url yang akan digunakan di Halaman FORM
     * @param url
     */
    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }

}
