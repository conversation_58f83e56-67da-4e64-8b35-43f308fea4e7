package id.co.bri.brimons.presenters.general

import id.co.bri.brimons.contract.IPresenter.general.IGeneralSnkWithPinPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.general.IGeneralSnkWithPinView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.PaymentRevampRequest
import id.co.bri.brimons.models.apimodel.request.general.RequestGeneralKonfirmasiInquiry
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class GeneralSnkWithPinPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IGeneralSnkWithPinPresenter<V> where V : IMvpView, V : IGeneralSnkWithPinView {

        lateinit var mUrlPayment : String
        lateinit var paymentRequest: Any


        override fun setUrlPayment(urlPayment: String) {
                this.mUrlPayment = urlPayment
        }

        override fun getDataPayment(
                pin: String?,
                note: String?,
                generalConfirmationResponse: GeneralConfirmationResponse?,
                fromFast: Boolean
        ) {
                if (!isViewAttached) {
                        return
                }

                if (isViewAttached) {
                        view.showProgress()
                        val seqNum = brImoPrefRepository.seqNumber
                        paymentRequest = PaymentRevampRequest(
                                generalConfirmationResponse!!.referenceNumber, pin, generalConfirmationResponse.pfmCategory.toString()
                        )
                        val disposable: Disposable =
                                apiSource.getData(mUrlPayment, paymentRequest, seqNum) //function(param)
                                        .subscribeOn(Schedulers.io())
                                        .observeOn(AndroidSchedulers.mainThread())
                                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                                                override fun onFailureHttp(errorMessage: String) {
                                                        getView().onException(errorMessage)
                                                }

                                                override fun onApiCallSuccess(response: RestResponse) {
                                                        getView().hideProgress()
                                                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                                                val brivaResponse = response.getData(
                                                                        ReceiptRevampResponse::class.java
                                                                )

                                                                getView().onSuccessGetPayment(brivaResponse)
                                                        }

                                                }

                                                override fun onApiCallError(restResponse: RestResponse) {
                                                        if (restResponse.code.equals("93", ignoreCase = true))
                                                                getView().onExceptionTrxExpired(restResponse.desc)
                                                        else if (restResponse.code.equals("01", ignoreCase = true))
                                                                getView().onException01(restResponse.desc)
                                                        else getView().onException(restResponse.desc)
                                                }
                                        })
                        compositeDisposable.add(disposable)
                }
        }



        override fun getDataPaymentWithRequest(
                response: GeneralConfirmationResponse?,
                request: RequestGeneralKonfirmasiInquiry
        ) {

                if (!isViewAttached) {
                        return
                }

                if (isViewAttached) {
                        //set flag Loading
                        view.showProgress()
                        val seqNum = brImoPrefRepository.seqNumber

                        val disposable: Disposable =
                                apiSource.getData(mUrlPayment, request, seqNum)
                                        .subscribeOn(Schedulers.io())
                                        .observeOn(AndroidSchedulers.mainThread())
                                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                                                override fun onFailureHttp(errorMessage: String) {
                                                        getView().onException(errorMessage)
                                                }

                                                override fun onApiCallSuccess(response: RestResponse) {
                                                        getView().hideProgress()
                                                        if (response.code.equals("00", ignoreCase = true)){
                                                                val brivaResponse = response.getData(
                                                                        ReceiptRevampResponse::class.java
                                                                )

                                                                getView().onSuccessGetPayment(brivaResponse)
                                                        }

                                                }

                                                override fun onApiCallError(restResponse: RestResponse) {
                                                        getView().hideProgress()
                                                        if (restResponse.code.equals(
                                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                                        ignoreCase = true)
                                                        ) getView().onSessionEnd(restResponse.desc)
                                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                                                getView().onExceptionTrxExpired(restResponse.desc)
                                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true))
                                                                getView().onException01(restResponse.desc)
                                                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                                                                getView().onException99(restResponse.desc)
                                                        else getView().onException(restResponse.desc)
                                                }
                                        })
                        compositeDisposable.add(disposable)
                }
        }

}