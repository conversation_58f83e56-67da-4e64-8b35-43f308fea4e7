package id.co.bri.brimons.presenters.onboardingrevamp

import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingReceiptPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingReceiptView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.OnboardingLoginResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingReceiptPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingReceiptPresenter<V> where V : IMvpView, V : IOnboardingReceiptView {

    private var urlRevoke: String = ""

    override fun saveUserLogin(onboardingLogin: OnboardingLoginResponse) {
        brImoPrefRepository.saveUsername(onboardingLogin.username)
        brImoPrefRepository.saveTokenKey(onboardingLogin.tokenKey)
        brImoPrefRepository.saveUserType(Constant.IB_TYPE)
        updateLoginFlag(true)
    }

    override fun setUrlRevokeLogin(url: String) {
        urlRevoke = url
    }

    override fun sendRevokeLogin() {
        if (urlRevoke.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlRevoke, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val onboardingLogin = response.getData(OnboardingLoginResponse::class.java)
                        getView().onSuccessRevoke(onboardingLogin)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionLogin()
                    }
                })
        )
    }

    override fun saveBubbleShowRek(termCondition: String) {
        brImoPrefRepository.saveBubbleNewOnboarding(true)
        brImoPrefRepository.saveTermCondition(termCondition)
        if (java.lang.Boolean.TRUE == GeneralHelper.checkBiometricSupport()) {
            brImoPrefRepository.saveBottomBiometric(false)
        }
    }

}