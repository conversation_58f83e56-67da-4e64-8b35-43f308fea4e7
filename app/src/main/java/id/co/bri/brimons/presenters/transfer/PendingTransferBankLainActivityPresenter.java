package id.co.bri.brimons.presenters.transfer;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.transfer.IPendingTransferBankLainPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transfer.IPendingTransferBankLainView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.StatusFastRequest;
import id.co.bri.brimons.models.apimodel.request.StatusRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by FNS
 */

public class PendingTransferBankLainActivityPresenter<V extends IMvpView & IPendingTransferBankLainView> extends MvpPresenter<V> implements IPendingTransferBankLainPresenter<V> {

    private static final String TAG = "PendingTransferBankLain";

    Transaksi newTransaksi = null;

    private boolean onLoad = false;

    public PendingTransferBankLainActivityPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getDataPaid(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse) {
        if (onLoad) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }
        //initiate param with getter from view

        getView().showProgress();
        onLoad = true;
        StatusRequest request = new StatusRequest(pendingResponse.getReferenceNumber());
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getData(mUrl, request, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PendingResponse pendingResponse = response.getData(PendingResponse.class);
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            if (pendingResponse.getImmediatelyFlag())
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        generalConfirmationResponse.getPfmCategory(),
                                        generalConfirmationResponse.getPayAmount(),
                                        generalConfirmationResponse.getReferenceNumber(),
                                        generalConfirmationResponse.getPfmDescription())
                                );
                            getView().onGetData(pendingResponse);
                            onLoad = false;
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onGetTransaksiGagal(response);
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                            getView().onException93(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void getDataPaidFastMenu(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse) {
        if (onLoad) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }
        //initiate param with getter from view

        getView().showProgress();
        onLoad = true;
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        StatusFastRequest request = new StatusFastRequest(pendingResponse.getReferenceNumber(), getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey());
        Disposable disposable = getApiSource().getData(mUrl, request, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PendingResponse pendingResponse = response.getData(PendingResponse.class);
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            getView().onGetData(pendingResponse);
                            onLoad = false;
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getPayAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    generalConfirmationResponse.getPfmDescription())
                            );
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onGetTransaksiGagal(response);
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                            getView().onException93(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void getDataPaidPendidikan(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse) {
        if (onLoad) {
            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }
        //initiate param with getter from view

        getView().showProgress();
        onLoad = true;
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataForm(mUrl, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PendingResponse pendingResponse = response.getData(PendingResponse.class);
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            getView().onGetData(pendingResponse);
                            onLoad = false;
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getPayAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    generalConfirmationResponse.getPfmDescription())
                            );
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onGetTransaksiGagal(response);
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                            getView().onException93(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        //create disposible
        if (transaksi != null) {
            DisposableSingleObserver disposableSingleObserver = new DisposableSingleObserver<Long>() {
                @Override
                public void onSuccess(Long id) {
                    Log.d(TAG, "onSuccess Save : " + transaksi.getRefnum());
                }

                @Override
                public void onError(Throwable e) {
                    /*
                    if(BuildConfig.DEBUG)
                    e.printStackTrace();

                     */
                }
            };

            getTransaksiPfmSource().saveTransaksiPfm(transaksi)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(disposableSingleObserver);

            getCompositeDisposable().add(disposableSingleObserver);
        }
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public boolean onLoadGetReceipt() {
        return onLoad;
    }

    @Override
    public void stop() {
        newTransaksi = null;
        super.stop();
    }
}