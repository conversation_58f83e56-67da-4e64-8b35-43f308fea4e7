package id.co.bri.brimons.presenters;

import id.co.bri.brimons.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.ValidationHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastMenuRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import io.reactivex.disposables.CompositeDisposable;

public class MvpPresenter<V extends IMvpView> implements IMvpPresenter<V> {

    private static final String TAG = "MvpPresenter";

    protected V mView;

    //Rx utility
    private final SchedulerProvider schedulerProvider;
    private final CompositeDisposable compositeDisposable;

    //model dari sharedpreference
    private final BRImoPrefSource mBRImoPrefRepository;

    //model dari service
    private final ApiSource apiSource;

    //model dari repository
    private final TransaksiPfmSource transaksiPfmSource;

    protected FastMenuRequest fastMenuRequest = null;

    protected boolean onLoad = false;


    public MvpPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, TransaksiPfmSource transaksiPfmSource) {
        this.schedulerProvider = schedulerProvider;
        this.compositeDisposable = compositeDisposable;
        this.mBRImoPrefRepository = mBRImoPrefRepository;
        this.apiSource = apiSource;
        this.transaksiPfmSource = transaksiPfmSource;
    }


    @Override
    public TransaksiPfmSource getTransaksiPfmSource() {
        return transaksiPfmSource;
    }


    /**
     * Method digunakan untuk memaping
     *
     * @param restResponse
     */

    @Override
    public void onApiError(RestResponse restResponse) {
        onLoad = false;
        getView().hideProgress();

        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
            getView().onSessionEnd(restResponse.getDesc());
        else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
            getView().onException99(restResponse.getDesc());
        } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_FO.getValue())) {
            getView().onException99(restResponse.getDesc());
        }
        getView().onException(restResponse.getDesc());

    }

    /**
     * Merthod digunakan untuk memproses balikan berhasil dari RX android (Rest Service)
     *
     * @param response
     */

    @Override
    public void onApiSuccess(RestResponse response) {
        onLoad = false;
        getView().hideProgress();
    }

    /**
     * Method digunakan untuk memproses balikan error HTTP dari RX
     *
     * @param error string error
     */

    @Override
    public void onFailure(String error) {
        getView().hideProgress();
        onLoad = false;
        getView().onException(error);
    }

    @Override
    public FastMenuRequest getFastMenuRequest() {
        if (fastMenuRequest == null) {

            fastMenuRequest = new FastMenuRequest(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey());
            return fastMenuRequest;
        } else {
            return fastMenuRequest;
        }

    }


    @Override
    public boolean isViewAttached() {
        return mView != null;
    }

    @Override
    public SchedulerProvider getSchedulerProvider() {
        return schedulerProvider;
    }

    @Override
    public ApiSource getApiSource() {
        return apiSource;
    }

    @Override
    public CompositeDisposable getCompositeDisposable() {
        return compositeDisposable;
    }

    @Override
    public void start() {
        //no start
    }


    @Override
    public void stop() {
        //clear composite disposable
        if (!isCompositeDisposableDisposed(getCompositeDisposable())) {
            getCompositeDisposable().dispose();
        }
    }

    protected boolean isCompositeDisposableDisposed(CompositeDisposable compositeDisposable) {
        if (compositeDisposable != null) {
            return compositeDisposable.isDisposed();
        } else {
            return true;
        }
    }

    @Override
    public void setView(V view) {
        mView = view;
    }


    @Override
    public V getView() {
        return mView;
    }

    @Override
    public BRImoPrefSource getBRImoPrefRepository() {
        return mBRImoPrefRepository;
    }

    @Override
    public void setDisablePopup(boolean disableNotif) {
        getBRImoPrefRepository().disablePopupNotif(disableNotif);
    }

    /**
     * Method validasi request API
     *
     * @param value string request
     * @return boolean validasi
     */
    @Override
    public boolean validateValueRequest(String value) {
        boolean result = false;

        if (value != null) {
            if (!value.isEmpty())
                result = true;
            else
                result = false;
        } else {
            result = false;
        }

        return result;
    }

    /**
     * Method validasi username
     *
     * @param value string request
     * @return boolean validasi
     */
    @Override
    public boolean validateValueUsername(String value) {
        boolean result = false;

        if (ValidationHelper.isValidUsername(value)) {
            result = true;
        }

        return result;

    }

    @Override
    public void updateLoginFlag(boolean isLogin) {
        getBRImoPrefRepository().saveLoginFlag(isLogin);
    }

    @Override
    public boolean getLoginFlag() {
        return getBRImoPrefRepository().getLoginFlag();
    }

    @Override
    public String getPersistenceId() {
        String id = mBRImoPrefRepository.getDeviceId2();
        return id;
    }

    @Override
    public String getLanguage() {
        return getBRImoPrefRepository().getLanguange();
    }
}