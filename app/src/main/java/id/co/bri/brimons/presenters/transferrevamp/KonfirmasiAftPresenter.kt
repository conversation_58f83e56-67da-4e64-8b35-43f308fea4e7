package id.co.bri.brimons.presenters.transferrevamp

import id.co.bri.brimons.contract.IPresenter.transferrevamp.IKonfirmasiAftPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.transferrevamp.IKonfirmasiAftView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.RencanaPaymentRequest
import id.co.bri.brimons.models.apimodel.response.ExtrasResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class KonfirmasiAftPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IKonfirmasiAftPresenter<V> where V : IMvpView?, V : IKonfirmasiAftView? {
    private var urlPayment: String? = null
    private var paymentRequest: Any? = null

    override fun getDataPayment(pin: String, refnum: String?, isFromEdit: Boolean) {
        if (urlPayment == null) {
            return
        }
        if (!isViewAttached) {
            return
        }
        if (isViewAttached) {
            //set flag Loading
            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            paymentRequest = RencanaPaymentRequest(pin, refnum)

            val disposable: Disposable =
                apiSource.getData(urlPayment, paymentRequest, seqNum) //function(param)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            if (!isFromEdit) {
                                val responseInfo = response.getData(ExtrasResponse::class.java)
                                getView()?.onSuccessPayCreate(responseInfo)
                            } else {
                                getView()?.onSuccessPayEdit(response.desc)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView()?.onSessionEnd(restResponse.desc) else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                    ignoreCase = true
                                )
                            ) getView()?.onExceptionTrxExpired(restResponse.desc) else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_01.value,
                                    ignoreCase = true
                                )
                            ) getView()?.onException01(restResponse.desc) else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value) {
                                getView()?.onException99(restResponse.desc)
                            } else {
                                getView()?.onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun setUrlPayment(urlPayment: String) {
        this.urlPayment = urlPayment
    }


    override fun start() {
        super.start()
        setDisablePopup(true)
    }

    override fun stop() {
        super.stop()
    }
}