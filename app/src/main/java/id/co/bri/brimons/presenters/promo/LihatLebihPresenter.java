package id.co.bri.brimons.presenters.promo;

import id.co.bri.brimons.contract.IPresenter.promo.ILihatLebihPromoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.promo.ILihatLebihPromoView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimons.models.apimodel.response.PromoResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class LihatLebihPresenter<V extends IMvpView & ILihatLebihPromoView> extends MvpPresenter<V> implements ILihatLebihPromoPresenter<V> {
    private static final String TAG = "LihatLebihPresenter";

    private DisposableSingleObserver disposableSingleObserver = null;
    private String formUrl;

    protected Object detailPromoRequest;

    public LihatLebihPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDetailItem(String id) {
        if (formUrl == null || !isViewAttached()) {
            return;
        }


        getView().showProgress();

        detailPromoRequest = new DetailPromoRequest(id);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(formUrl, detailPromoRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                PromoResponse responsebriva = response.getData(PromoResponse.class);

                                getView().onSuccessGetDetailItem(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }
}