package id.co.bri.brimons.presenters.transferrevamp

import id.co.bri.brimons.contract.IPresenter.transferrevamp.IDetailAftPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.transferrevamp.IDetailAftView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.revamptransfer.DetailAftRequest
import id.co.bri.brimons.models.apimodel.request.revamptransfer.RemoveAftRequest
import id.co.bri.brimons.models.apimodel.response.InquiryTransferRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SafetyModeResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class DetailAftPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IDetailAftPresenter<V> where V : IMvpView?, V : IDetailAftView? {
    private var mUrlRemoveAft: String? = null
    private var mUrlInquiryEdit: String? = null
    private var mUrlSafetyMode: String? = null


    override fun removeAft(idAft: String, pin: String) {
        if (!isViewAttached) {
            return
        }
        //set flag Loading
        val seqNum = brImoPrefRepository.seqNumber
        view?.showProgress()
        val removeAftRequest = RemoveAftRequest(idAft, pin)
        val disposable: Disposable =
            apiSource.getData(mUrlRemoveAft, removeAftRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        getView()?.onSuccessRemoveAft()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                            getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        compositeDisposable.add(disposable)
    }

    override fun setUrlRemove(urlRemoveAft: String) {
        this.mUrlRemoveAft = urlRemoveAft
    }

    override fun setUrlInquiryEdit(urlEdit: String) {
        this.mUrlInquiryEdit = urlEdit
    }

    override fun setUrlTimerSafetyMode(url: String) {
        this.mUrlSafetyMode = url
    }

    override fun inquiryEditAft(idAft: String) {
        if (mUrlInquiryEdit == null)
            return

        if (!isViewAttached)
            return

        //set flag Loading
        val seqNum = brImoPrefRepository.seqNumber
        view?.showProgress()
        val detailAftRequest = DetailAftRequest(idAft)
        val disposable: Disposable =
            apiSource.getData(mUrlInquiryEdit, detailAftRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val responseInquiry = response.getData(InquiryTransferRevampResponse::class.java)
                        getView()?.hideProgress()
                        getView()?.onSuccessGetInquiryEdit(responseInquiry)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                            getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        compositeDisposable.add(disposable)
    }


    override fun getTimerSafetyMode() {
        view?.showProgress()
        if (!isViewAttached() || mUrlSafetyMode?.isEmpty() == true) return
        val seqNum = getBRImoPrefRepository().getSeqNumber()
        getCompositeDisposable().add(
            getApiSource().getDataTanpaRequest(mUrlSafetyMode, seqNum)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val safetyModeResponse = response.getData(
                                SafetyModeResponse::class.java
                            )
                            if (safetyModeResponse.ttl.toInt()>0)
                                getView()?.isOnConditionSafetyMode(true)
                            else  getView()?.isOnConditionSafetyMode(false)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun start() {
        super.start()
    }

    override fun stop() {
        super.stop()
    }
}