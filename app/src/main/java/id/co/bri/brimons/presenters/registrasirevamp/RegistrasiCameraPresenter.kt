package id.co.bri.brimons.presenters.registrasirevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.registrasirevamp.IRegistrasiCameraPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.registrasirevamp.IRegistrasiCameraView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.registrasi.RegisIdRequest
import id.co.bri.brimons.models.apimodel.request.registrasi.RegisKycRequest
import id.co.bri.brimons.models.apimodel.response.OcrKtpErrorResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.StatusResponse
import id.co.bri.brimons.models.apimodel.response.registrasi.RegisPendingResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiCameraPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiCameraPresenter<V> where V : IMvpView, V : IRegistrasiCameraView {

    private var urlKtp: String = ""
    private lateinit var urlPhotoKtp: String

    override fun getDeviceId() {
        view.getRegisId(brImoPrefRepository.deviceId)
    }

    override fun setUrlCheckKtp(url: String) {
        urlKtp = url
    }

    override fun setUrlPhotoKtp(url: String) {
        urlPhotoKtp = url
    }

    override fun sendPhotoKtp(regisKycRequest: RegisKycRequest) {
        if (urlPhotoKtp.isNotEmpty() && isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(urlPhotoKtp, regisKycRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisPendingResponse = response.getData(RegisPendingResponse::class.java)
                            getView().onSuccessRegistrasi(regisPendingResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun sendCheckKtp() {
        if (urlKtp.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        val request = RegisIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlKtp, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessStatusKtp(Gson().toJson(response.data))
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code == Constant.RE_INVALID_KTP){
                            val ocrErrorResponse = restResponse.getData(OcrKtpErrorResponse::class.java)
                            getView().onExceptionInvalidKtp(ocrErrorResponse)
                        }

                        else getView().onException(restResponse.desc)
                    }
                })
        )
    }
}