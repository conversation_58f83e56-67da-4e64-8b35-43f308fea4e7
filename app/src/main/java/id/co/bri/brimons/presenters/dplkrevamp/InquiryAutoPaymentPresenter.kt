package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IInquiryAutoPaymentPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IInquiryAutoPaymentView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.SubmitAutoPaymentRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.SubmitAutoPaymentResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class InquiryAutoPaymentPresenter<V>(schedulerProvider: SchedulerProvider?,
                                         compositeDisposable: CompositeDisposable?,
                                         mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                         categoryPfmSource: CategoryPfmSource?,
                                         transaksiPfmSource: TransaksiPfmSource?,
                                         anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IInquiryAutoPaymentPresenter<V> where V : IMvpView?, V : IInquiryAutoPaymentView?  {

    var urlInquirySubmitAutoPayment :String? = null

    override fun setUrlSubmitAutoPayment(urlSubmitAutopayment: String) {
        urlInquirySubmitAutoPayment = urlSubmitAutopayment
    }



    override fun submitAutoPayment(request: SubmitAutoPaymentRequest) {
        if (urlInquirySubmitAutoPayment == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlInquirySubmitAutoPayment,request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            val data = response.getData(
                                SubmitAutoPaymentResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessInquirySubmitAutoPayment(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView()!!.onExceptionTrxExpired(restResponse.desc)
                                else -> getView()!!.onException(
                                    restResponse.desc
                                )
                            }

                        }
                    })
            )
    }


    fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != null && saldoText.isNotEmpty()) { // Add null check and isEmpty() check
            saldo = java.lang.Double.valueOf(saldoText.trim()) // Add trim() inside the null check
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view!!.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }


    override fun start() {
        super.start()
        getDefaultSaldo()
    }


}