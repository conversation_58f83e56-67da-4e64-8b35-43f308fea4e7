package id.co.bri.brimons.presenters.dompetdigitalreskin

import id.co.bri.brimons.contract.IPresenter.dompetdigitalreskin.IEditBindingDompetDigitalPresenter
import id.co.bri.brimons.contract.IView.dompetdigitalreskin.IEditBindingDompetDigitalView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.EwalletBindingTypeRequest
import id.co.bri.brimons.models.apimodel.response.EwalletUnbindingResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class EditBindingDompetDigitalPresenter<V : IEditBindingDompetDigitalView>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    brImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(schedulerProvider, compositeDisposable, brImoPrefRepository, apiSource,
    transaksiPfmSource
),
    IEditBindingDompetDigitalPresenter<V> {

    private lateinit var urlEwalletUnbinding: String
    private var eWalletRequest: EwalletBindingTypeRequest? = null

    override fun setUrlEwalletUnBinding(url: String) {
        urlEwalletUnbinding = url
    }

    override fun setUnbindingEwallet(type: String?) {
        if (!isViewAttached() || !::urlEwalletUnbinding.isInitialized || urlEwalletUnbinding.isEmpty()) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        getView().showProgress()
        eWalletRequest = EwalletBindingTypeRequest(type)
        
        compositeDisposable.add(
            apiSource.getData(urlEwalletUnbinding, eWalletRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {

                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val ewalletUnbindingResponse = response.getData(EwalletUnbindingResponse::class.java)
                        getView().hideProgress()
                        getView().onSuccessUnbindingeWallet(ewalletUnbindingResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }
}
