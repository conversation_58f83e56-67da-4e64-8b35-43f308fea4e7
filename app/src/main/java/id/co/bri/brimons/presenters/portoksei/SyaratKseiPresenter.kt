package id.co.bri.brimons.presenters.portoksei

import id.co.bri.brimons.contract.IPresenter.portoksei.ISyaratKseiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.portoksei.ISyaratKseiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.portofolioksei.SyaratKseiRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.portofolioksei.RegistrasiResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class SyaratKseiPresenter<V>(schedulerProvider: SchedulerProvider?,
                             compositeDisposable: CompositeDisposable?,
                             mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                             categoryPfmSource: CategoryPfmSource?,
                             transaksiPfmSource: TransaksiPfmSource?,
                             anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        ISyaratKseiPresenter<V> where V : IMvpView?, V : ISyaratKseiView {

    var urlKsei : String? = null

    override fun setUrl(url: String) {
        urlKsei = url
    }

    override fun getDataPin(req : SyaratKseiRequest) {
        if (urlKsei == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(urlKsei, req, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        //TO-DO onSuccess
                        getView()!!.hideProgress()
                        GeneralHelper.responseChuck(response)
                        val registrasiResponse = response.getData(RegistrasiResponse::class.java)
                        getView()!!.onSuccessGetPin(registrasiResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                        ignoreCase = true
                                )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if(restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                        ignoreCase = true)){
                                   getView()!!.onException93(
                                            restResponse.desc)
                                }
                        else getView()!!.onException(
                                restResponse.desc
                        )
                    }
                })
        )
    }
}