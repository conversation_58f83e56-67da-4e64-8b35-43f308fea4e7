package id.co.bri.brimons.presenters.qrtransfer;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.qrtransfer.IPendingTransferQrPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.qrtransfer.IPendingTransferQrView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.StatusRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class PendingTransferQrPresenter<V extends IMvpView & IPendingTransferQrView> extends MvpPresenter<V> implements IPendingTransferQrPresenter<V> {

    private static final String TAG = "PendingTransferQrPresen";

    private boolean onLoad = false;

    public PendingTransferQrPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataPaid(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse) {
        if (onLoad) {
            return;
        }
        //initiate param with getter from view

        getView().showProgress();
        onLoad = true;
        StatusRequest request = new StatusRequest(pendingResponse.getReferenceNumber());
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getData(mUrl, request, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PendingResponse pendingResponse = response.getData(PendingResponse.class);
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            PendingResponse brivaResponse = response.getData(PendingResponse.class);
                            if (brivaResponse.getImmediatelyFlag())
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        generalConfirmationResponse.getPfmCategory(),
                                        generalConfirmationResponse.getPayAmount(),
                                        generalConfirmationResponse.getReferenceNumber(),
                                        generalConfirmationResponse.getPfmDescription())
                                );
                            getView().onGetData(pendingResponse);
                            onLoad = false;
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onGetTransaksiGagal(response);
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        //create disposible
        if (transaksi != null) {
            DisposableSingleObserver disposableSingleObserver = new DisposableSingleObserver<Long>() {
                @Override
                public void onSuccess(Long id) {
                    Log.d(TAG, "onSuccess Save : " + transaksi.getRefnum());
                }

                @Override
                public void onError(Throwable e) {
                    /*
                    if(BuildConfig.DEBUG)
                    e.printStackTrace();

                     */
                }
            };

            getTransaksiPfmSource().saveTransaksiPfm(transaksi)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(disposableSingleObserver);

            getCompositeDisposable().add(disposableSingleObserver);
        }
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public boolean onLoadGetReceipt() {
        return onLoad;
    }
}