package id.co.bri.brimons.presenters.briva;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.briva.IFormBrivaPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.briva.IFormBrivaView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryBrivaRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormBrivaPresenter<V extends IMvpView & IBaseFormView & IFormBrivaView> extends BaseFormPresenter<V> implements IFormBrivaPresenter<V> {

    private static final String TAG = "FormBrivaPresenter";

    public FormBrivaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }


    /**
     * @param noBriva
     * @param fromFastMenu
     */
    @Override
    public void getDataInquiry(String noBriva, boolean fromFastMenu) {

        if (isViewAttached()) {
            //initiate param with getter from view

            getView().showProgress();

            InquiryBrivaRequest inquiryRequest = new InquiryBrivaRequest(noBriva, getFastMenuRequest().getUsername(), getFastMenuRequest().getTokenKey());
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    try {
                                        GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                        if (inquiryUrl != null && konfirmasiUrl != null) {
                                            getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl, fromFastMenu);
                                        }
                                    }catch (Exception e){
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_58.getValue()))
                                        getView().onException58(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }
}