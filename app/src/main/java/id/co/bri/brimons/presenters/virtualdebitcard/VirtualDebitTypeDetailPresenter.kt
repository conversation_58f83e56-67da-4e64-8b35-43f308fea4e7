package id.co.bri.brimons.presenters.virtualdebitcard

import id.co.bri.brimons.contract.IPresenter.virtualdebitcard.IVirtualDebitTypeDetailPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.virtualdebitcard.IVirtualDebitTypeDetailView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.InquiryBrizziRequest
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class VirtualDebitTypeDetailPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVirtualDebitTypeDetailPresenter<V> where V : IMvpView, V : IVirtualDebitTypeDetailView {

    private lateinit var urlGenerateCVV: String
    private lateinit var urlEnableDisableTransactionVDC: String
    private lateinit var urlGetDetailVirtualCard: String
    private lateinit var urlUpdateLabelVDC: String

    private lateinit var requestDetailVDC: InquiryBrizziRequest

    override fun getDetailVDC(cardNumber: String) {
    }
}