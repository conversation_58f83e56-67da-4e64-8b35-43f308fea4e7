package id.co.bri.brimons.presenters.travel;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.travel.IFormBusPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.travel.IFormBusView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.SearchBusRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SearhBusResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormBusPresenter <V extends IMvpView & IBaseFormView & IFormBusView> extends BaseFormPresenter<V>
        implements IFormBusPresenter<V> {

    String inquiryUrl;

    public FormBusPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.inquiryUrl = url;
    }

    @Override
    public void getDataBus(SearchBusRequest searchBusRequest) {
        inquiryRequest = searchBusRequest;
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .timeout(60, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                SearhBusResponse searhBusResponse = response.getData(SearhBusResponse.class);
                                getView().onSuccessGetDataBus(searhBusResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getSwitchButton() {
        getView().switchCity();
    }

    @Override
    public String getCityA() {
        return getView().getCityDepart().getText().toString();
    }

    @Override
    public String getCityB() {
        return getView().getCityReturn().getText().toString();
    }
}