package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.ISimulasiDplkRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.ISimulasiDplkRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.InquiryDplkRegisRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.SimulasiDplkRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplk.DplkBoardingResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.FormPilihBrifineResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.SimulasiDplkResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class SimulasiDplkRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                                     compositeDisposable: CompositeDisposable,
                                     mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource?,
                                     categoryPfmSource: CategoryPfmSource,
                                     transaksiPfmSource: TransaksiPfmSource,
                                     anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
        ISimulasiDplkRevampPresenter<V> where V : IMvpView, V : ISimulasiDplkRevampView {

    private var urlSimulasi: String = ""
    private var mUrlInquiryDplk: String = ""
    private var mUrlRegisDplk: String = ""


    override fun setUrlSimulasiDplk(urlSimulasiDplk: String) {
        this.urlSimulasi = urlSimulasiDplk
    }

    override fun setUrlInquiryDplk(urlInquryDplk: String) {
        mUrlInquiryDplk = urlInquryDplk
    }

    override fun setUrlRegistrationDplk(urlRegistrationDplk: String) {
        mUrlRegisDplk = urlRegistrationDplk
    }

    override fun getDataSimulasiDplk(request: SimulasiDplkRequest) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlSimulasi, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()

            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.hideProgress()
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(SimulasiDplkResponse::class.java)

                            getView()?.onSuccessGetSimulasiDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataInquiryDplk(request: InquiryDplkRegisRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlInquiryDplk, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(FormPilihBrifineResponse::class.java)
                            getView().onSuccessInquiryDplkRegis(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getRegistrationDplk() {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getDataTanpaRequest(mUrlRegisDplk, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val data = response.getData(DplkBoardingResponse::class.java)
                        getView().onSuccessDplkRegis(data)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        getView().onException(restResponse.desc)
                    }
                })
        )

    }
}