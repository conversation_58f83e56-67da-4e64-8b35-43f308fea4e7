package id.co.bri.brimons.presenters.cc_sof

import id.co.bri.brimons.contract.IPresenter.cc_sof.IMerchantConnectPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.cc_sof.IMerchantConnectView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.CheckBlockRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.cc.MerchantConnectRes
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class MerchantConnectPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource,
), IMerchantConnectPresenter<V> where V : IMvpView, V : IMerchantConnectView {

    private var url = ""

    override fun setGetMerchantUrl(url: String) {
        this.url = url
    }

    override fun getMerchantConnect(cardToken: String) {
        if (url.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val request = CheckBlockRequest(cardToken)

        compositeDisposable.add(
            apiSource.getData(url, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val merchantResponse = response.getData(MerchantConnectRes::class.java)
                        if (response.code == "00") {
                            getView().onSuccessGetMerchant(merchantResponse)
                        } else getView().merchantNotFound(merchantResponse)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }

}