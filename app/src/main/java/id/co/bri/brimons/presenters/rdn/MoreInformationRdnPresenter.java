package id.co.bri.brimons.presenters.rdn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.rdn.IMoreInformationRdnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.rdn.IMoreInformationRdnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.rdn.RdnFormSofRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnCheckpointResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnMoveSbnResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class MoreInformationRdnPresenter <V extends IMvpView & IMoreInformationRdnView> extends MvpPresenter<V> implements IMoreInformationRdnPresenter<V> {

    private String url;

    public MoreInformationRdnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onSubmitFormSofRdn(RdnFormSofRequest request) {
        if (isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(url,request,seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                        RdnCheckpointResponse rdnCheckpointResponse = response.getData(RdnCheckpointResponse.class);
                                        getView().onSuccessSendForm(rdnCheckpointResponse.getCheckpointId());
                                    }else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_S1.getValue())){
                                        RdnMoveSbnResponse rdnMoveSbnResponse = response.getData(RdnMoveSbnResponse.class);
                                        getView().onSuccessS1case(rdnMoveSbnResponse);
                                    }else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_S2.getValue())){
                                        RdnMoveSbnResponse rdnMoveSbnResponse = response.getData(RdnMoveSbnResponse.class);
                                        getBRImoPrefRepository().saveCheckPointRdn(rdnMoveSbnResponse.getCheckpointId());
                                        getView().onSuccessS2case(rdnMoveSbnResponse);
                                    }

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    }
                                    else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            }));
        }
    }

    @Override
    public void onGetRdnFirst() {
        getView().onSuccessFirstRdn(getBRImoPrefRepository().getFirstRdn());
    }

    /**
     * Method yang digunakan untuk save checkpoint
     */
    @Override
    public void saveRdnCheckPoint(String checkPoint) {
        getBRImoPrefRepository().saveCheckPointRdn(checkPoint);
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    public void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();

        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        getView().setDefaultSaldo(saldo, saldoString, defaultAcc);
    }

}