package id.co.bri.brimons.presenters.dompetdigitalreskin

import id.co.bri.brimons.contract.IPresenter.IMvpPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.domain.config.enumconfig.JourneyType
import id.co.bri.brimons.models.apimodel.response.SavedResponse


interface ISearchSavedHistoryDompetDigitalPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUpdateItem(url: String, savedResponse: SavedResponse, position: Int, type: Int, journeyType: JourneyType? = null)

    fun setUrlInquiry(urlInquiry: String)

    fun setUrlConfirm(urlConfirm: String)

    fun setUrlPayment(urlPayment: String)

    fun getUrlInquiry(): String

    fun getUrlConfirm(): String

    fun getUrlPayment(): String

    fun getDataInquirySaved(
        isFromFastMenu: Boolean,
        walletCode: String,
        corpCode: String,
        purchaseNumber: String
    )
}
