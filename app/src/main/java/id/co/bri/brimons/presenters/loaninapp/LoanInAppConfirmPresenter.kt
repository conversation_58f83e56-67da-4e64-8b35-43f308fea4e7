package id.co.bri.brimons.presenters.loaninapp

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.loaninapp.ILoanInAppConfirmPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.loaninapp.ILoanInAppConfirmView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.applyccrevamp.ApplyCcGetOtpRequest
import id.co.bri.brimons.models.apimodel.response.applyccrevamp.GetOtpGeneralResponse
import id.co.bri.brimons.models.applyccrevamp.toGetOtpGeneralModel
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class LoanInAppConfirmPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ILoanInAppConfirmPresenter<V> where V : IMvpView, V : ILoanInAppConfirmView {
    override fun getOtp(cardToken: String) {
        view.getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_loan_in_send_otp),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            ApplyCcGetOtpRequest("", cardToken, ""),
        ) {
            val data = it.getData(GetOtpGeneralResponse::class.java).toGetOtpGeneralModel()
            view.getOtpSuccess(data)
        }
    }
}