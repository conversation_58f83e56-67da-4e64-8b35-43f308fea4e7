package id.co.bri.brimons.presenters.britamajunio;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.britamajunio.IDetailRekeningJunioPresenter;

import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.britamajunio.IDetailRekeningJunioView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.DefaultRekeningRequest;
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimons.models.apimodel.request.IdRequest;
import id.co.bri.brimons.models.apimodel.request.PinFinansialRequest;
import id.co.bri.brimons.models.apimodel.request.StatusKartuRequest;
import id.co.bri.brimons.models.apimodel.response.DetailJunioResponse;
import id.co.bri.brimons.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimons.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimons.models.apimodel.response.QuestionResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class DetailJunioPresenter<V extends IMvpView & IDetailRekeningJunioView>
        extends MvpPresenter<V> implements IDetailRekeningJunioPresenter<V> {

    protected String url;
    protected String urlStatus;
    protected String urlDefault;

    protected String urlFinansialRek;

    protected String urlInfoSaldoHold;

    private String urlDetail;

    public DetailJunioPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setDetailJunio(String accountNumber) {
        if (url == null || !isViewAttached()) {
            return;
        }

        StatusKartuRequest statusKartuRequest = new StatusKartuRequest(accountNumber);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(url, statusKartuRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                DetailJunioResponse detailJunioResponse = response.getData(DetailJunioResponse.class);
                                getView().onSuccessDetailJunio(detailJunioResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                    getView().onException12(restResponse.getDesc());
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                                GeneralHelper.responseChuck(restResponse);

                            }
                        })
        );
    }

    @Override
    public void setUrlDetailJunio(String url) {
        this.url = url;
    }

    @Override
    public void setUrlStatus(String urlStatus) {
        this.urlStatus = urlStatus;
    }

    @Override
    public void setUrlDefault(String urlDefault) {
        this.urlDefault = urlDefault;
    }

    @Override
    public void setUrlFinansialRek(String urlFinansialRek) {
        this.urlFinansialRek = urlFinansialRek;
    }

    @Override
    public void setUrlInfoSaldoHold(String urlInfoSaldoHold) {
        this.urlInfoSaldoHold = urlInfoSaldoHold;
    }

    @Override
    public void setUrlDetailStatus(String urlDetail) {
        this.urlDetail = urlDetail;
    }

    @Override
    public void getInfoSaldoHold() {
        if (urlInfoSaldoHold != null || isViewAttached()) {
            getView().showProgress();

            IdRequest idRequest = new IdRequest("105");
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInfoSaldoHold, idRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    //do nothing
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    QuestionResponse questionResponse = response.getData(QuestionResponse.class);
                                    getView().onSuccessInfoSaldoHold(questionResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void sendFinansialRek(PinFinansialRequest pinRequest, int statusFinansial) {
        if (!urlFinansialRek.isEmpty() && isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlFinansialRek, pinRequest, seqNum)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (statusFinansial == 0) {
                                        GeneralOtpResponse generalOtpResponse = response.getData(GeneralOtpResponse.class);
                                        getView().onSuccessGetFinansial(generalOtpResponse);
                                    } else {
                                        getView().onSuccessNonaktifFinansial();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                        getView().onException12(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void setChangeDefault(String accountNumber) {
        if (url == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        DefaultRekeningRequest defaultRekeningRequest = new DefaultRekeningRequest(accountNumber);

        getCompositeDisposable().add(
                getApiSource().getData(urlDefault, defaultRekeningRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getView().onSuccessGetChangeDefault(response.getDesc());
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getDataDetailStatus(DetailKelolaKartuReq detailKelolaKartuReq) {
        if (urlDetail != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(urlDetail, detailKelolaKartuReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            DetailKelolaKartuRes detailResponse = response.getData(DetailKelolaKartuRes.class);
                            getView().onGetSuccessDetail(detailResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });
            getCompositeDisposable().add(disposable);
        }
    }

}