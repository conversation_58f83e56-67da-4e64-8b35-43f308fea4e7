package id.co.bri.brimons.presenters.pengelolaankartu;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.pengelolaankartu.IDetailLimitCardPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.pengelolaankartu.IDetailLimitCardView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.DetailLimitCardRequest;
import id.co.bri.brimons.models.apimodel.request.SubmitCustomLimitRequest;
import id.co.bri.brimons.models.apimodel.response.DetailLimitCardResponse;
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SubmitCustomLimitResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailLimitCardPresenter<V extends IMvpView & IDetailLimitCardView>
        extends MvpPresenter<V> implements IDetailLimitCardPresenter<V> {

    protected String url, submitLimitUrl;
    private DetailLimitCardRequest request;
    private SubmitCustomLimitRequest requestSubmitLimit;

    public DetailLimitCardPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                    BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                    TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlDetails(String url) {
        this.url = url;
    }

    @Override
    public void getDetailsLimit(String productId, String accountNumber) {
        if (url != null || isViewAttached()) {

            request = new DetailLimitCardRequest(productId, accountNumber);

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        DetailLimitCardResponse detailLimitCardResponse = response.getData(DetailLimitCardResponse.class);
                                        getView().onSuccessGetDetails(detailLimitCardResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        EmptyStateResponse maxResponse = response.getData(EmptyStateResponse.class);
                                        getView().onMaxLimitTrial(maxResponse);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().onErrorChange(restResponse);
                                    getView().hideProgress();
                                    onApiError(restResponse);
                                }
                            })
            );
        }
    }

    @Override
    public void setUrlSubmitLimit(String url) {
        this.submitLimitUrl = url;
    }

    @Override
    public void submitLimit(String pin, String productId, String limitCustom, String accountNumber) {
        if (submitLimitUrl != null || isViewAttached()) {

            requestSubmitLimit = new SubmitCustomLimitRequest(pin, productId, limitCustom, accountNumber);

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(submitLimitUrl, requestSubmitLimit, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equals("US")) {
                                        getView().onErrorChange(response);
                                    } else {
                                        SubmitCustomLimitResponse submitCustomLimitResponse = response.getData(SubmitCustomLimitResponse.class);
                                        getView().onSuccessSubmitLimit(submitCustomLimitResponse);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().onErrorChange(restResponse);
                                    onApiError(restResponse);
                                }
                            })
            );
        }
    }
}