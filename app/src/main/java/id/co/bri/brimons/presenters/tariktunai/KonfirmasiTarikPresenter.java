package id.co.bri.brimons.presenters.tariktunai;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.tariktunai.IKonfirmasiTarikPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.tariktunai.IKonfirmasiTarikView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PaymentRequest;
import id.co.bri.brimons.models.apimodel.response.KonfirmasiTarikTunaiResponse;
import id.co.bri.brimons.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiTarikPresenter<V extends IMvpView & IBaseFormView & IKonfirmasiTarikView> extends BaseFormPresenter<V> implements IKonfirmasiTarikPresenter<V> {

    private static final String TAG = "GeneralConfirmationPres";

    protected String urlPayment;

    public KonfirmasiTarikPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getDataPaymentTarik(String pin, String note, KonfirmasiTarikTunaiResponse generalConfirmationResponse) {
        if(urlPayment == null){
            Log.d(TAG, "getDataPayment: urlInformasi payment null");
            return;
        }

        if(!isViewAttached()){
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {
            //initiate param with getter from view

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            PaymentRequest paymentRequest = new PaymentRequest(generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString(), note);
            Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum )//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {


                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            PaymentTarikResponse paymentTarikResponse = response.getData(PaymentTarikResponse.class);
/*
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    "")
                            );
*/
                            getView().onSuccessGetTarikPayment(paymentTarikResponse);

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, int amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();

             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if(transaksi!= null){
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {

                        }

                        @Override
                        public void onError(Throwable e) {
                            Log.d(TAG, "onError: "+e.toString());
                        }
                    })
            );
        }
    }

    @Override
    public void setDisablePopup(boolean disableNotif) {
        getBRImoPrefRepository().disablePopupNotif(disableNotif);
    }

    @Override
    public void start() {
        super.start();
        setDisablePopup(true);
    }

    @Override
    public void stop() {
        super.stop();
        setDisablePopup(false);
    }
}