package id.co.bri.brimons.presenters.voucher

import id.co.bri.brimons.contract.IPresenter.voucher.IVoucherPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.voucher.IVoucherView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.voucher.VoucherGameResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class VoucherPresenter<V>(schedulerProvider: SchedulerProvider?, compositeDisposable: CompositeDisposable?,
                          mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?, categoryPfmSource: CategoryPfmSource?,
                          transaksiPfmSource: TransaksiPfmSource?, anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IVoucherPresenter<V> where V : IMvpView?, V : IVoucherView? {

    private lateinit var mUrl: String

    override fun setUrlVoucher(url: String) {
        mUrl = url
    }

    override fun getDataVoucher() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getDataTanpaRequest(mUrl, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val voucherResponse = response.getData(
                                VoucherGameResponse::class.java
                            )
                            getView()!!.onSuccessGetData(voucherResponse)

                            if(!GeneralHelper.isProd()) {
                                GeneralHelper.responseChuck(response)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                restResponse.desc
                            )
                        }
                    })
            )
        }

    }

}