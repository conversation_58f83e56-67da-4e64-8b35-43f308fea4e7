package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IInquiryJualEmasPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IInquiryJualEmasView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.emas.ConfirmationJualEmasRequest
import id.co.bri.brimons.models.apimodel.request.emas.GrafikEmasRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.emas.GrafikEmasResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class InquiryJualEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                  compositeDisposable: CompositeDisposable?,
                                  mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                  categoryPfmSource: CategoryPfmSource?,
                                  transaksiPfmSource: TransaksiPfmSource?,
                                  anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IInquiryJualEmasPresenter<V> where V : IMvpView?, V : IInquiryJualEmasView {

    private lateinit var urlData : String
    private lateinit var urlJual : String

    override fun setUrlInquiry(url: String) {
        this.urlData = url
    }

    override fun setUrlGrafikJual(url: String) {
        urlJual=url
    }

    override fun getDataConfirmationJual(request: ConfirmationJualEmasRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlData, request, seqNum) //function(param)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                val response = response.getData(
                                        GeneralConfirmationResponse::class.java
                                )

                                getView().onSuccessGetJualConfirmation(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                    getView().onSessionEnd(restResponse.desc)
                                }
                                else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)){
                                    getView().onException93(restResponse.desc)
                                }
                                else if(restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_01.value,
                                                ignoreCase = true
                                        )
                                ){
                                    getView().onException01(restResponse.desc)
                                }
                                else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getDataGrafikJual(request : GrafikEmasRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlJual, request, seqNum) //function(param)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                val response = response.getData(
                                        GrafikEmasResponse::class.java
                                )

                                getView().onSuccessGrafikEmas(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }


    private fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view!!.setDefaultSaldo(saldo, saldoString, defaultAcc,saldoHold)
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }
}