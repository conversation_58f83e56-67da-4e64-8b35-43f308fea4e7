package id.co.bri.brimons.presenters.saldodompetdigital;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.saldodompetdigital.IWebViewBindingPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.saldodompetdigital.IWebViewBindingView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.EwalletUpdateStatusRequest;
import id.co.bri.brimons.models.apimodel.response.EwalletUpdateStatusResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class WebViewBindingPresenter<V extends IMvpView & IWebViewBindingView> extends MvpPresenter<V> implements IWebViewBindingPresenter<V> {

    protected String updateStatusBindingUrl;
    protected Object updateStatusRequest;

    boolean isLoading = false;

    public WebViewBindingPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onSuccessGetCode(String type, String status, String callbackCode, String redirectCode, String authCode) {
        getView().showProgress();
        if (updateStatusBindingUrl == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        updateStatusRequest = new EwalletUpdateStatusRequest(type, status, callbackCode, redirectCode, authCode);

        getCompositeDisposable().add(
                getApiSource().getData(updateStatusBindingUrl, updateStatusRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                EwalletUpdateStatusResponse ewalletUpdateStatusResponse = response.getData(EwalletUpdateStatusResponse.class);
                                getView().onSuccessUpdateStatusBinding(ewalletUpdateStatusResponse);
                                getView().hideProgress();

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                }
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                    getView().onException12(restResponse.getDesc());
                                }
                                else {
                                    getView().onException(restResponse.getDesc());
                                }
                                getView().hideProgress();
                            }
                        })
        );


    }

    @Override
    public void start() {
        super.start();

    }


    @Override
    public void setUrlUpdateStatusBinding(String url) {
        updateStatusBindingUrl = url;
    }
}