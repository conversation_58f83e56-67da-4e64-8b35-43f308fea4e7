package id.co.bri.brimons.presenters.registrasirevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.registrasirevamp.IRegistrasiCheckPointPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.registrasirevamp.IRegistrasiCheckPointView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.RegisIdModel
import id.co.bri.brimons.models.apimodel.request.RegisIdRequest
import id.co.bri.brimons.models.apimodel.response.registrasi.RegisProgressResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiCheckPointPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiCheckPointPresenter<V> where V : IMvpView, V : IRegistrasiCheckPointView {

    private var urlReset: String? = null
    private var urlProgress: String? = null

    override fun regisId() {
        view.getRegisId(brImoPrefRepository.deviceId)
    }

    override fun setUrlReset(url: String) {
        urlReset = url
    }

    override fun setUrlProgress(url: String) {
        urlProgress = url
    }

    override fun sendDataReset(regisIdModel: RegisIdModel) {
        if (urlReset != null && isViewAttached) {

            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(urlReset, regisIdModel, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisProgressResponse =
                                response.getData(RegisProgressResponse::class.java)

                            getView().onSuccessReset(regisProgressResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun getProgressRegis(progressBack: Boolean) {
        if (urlProgress != null && isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            val regisIdRequest = RegisIdRequest(brImoPrefRepository.deviceId)

            compositeDisposable.add(
                apiSource.getData(urlProgress, regisIdRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisProgressResponse =
                                response.getData(RegisProgressResponse::class.java)

                            if (progressBack) {
                                getView().onSuccessCheckPointBack(regisProgressResponse)
                            } else {
                                getView().onSuccessCheckPoint(
                                    Gson().toJson(response.data),
                                    regisProgressResponse
                                )
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun start() {
        super.start()
        regisId()
    }
}