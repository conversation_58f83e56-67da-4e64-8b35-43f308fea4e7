package id.co.bri.brimons.presenters.pln;

import id.co.bri.brimons.contract.IPresenter.pln.IInquiryPlnTokenPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimons.contract.IView.pln.IInquiryPlnTokenView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.presenters.base.BaseInquiryPresenter;

import io.reactivex.disposables.CompositeDisposable;

public class InquiryplnTokenPresenter<V extends IMvpView & IBaseInquiryView & IInquiryPlnTokenView> extends BaseInquiryPresenter<V> implements IInquiryPlnTokenPresenter<V> {

    public InquiryplnTokenPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }
}
