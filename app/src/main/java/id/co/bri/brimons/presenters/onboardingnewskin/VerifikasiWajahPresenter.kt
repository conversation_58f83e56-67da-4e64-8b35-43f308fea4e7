package id.co.bri.brimons.presenters.onboardingnewskin

import android.content.Context
import id.co.bri.brimons.contract.IPresenter.onboardingnewskin.IVerifikasiWajahPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.informasirekening.IVerifikasiWajahView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.MinioSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.newonboardingrevamp.InitializeZolozRequest
import id.co.bri.brimons.models.apimodel.request.newonboardingrevamp.SendKycRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.newskinonboarding.CheckStatusKycRes
import id.co.bri.brimons.models.apimodel.response.newskinonboarding.InitializeZolozResponse
import id.co.bri.brimons.models.apimodel.response.newskinonboarding.PresignUrlLivenessResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class VerifikasiWajahPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    private val minioSource: MinioSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVerifikasiWajahPresenter<V> where V : IMvpView, V : IVerifikasiWajahView {

    private var urlPreSigned = ""
    private var urlInitializeZoloz = ""
    private var urlSendKyc = ""
    private var transactionId = ""

    override fun setUrlPreSigned(url: String) {
        urlPreSigned = url
    }

    override fun setUrlInitializeZoloz(url: String) {
        urlInitializeZoloz = url
    }

    override fun setUrlSendKyc(url: String) {
        urlSendKyc = url
    }

    override fun getPreSigned() {
        if (urlPreSigned.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = mBRImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlPreSigned, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        view.hideProgress()
                        getView().onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val preSignedResponse =
                            response.getData(PresignUrlLivenessResponse::class.java)
                        getView().onSuccessGetPreSigned(preSignedResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        getView().onException(restResponse.desc)
//                        when (restResponse.code) {
//                            Constant.RE_EXCEED_LIMIT -> {
//                                val limitResponse =
//                                    restResponse.getData(OnboardingErrorResponse::class.java)
//                                getView().onExceedLimit(limitResponse)
//                            }
//
//                            Constant.RE_STATUS_NOT_MATCH -> getView().onStatusNotMatch()
//                            else -> getView().onException(restResponse.desc)
//                        }
                    }
                })
        )
    }

    override fun putMinioData(
        transactionId: String,
        image1Url: String,
        image2Url: String,
        videoUrl: String,
        image1: String,
        image2: String,
        video: String,
        context: Context
    ) {
//        view.showProgress()
//        this.transactionId = transactionId
//
//        view.putDataMinioChain(
//            urlImg1 = image1Url,
//            urlImg2 = image2Url,
//            urlVideo = videoUrl,
//            img1 = image1,
//            img2 = image2,
//            video = video,
//            context = context,
//            compositeDisposable = compositeDisposable,
//            minioSource = minioSource,
//            schedulerProvider = schedulerProvider,
//            isWithProgress = false,
//            onMinioSuccess = {
//                getView().onSuccessUploadMinio()
//            },
//            onMinioCallError = { msg, code ->
//                if (code == 403) {
//                    getView().onException(msg)
//                } else {
//                    view.onException(msg)
//                }
//            },
//            onFailureHttp = { msg ->
//                view.onException(msg)
//            }
//        )
    }

    override fun getInitializeZoloz(metaInfo: String) {
        if (urlInitializeZoloz.isEmpty() || !isViewAttached) return

        val request = InitializeZolozRequest(metaInfo)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlInitializeZoloz, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val initializeZolozResponse =
                            response.getData(InitializeZolozResponse::class.java)
                        transactionId = initializeZolozResponse.transactionId.toString()
                        getView().onSuccessStartZoloz(initializeZolozResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        getView().onException(restResponse.desc)
                    }
                })
        )
    }

    override fun sendKyc(partnerId: String, result: String) {
        if (urlSendKyc.isEmpty() || !isViewAttached) return

        val request = SendKycRequest(
            partnerId,
            transactionId,
            result
        )
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlSendKyc, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkStatusKycResponse =
                            response.getData(CheckStatusKycRes::class.java)
                        getView().onSuccessSendKyc(checkStatusKycResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        getView().onException(restResponse.desc)
//                        if (restResponse.desc.contains(Constant.RE_ZOLOZ_FAILED))
//                            getView().onExceptionZoloz()
//                        else {
//                            when (restResponse.code) {
//                                Constant.RE_EXCEED_LIMIT -> {
//                                    val limitResponse =
//                                        restResponse.getData(OnboardingErrorResponse::class.java)
//                                    getView().onExceedLimit(limitResponse)
//                                }
//
//                                Constant.RE_STATUS_NOT_MATCH -> getView().onStatusNotMatch()
//                                else -> getView().onException(restResponse.desc)
//                            }
//                        }
                    }
                })
        )
    }

}