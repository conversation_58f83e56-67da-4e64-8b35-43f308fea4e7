package id.co.bri.brimons.presenters.asuransiRevamp

import id.co.bri.brimons.contract.IPresenter.asuransiRevamp.IListAsuransiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.asuransiRevamp.IListAsuransiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.asuransirevamp.DetailAsuransiRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.asuransi.DetailAsuransiResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ListAsuransiPresenter<V>(schedulerProvider: SchedulerProvider?,
                               compositeDisposable: CompositeDisposable?,
                               mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                               categoryPfmSource: CategoryPfmSource?,
                               transaksiPfmSource: TransaksiPfmSource?,
                               anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IListAsuransiPresenter<V> where V : IMvpView, V : IListAsuransiView  {

    lateinit var url : String

    override fun setUrlDetail(urlList: String) {
        url = urlList
    }

    override fun getDataDetail(request : DetailAsuransiRequest) {
        if (isViewAttached) {
//            show loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(url, request, seqNum).subscribeOn(
                    schedulerProvider.io()
            )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val detailResponse = response.getData(
                                    DetailAsuransiResponse::class.java
                            )
                            getView()!!.onSuccessDetail(detailResponse)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            }else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView()!!.onException12(restResponse.desc)
                            }
                            else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }
}