package id.co.bri.brimons.presenters.kartuKredit;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.kartuKredit.IFormKreditPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.kartuKredit.IFormKreditView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryKreditRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;


public class FormKreditPresenter<V extends IMvpView & IBaseFormView & IFormKreditView> extends BaseFormPresenter<V> implements IFormKreditPresenter<V> {
    private static final String TAG = "FormKreditPresenter";

    public FormKreditPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    /**
     * @param request
     */
    @Override
    public void getDataInquiry(InquiryKreditRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                if (inquiryUrl != null && konfirmasiUrl != null)
                                    getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl,false);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }


}