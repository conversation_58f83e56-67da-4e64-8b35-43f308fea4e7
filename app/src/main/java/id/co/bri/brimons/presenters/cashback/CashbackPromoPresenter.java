package id.co.bri.brimons.presenters.cashback;

import id.co.bri.brimons.contract.IPresenter.cashback.ICashbackPromoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.cashback.ICashbackPromoView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastMenuRequest;
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimons.models.apimodel.response.ListAllCashbackResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class CashbackPromoPresenter <V extends IMvpView & ICashbackPromoView> extends MvpPresenter<V> implements ICashbackPromoPresenter<V> {

    private String url;

    public CashbackPromoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void getCashbackAll(boolean fromNonDashboard) {
        if (url == null || !isViewAttached()) {
            return;
        }
        getView().isHideSkeleton(false);

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        String tokenKey = getBRImoPrefRepository().getTokenKey();
        String username = getBRImoPrefRepository().getUsername();

        if (!fromNonDashboard) {
            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(url, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().isHideSkeleton(true);
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ListAllCashbackResponse listAllCashbackResponse = response.getData(ListAllCashbackResponse.class);
                                        getView().onSuccessCashback(listAllCashbackResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        EmptyStateResponse stateResponse = response.getData(EmptyStateResponse.class);
                                        getView().showEmptyState(stateResponse);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                        getView().onException12(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        } else {
            FastMenuRequest request = new FastMenuRequest(username, tokenKey);
            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().isHideSkeleton(true);
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ListAllCashbackResponse listAllCashbackResponse = response.getData(ListAllCashbackResponse.class);
                                        getView().onSuccessCashback(listAllCashbackResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        EmptyStateResponse stateResponse = response.getData(EmptyStateResponse.class);
                                        getView().showEmptyState(stateResponse);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}