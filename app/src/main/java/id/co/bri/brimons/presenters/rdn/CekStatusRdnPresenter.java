package id.co.bri.brimons.presenters.rdn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.rdn.ICekStatusRdnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.rdn.ICekStatusRdnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.OnboardingRDNSBNRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnDataFormResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnFormSofResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnOnBoardingResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnOnCheckpointResponse;
import id.co.bri.brimons.models.apimodel.response.rdn.RdnProvinceResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class CekStatusRdnPresenter<V extends IMvpView & ICekStatusRdnView> extends MvpPresenter<V> implements ICekStatusRdnPresenter<V> {

    private String urlForm;
    private String urlInformation;
    private String urlReset;
    private String urlOnBoarding;
    private String urlProvince;

    public CekStatusRdnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlForm(String url) {
        this.urlForm = url;
    }

    @Override
    public void setUrlInformation(String url) {
        this.urlInformation = url;
    }

    @Override
    public void setUrlOnBoarding(String url) {
        this.urlOnBoarding = url;
    }

    @Override
    public void setUrlReset(String url) {
        this.urlReset = url;
    }

    @Override
    public void setProvince(String url) { this.urlProvince = url;}

    @Override
    public void onGetDataForm() {
        if (isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlForm,"",seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    RdnDataFormResponse rdnDataFormResponse = response.getData(RdnDataFormResponse.class);
                                    onGetProvince(rdnDataFormResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void onGetProvince(RdnDataFormResponse rdnDataFormResponse) {
        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlProvince,"",seq)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seq){

                            @Override
                            protected void onFailureHttp(String type) {
                                getView().hideProgress();
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                RdnProvinceResponse rdnProvinceResponse = response.getData(RdnProvinceResponse.class);
                                getView().onSuccessGetDataForm(rdnDataFormResponse, rdnProvinceResponse);
                                getView().hideProgress();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void saveFirstRdn(boolean firstRdn) {
        getBRImoPrefRepository().saveFirstRdn(firstRdn);
    }

    @Override
    public void saveCheckPointRdn(String checkPoint) {
        getBRImoPrefRepository().saveCheckPointRdn(checkPoint);
    }

    @Override
    public void onGetInformation() {
        if (isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInformation,"",seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    RdnFormSofResponse rdnFormSofResponse = response.getData(RdnFormSofResponse.class);
                                    getView().onSuccessGetInformation(rdnFormSofResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void onResetCheckpoint() {
        if (isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlReset,"",seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    onGetOnboarding();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void onGetOnboarding() {
        if (isViewAttached()){
            String seq = getBRImoPrefRepository().getSeqNumber();
            Boolean isSbnRDn = getBRImoPrefRepository().getFirstRdn();
            OnboardingRDNSBNRequest onboardingRDNSBNRequest = new OnboardingRDNSBNRequest(isSbnRDn);
            getCompositeDisposable()
                    .add(getApiSource().getData(urlOnBoarding,onboardingRDNSBNRequest,seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                        RdnOnBoardingResponse rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse.class);
                                        getView().onSuccessOnBoarding(rdnOnBoardingResponse);
                                        getView().hideProgress();
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                        RdnOnCheckpointResponse rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse.class);
                                        getView().onSuccessCheckPoint(rdnOnBoardingResponse);
                                        getView().hideProgress();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }
}