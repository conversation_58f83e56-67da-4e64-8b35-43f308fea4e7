package id.co.bri.brimons.presenters.dplkrevamp

import id.co.bri.brimons.contract.IPresenter.dplkrevamp.IPickProfileRiskPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dplkrevamp.IPickProfileRiskView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.ListProductDplkRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.ListPilihBrifineResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class PickProfileRiskPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
), IPickProfileRiskPresenter<V> where V : IMvpView?, V : IPickProfileRiskView {
    private var mUrlListProductDplk = ""
    override fun setUrulListProductDplk(url: String) {
        mUrlListProductDplk = url
    }

    override fun getListProductDplk(request: ListProductDplkRequest) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable
                .add(
                    apiSource.getData(mUrlListProductDplk, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {

                                getView().hideProgress()
                                val data = response.getData(ListPilihBrifineResponse::class.java)
                                getView().onSuccessGetListProductDplk(data)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                getView().onException(restResponse.desc)
                            }
                        })
                )
        }
    }
}