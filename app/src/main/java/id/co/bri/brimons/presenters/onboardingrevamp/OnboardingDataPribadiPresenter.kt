package id.co.bri.brimons.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingDataPribadiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingDataPribadiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingDataPribadiReq
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingDataPribadiPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingDataPribadiPresenter<V> where V : IMvpView, V : IOnboardingDataPribadiView {

    private lateinit var urlDataPribadi: String

    override fun setUrl(url: String) {
        urlDataPribadi = url
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

    override fun sendSelfData(onboardingDataPribadiReq: OnboardingDataPribadiReq) {
        if (urlDataPribadi.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlDataPribadi, onboardingDataPribadiReq, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessSelfData(Gson().toJson(response.data))
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

}