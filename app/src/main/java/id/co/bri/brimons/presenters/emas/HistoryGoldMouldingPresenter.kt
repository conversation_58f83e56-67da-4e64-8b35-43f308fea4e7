package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IHistoryGoldMouldingPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IRiwayatAmbilFisikEmasView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.emas.DetailAmbilEmasFisikRequest
import id.co.bri.brimons.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.emas.DetailAmbilEmasFisikResponse
import id.co.bri.brimons.models.apimodel.response.emas.RiwayatAmbilFisikEmasResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class HistoryGoldMouldingPresenter<V>(schedulerProvider: SchedulerProvider?,
                                      compositeDisposable: CompositeDisposable?,
                                      mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                      categoryPfmSource: CategoryPfmSource?,
                                      transaksiPfmSource: TransaksiPfmSource?,
                                      anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
        IHistoryGoldMouldingPresenter<V> where V : IMvpView?, V : IRiwayatAmbilFisikEmasView {

        var mUrlHistoryAmbilFisikEmas : String? = null
        var mUrlGetDetailAmbilFisikEmas : String? = null

        override fun setUrlGetRiwayatAmbilFisikEmas(urlAmbilFisik: String) {
                mUrlHistoryAmbilFisikEmas = urlAmbilFisik
        }

        override fun setUrlGetDetailAmbilFisikEmas(urlGetDetail: String) {
                mUrlGetDetailAmbilFisikEmas = urlGetDetail
        }

        override fun getRiwayatAmbilFisikEmas(request: RiwayatFilterRequest?) {
                if (mUrlHistoryAmbilFisikEmas == null || !isViewAttached) {
                        return
                }

                val seq = brImoPrefRepository.seqNumber

                compositeDisposable.add(
                        apiSource.getData(mUrlHistoryAmbilFisikEmas, request, seq) //function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seq) {
                                        override fun onFailureHttp(errorMessage: String) {
                                                getView().onExceptionGetRiwayatAmbilFisik()
                                                getView().onException(errorMessage)
                                        }

                                        override fun onApiCallSuccess(response: RestResponse) {
                                                val mutasiResponse = response.getData(RiwayatAmbilFisikEmasResponse::class.java)

                                                getView().onSuccessGetRiwayatAmbilFisik(mutasiResponse)
                                        }

                                        override fun onApiCallError(restResponse: RestResponse) {
                                                getView().onExceptionGetRiwayatAmbilFisik()
                                                onApiError(restResponse)
                                        }
                                })
                )
        }

        override fun getDetailAmbilFisikEmas(request: DetailAmbilEmasFisikRequest) {
                if (mUrlGetDetailAmbilFisikEmas == null || !isViewAttached) {
                        return
                }
                view.showProgress()
                val seq = brImoPrefRepository.seqNumber

                compositeDisposable.add(
                        apiSource.getData(mUrlGetDetailAmbilFisikEmas, request, seq) //function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seq) {
                                        override fun onFailureHttp(errorMessage: String) {
                                                getView().hideProgress()
                                                getView().onException(errorMessage)
                                        }

                                        override fun onApiCallSuccess(response: RestResponse) {
                                                getView().hideProgress()
                                                val mutasiResponse = response.getData(
                                                        DetailAmbilEmasFisikResponse::class.java)
                                                getView().onSuccessGetDetailAmbilFisikEmas(mutasiResponse)
                                        }

                                        override fun onApiCallError(restResponse: RestResponse) {
                                                getView().hideProgress()
                                                when(restResponse.code){
                                                        RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException(restResponse.desc)
                                                        else -> onApiError(restResponse)
                                                }
                                        }
                                })
                )        }


}