package id.co.bri.brimons.presenters;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimons.contract.IPresenter.ISetMenuDashboardIBPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.ISetMenuDashboardIBView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.menudashall.MenuDashAllSource;
import id.co.bri.brimons.data.repository.menudashfav.MenuDashFavSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.converter.MapperHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.ProductIbbizResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.DashboardMenu.MenuDashAll;
import id.co.bri.brimons.models.daomodel.DashboardMenu.MenuFeatureModel;
import id.co.bri.brimons.models.daomodel.DashboardMenu.SubFeatureModel;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.schedulers.Schedulers;

public class SetMenuDashboardIBPresenter<V extends IMvpView & ISetMenuDashboardIBView>
        extends MvpPresenter<V> implements ISetMenuDashboardIBPresenter<V> {

    protected MenuDashFavSource menuDashFavSource;
    protected MenuDashAllSource menuDashAllSource;
    protected String urlIbbiz;

    public SetMenuDashboardIBPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                       BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                       TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource,
                                       MenuDashFavSource menuDashFavSource, MenuDashAllSource menuDashAllSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.menuDashFavSource = menuDashFavSource;
        this.menuDashAllSource = menuDashAllSource;
    }

    @Override
    public void setUrlIbbiz(String urlIbbiz) {
        this.urlIbbiz = urlIbbiz;
    }

    @Override
    public void onGetDataMenuFav() {
        /*List<MenuDashFav> subFeatureModels = new ArrayList<>();

                    for (MenuDashFav menuDashFav : dashMenuFavs) {
                        if (!menuDashFav.getMenuName().equalsIgnoreCase("Lainnya")){
                            subFeatureModels.add(menuDashFav);
                        }
                    }*/
        getCompositeDisposable().add(menuDashFavSource.getDashMenuFav()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(getView()::onGetDataMenuFavSuccess, throwable -> getView().onException(throwable.getMessage())));
    }

    @Override
    public void onGetDataMenuAll() {
        getCompositeDisposable().add(menuDashAllSource.getMenuFeatureList()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(dashMenuAlls -> {
                    if (dashMenuAlls != null) {
                        getView().onGetDataMenuSuccess(dashMenuAlls);
                    } else {
                        saveMenuAllDefault();
                    }
                }, throwable -> getView().onException(throwable.getMessage())));
    }

    protected void saveMenuAllDefault() {
        //get master data menu All
        List<MenuDashAll> defaultMenuDashAlls = new ArrayList<>();
        //defaultMenuDashAlls.addAll(MenuConfig.fetchMenuDashAll());

        if (!defaultMenuDashAlls.isEmpty() && menuDashAllSource != null) {
            getCompositeDisposable().add(menuDashAllSource.insertMenuDashAll(defaultMenuDashAlls)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableCompletableObserver() {
                        @Override
                        public void onComplete() {
                            // do nothing
                            onGetDataMenuAll();
                        }

                        @Override
                        public void onError(@io.reactivex.annotations.NonNull Throwable e) {
                            // do nothing
                        }
                    }));
        }
    }

    @Override
    public void onGetDataSearch() {
        getCompositeDisposable().add(menuDashAllSource.getDashMenuAll()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(menuDashAlls -> {
                    if (menuDashAlls != null) {
                        getView().onGetDataSearchSuccess(menuDashAlls);
                    }
                }, throwable -> getView().onException(throwable.getMessage())));
    }

    @Override
    public void onDeleteMenuFavorite(List<SubFeatureModel> menuFavList) {
        getCompositeDisposable().add(menuDashFavSource.deleteDashMenuFav()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        onSaveMenuFavorite(menuFavList);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    @Override
    public void onSaveMenuFavorite(List<SubFeatureModel> menuFavList) {
        getCompositeDisposable().add(menuDashFavSource
                .insertMenuDashAll(MapperHelper.dashboardMenuDashFavModelConverter(menuFavList))
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        // do nothing
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    @Override
    public void onDeleteMenuAll(List<MenuFeatureModel> menuFeatureModelList) {
        getCompositeDisposable().add(menuDashAllSource.deleteDashMenuAll()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        onSaveMenuAll(menuFeatureModelList);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    @Override
    public void onSaveMenuAll(List<MenuFeatureModel> menuFeatureModelList) {

        getCompositeDisposable().add(menuDashAllSource
                .insertMenuDashAll(MapperHelper.dashboardMenuDashAllConverter(menuFeatureModelList))
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        // do nothing
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    @Override
    public void onGetDataIbbiz() {
        if (urlIbbiz != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlIbbiz, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    ProductIbbizResponse productResponse = response.getData(ProductIbbizResponse.class);
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        getView().onProductIbbiz(productResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                        getView().onSuccessIbbiz(productResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException12(restResponse.getDesc());
                                }
                            }));
        }
    }
}