package id.co.bri.brimons.presenters;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.IDialogPromoPresenter;
import id.co.bri.brimons.contract.IView.IDialogPromoView;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.NotifikasiModel;
import id.co.bri.brimons.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimons.models.apimodel.response.PromoResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DialogPromoPresenter<V extends IMvpView & IDialogPromoView>
        extends MvpPresenter<V> implements IDialogPromoPresenter<V> {

    private String url;
    private DetailPromoRequest detailPromoRequest;
    private boolean isLoading = false;

    public DialogPromoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getUrl(String url) {
        this.url = url;
    }

    @Override
    public void getDetailPromo(String promoId ,NotifikasiModel notifikasiModel) {
        if (isLoading) {
            return;
        } else {
            if (url != null || isViewAttached()) {
                getView().showProgress();
                isLoading = true;
                detailPromoRequest = new DetailPromoRequest(promoId);
                String seqNum = getBRImoPrefRepository().getSeqNumber();
                getCompositeDisposable()
                        .add(getApiSource().getData(url, detailPromoRequest, seqNum)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(),seqNum) {


                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                        isLoading = false;
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        //TO-DO onSuccess
                                        getView().hideProgress();
                                        PromoResponse promoResponse = response.getData(PromoResponse.class);
                                        getView().onSuccessGetDetailPromo(promoResponse,notifikasiModel);
                                        isLoading = false;
                                        Log.d("Chuck", restResponse.getDesc());
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoading = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                            getView().onSessionEnd(restResponse.getDesc());
                                        else
                                            getView().onException(restResponse.getDesc());

                                        Log.d("Chuck", restResponse.getDesc());

                                    }
                                }));

            }
        }
    }

    @Override
    public void getDetailPromoProduct(String promoId, String blastId,String additional) {
        if (isLoading) {
            return;
        } else {
            if (url != null || isViewAttached()) {
                getView().showProgress();
                isLoading = true;
                detailPromoRequest = new DetailPromoRequest(promoId);
                String seqNum = getBRImoPrefRepository().getSeqNumber();
                getCompositeDisposable()
                        .add(getApiSource().getData(url, detailPromoRequest, seqNum)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(),seqNum) {


                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                        isLoading = false;
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        //TO-DO onSuccess
                                        getView().hideProgress();
                                        PromoResponse promoResponse = response.getData(PromoResponse.class);
                                        getView().onSuccessGetDetailPromoProduct(promoResponse,promoId,blastId,additional);
                                        isLoading = false;
                                        Log.d("Chuck", restResponse.getDesc());
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoading = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                            getView().onSessionEnd(restResponse.getDesc());
                                        else
                                            getView().onException(restResponse.getDesc());

                                        Log.d("Chuck", restResponse.getDesc());

                                    }
                                }));

            }
        }
    }

}