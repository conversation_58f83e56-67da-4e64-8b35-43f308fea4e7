package id.co.bri.brimons.presenters.esbn

import id.co.bri.brimons.contract.IPresenter.esbn.IInquirySbnPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.esbn.IInquirySbnView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.esbn.GetConfirmationRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.esbn.earlyredeem.GetConfirmationResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class InquirySbnPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IInquirySbnPresenter<V> where V : IMvpView?, V : IInquirySbnView? {
    var urlInquiry: String? = null
    override fun start() {
        super.start()
    }

    override fun stop() {
        super.stop()
    }

    override fun setIUrlGetConfirmation(url: String?) {
        urlInquiry = url
    }

    override fun getCOnfirmationRedeem(request: GetConfirmationRequest) {
        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlInquiry, request, seqNum)
                .subscribeOn(schedulerProvider.single())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView()!!.onException(type)
                        view!!.hideProgress()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view!!.hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val dataResponse = response.getData(
                                GetConfirmationResponse::class.java
                            )
                            getView()!!.onSuccessGetConfirmationRedeem(dataResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }
}