package id.co.bri.brimons.presenters.cc_sof;

import id.co.bri.brimons.contract.IPresenter.cc_sof.IFormAktivasiCcSofPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.cc_sof.IFormAktivasiCcSofView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ActivateCcSofRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormAktivasiCcSofPresenter<V extends IMvpView & IFormAktivasiCcSofView>
        extends MvpPresenter<V> implements IFormAktivasiCcSofPresenter<V> {

    protected String urlAktivasi;

    public FormAktivasiCcSofPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String urlAktivasi) {
        this.urlAktivasi = urlAktivasi;
    }

    @Override
    public void getDataAktivasi(ActivateCcSofRequest activateCcSofRequest) {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlAktivasi, activateCcSofRequest, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralOtpResponse otpResponse = response.getData(GeneralOtpResponse.class);
                                    getView().onSuccessAktivasi(otpResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                        getView().onException93(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }
}