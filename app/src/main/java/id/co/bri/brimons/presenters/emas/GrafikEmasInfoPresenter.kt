package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IGrafikEmasInfoPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IGrafikEmasInfoView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimons.models.apimodel.response.onExceptionWH
import id.co.bri.brimons.models.apimodel.response.InfoResponse
import id.co.bri.brimons.models.apimodel.response.QuestionResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimons.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class GrafikEmasInfoPresenter<V>(schedulerProvider: SchedulerProvider?,
                                 compositeDisposable: CompositeDisposable?,
                                 mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                 categoryPfmSource: CategoryPfmSource?,
                                 transaksiPfmSource: TransaksiPfmSource?,
                                 anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IGrafikEmasInfoPresenter<V> where V : IMvpView?, V : IGrafikEmasInfoView? {

    private var mUrlBeli: String? = null
    private var mUrlJual: String? = null
    private var mUrlPusatBantuan: String? = null

    override fun setUrlGetFormBeli(urlBeli: String) {
        mUrlBeli = urlBeli
    }

    override fun setUrlFormJual(urlJual: String) {
        mUrlJual = urlJual
    }

    override fun setUrlPusatBantuan(url: String) {
        mUrlPusatBantuan = url
    }

    override fun getJualEmas() {
        if (mUrlJual == null || !isViewAttached) {

            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlJual, "", seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            val dataJualEmas = response.getData(
                                InquiryOpenEmasResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessFormJualEmas(
                                    dataJualEmas
                                )
                            } else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_02.value,
                                    ignoreCase = true
                                )
                            ) {
                                val onOnExceptionWH: onExceptionWH =
                                    restResponse.getData(
                                        onExceptionWH::class.java
                                    )
                                getView()!!.exceptionEODEOM(onOnExceptionWH)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SM.value,
                                    ignoreCase = true
                                )
                            ) {
                                val response = restResponse.getData(
                                    SafetyModeDrawerResponse::class.java
                                )
                                getView()!!.onSafetyMode(response)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_58.value, ignoreCase = true)) {
                                val response = restResponse.getData(
                                    InfoResponse::class.java
                                )
                                getView()!!.onException58(response)
                            } else getView()!!.onException(
                                restResponse.desc
                            )
                        }
                    })
            )
    }

    override fun getBeliEmas() {
        if (mUrlBeli == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlBeli, "", seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            val data = response.getData(
                                InquiryOpenEmasResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessFormBeliEmas(data)
                            } else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_02.value,
                                    ignoreCase = true
                                )
                            ) {
                                val onOnExceptionWH: onExceptionWH =
                                    restResponse.getData(
                                        onExceptionWH::class.java
                                    )
                                getView()!!.exceptionEODEOM(onOnExceptionWH)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SM.value,
                                    ignoreCase = true
                                )
                            ) {
                                val response = restResponse.getData(
                                    SafetyModeDrawerResponse::class.java
                                )
                                getView()!!.onSafetyMode(response)
                            } else getView()!!.onException(
                                restResponse.desc
                            )
                        }
                    })
            )
    }

    override fun getPusatBantuanSafety(code: String) {
        if (mUrlPusatBantuan == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(code)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlPusatBantuan, safetyPusatBantuanRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            val topicQuestionResponse = response.getData(
                                QuestionResponse::class.java
                            )
                            if (mUrlPusatBantuan != null) getView()!!.onSuccessGetPusatBantuan(
                                topicQuestionResponse
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                restResponse.desc
                            )
                        }
                    })
            )
    }
}