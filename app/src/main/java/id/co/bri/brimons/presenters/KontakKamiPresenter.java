package id.co.bri.brimons.presenters;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.IKontakKamiPresenter;
import id.co.bri.brimons.contract.IView.IKontakKamiView;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.voip.ListVoipFm;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.voip.CategoryVoipRes;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class KontakKamiPresenter<V extends IMvpView & IKontakKamiView>
        extends MvpPresenter<V> implements IKontakKamiPresenter<V> {

    public KontakKamiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                               BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                               TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    private String url;
    private String urlFmVoip;
    private String urlRevoke;

    @Override
    public void setUrlListVoip(String url) {
        this.url = url;
    }

    @Override
    public void getListVoip() {
        if (url == null && !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(url, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                CategoryVoipRes categoryVoipRes = response.getData(CategoryVoipRes.class);
                                getView().onSuccessGetListVoip(categoryVoipRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void setUrlFmListVoip(String url) {
        this.urlFmVoip = url;
    }

    @Override
    public void getFmListVoip() {
        if (urlFmVoip == null && !isViewAttached()) return;

        getView().showProgress();
        ListVoipFm request = new ListVoipFm(
                getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey());
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(urlFmVoip, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                CategoryVoipRes categoryVoipRes = response.getData(CategoryVoipRes.class);
                                getView().onSuccessGetFmListVoip(categoryVoipRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }
}