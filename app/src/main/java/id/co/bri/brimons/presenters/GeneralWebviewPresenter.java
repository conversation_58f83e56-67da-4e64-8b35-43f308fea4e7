package id.co.bri.brimons.presenters;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.IGeneralWebviewPresenter;
import id.co.bri.brimons.contract.IView.IGeneralWebviewView;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.RevokeSessionRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class GeneralWebviewPresenter<V extends IMvpView & IGeneralWebviewView>
        extends MvpPresenter<V> implements IGeneralWebviewPresenter<V> {

    protected String urlRevoke;

    public GeneralWebviewPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                   BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                   TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getInitiateResource() {
        getView().onInitiateResourceSuccess(getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey());
    }

    @Override
    public void setUrlRevoke(String urlRevoke) {
        this.urlRevoke = urlRevoke;
    }

    @Override
    public void revokeSession(RevokeSessionRequest request, boolean back) {
        if (urlRevoke != null || isViewAttached()) {

            getView().onGoingRevoke();
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlRevoke, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (back) {
                                        getView().hideProgress();
                                        getView().onSuccessRevoke();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
            );
        }
    }

    @Override
    public void start() {
        super.start();

        getInitiateResource();
    }
}