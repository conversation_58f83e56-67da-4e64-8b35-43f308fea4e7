package id.co.bri.brimons.presenters.qrmpm;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.qrmpm.IQrMPMScanPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.qrmpm.IQrMPMScanView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastQrTransferInquiryRequest;
import id.co.bri.brimons.models.apimodel.request.QrTransferInquiryRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.parking.InquiryParkingResponse;
import id.co.bri.brimons.models.apimodel.response.parking.InquiryQrCrossBorderResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class QrMPMScanPresenter<V extends IMvpView & IQrMPMScanView>
        extends MvpPresenter<V> implements IQrMPMScanPresenter<V> {

    protected String urlInquiry;
    protected String urlKonfirmasi;
    protected String urlPayment;

    protected Object request;

    public QrMPMScanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setInquiryUrl(String urlInquiry) {
        this.urlInquiry = urlInquiry;
    }

    @Override
    public void setKonfirmasiUrl(String urlKonfirmasi) {
        this.urlKonfirmasi = urlKonfirmasi;
    }

    @Override
    public void setPaymentUrl(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void geDataInquiry(String qrCode, boolean isFromFastMenu) {
        if (isViewAttached()) {
            getView().showProgress();

            if (isFromFastMenu) {
                request = new FastQrTransferInquiryRequest(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey(), qrCode);
            } else {
                request = new QrTransferInquiryRequest(qrCode);
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlInquiry, request, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    try {
                                        GeneralInquiryResponse generalResponse = response.getData(GeneralInquiryResponse.class);
                                        if (urlKonfirmasi != null && urlPayment != null && generalResponse.getTypeQr().equalsIgnoreCase(Constant.FLAG_TYPE_QR_MPM)) {
                                            getView().onSuccessGetInquiry(generalResponse, urlKonfirmasi, urlPayment, isFromFastMenu);
                                        }
                                    } catch (Exception e) {
                                        //do nothing
                                    }
                                    try {
                                        InquiryQrCrossBorderResponse inquiryQrCrossBorderResponse = response.getData(InquiryQrCrossBorderResponse.class);
                                        if (urlKonfirmasi != null && inquiryQrCrossBorderResponse.getTypeQr() != null && inquiryQrCrossBorderResponse.getTypeQr().equalsIgnoreCase(Constant.FLAG_TYPE_QR_CB)) {
                                            getView().onSuccessGetInquiryQrCrossBorder(inquiryQrCrossBorderResponse, isFromFastMenu);
                                        }
                                    } catch (Exception e) {
                                        //do nothing
                                    }
                                    try {
                                        InquiryParkingResponse inquiryParkingResponse = response.getData(InquiryParkingResponse.class);
                                        if (urlKonfirmasi != null && inquiryParkingResponse.getTypeQr() != null && inquiryParkingResponse.getTypeQr().equalsIgnoreCase(Constant.FLAG_TYPE_QR_PARKING)) {
                                            getView().onSuccessGetInquiryParking(inquiryParkingResponse, isFromFastMenu);
                                        }
                                    } catch (Exception e) {
                                        //do nothing
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}