package id.co.bri.brimons.presenters.asuransi;

import id.co.bri.brimons.contract.IPresenter.asuransi.IFormAsuransiPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.asuransi.IFormAsuransiView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastInquiryWalletRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryAsuransiRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;

import io.reactivex.disposables.CompositeDisposable;

public class FormAsuransiPresenter<V extends IMvpView & IBaseFormView & IFormAsuransiView> extends BaseFormPresenter<V> implements IFormAsuransiPresenter<V> {

    private static final String TAG = "FormAsuransuPresenter";


    public FormAsuransiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    /**
     * Method yang digunakan untuk get Data Inquiry di setiap FORM PRESENTER
     * @param request
     * @param isFromFastmenu
     */
    @Override
    public void getDataInquiry(InquiryAsuransiRequest request, boolean isFromFastmenu) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();

//        onLoad = true;
        if(isFromFastmenu){
            inquiryRequest = new FastInquiryWalletRequest(getFastMenuRequest(), request.getCardNumber(), request.getCreditCardCode());
        }else {
            inquiryRequest = request;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                try {
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                    if (inquiryUrl != null && konfirmasiUrl != null)
                                        getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl, isFromFastmenu);
                                }catch (Exception e){
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }
}