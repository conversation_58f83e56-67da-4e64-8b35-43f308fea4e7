package id.co.bri.brimons.presenters.rdnrevamp

import id.co.bri.brimons.contract.IPresenter.rdnrevamp.IHistoryWithdrawSuccessRdnPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.rdnrevamp.IHistoryWithdrawSuccessRdnView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.rdn.RdnHistoryListWithdrawRequest
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.rdnrevamp.RdnHistoryListWithdrawResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class HistoryWithdrawSuccessRdnPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IHistoryWithdrawSuccessRdnPresenter<V> where V : IMvpView, V : IHistoryWithdrawSuccessRdnView {
    private var urlGetHistory: String? = null
    private var urlGetHistoryDetail: String? = null

    override fun setUrlHistoryWithdraw(urlHistory: String) {
        urlGetHistory = urlHistory
    }

    override fun setUrlHistoryWithdrawDetail(urlHistoryDetail: String) {
        urlGetHistoryDetail = urlHistoryDetail
    }

    override fun getHistoryWithdraw(request: RdnHistoryListWithdrawRequest) {
        urlGetHistory.let { urlString ->
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val detailResponse = response.getData(
                                RdnHistoryListWithdrawResponse::class.java
                            )
                            getView().onSuccessGetHistoryWithdraw(detailResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onExceptionTimeoutGateway(restResponse.desc)
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun getHistoryWithdrawDetail(request: RdnHistoryListWithdrawRequest) {
        urlGetHistoryDetail.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(
                                ReceiptRevampResponse::class.java
                            )
                            getView().onSuccessGetHistoryWithdrawDetail(detailResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

}