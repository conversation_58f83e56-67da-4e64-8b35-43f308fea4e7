package id.co.bri.brimons.presenters.lifestyle

import id.co.bri.brimons.contract.IPresenter.lifestyle.IDashboardLifestylePresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.lifestyle.IDashboardLifestyleView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.lifestyle.MenuLifestyleSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.config.LifestyleConfig
import id.co.bri.brimons.domain.config.MenuConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimons.models.apimodel.response.CityFormResponse
import id.co.bri.brimons.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.UrlWebViewResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.DashboardLifestyleBadgeTrxResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.DashboardLifestyleInfoResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.DashboardLifestyleSellingResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.EODLifestyleResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.FeatureDataView
import id.co.bri.brimons.models.daomodel.lifestyle.MenuLifestyle
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableCompletableObserver
import io.reactivex.observers.DisposableMaybeObserver
import java.util.concurrent.TimeUnit

class DashboardLifestylePresenter <V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
    private val menuLifestyleSource: MenuLifestyleSource) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDashboardLifestylePresenter<V> where V : IMvpView?, V : IDashboardLifestyleView? {

    var mUrlDashboardInformation = ""
    var mUrlWebviewTugu = ""
    var mUrlDashboardSelling = ""
    var mUrlBadgeTrx = ""
    var mUrlFormBus = ""
    var mUrlKai = ""

    override fun setUrlDashboardInformation(urlInfo: String) {
        mUrlDashboardInformation = urlInfo
    }

    override fun setUrlDashboardSelling(urlSelling: String) {
        mUrlDashboardSelling = urlSelling
    }

    override fun setUrlBagdeTrx(urlBadgeTrx: String) {
        mUrlBadgeTrx = urlBadgeTrx
    }

    override fun setUrlWebviewTugu(urlTugu: String) {
        mUrlWebviewTugu = urlTugu
    }

    override fun setUrlBusShuttle(urlFormBus: String) {
        mUrlFormBus = urlFormBus
    }

    override fun setUrlKai(urlFormKai: String) {
        mUrlKai = urlFormKai
    }

    override fun getMenuLifestyleLocal() {
        if (isViewAttached) {
            compositeDisposable.add(menuLifestyleSource.getAllMenuLifestyle()
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableMaybeObserver<List<MenuLifestyle>>() {
                    override fun onSuccess(menuLifestyleList: List<MenuLifestyle>) {
                        if (menuLifestyleList.isNotEmpty()) {
                            view?.onSuccessGetMenuLocals(menuLifestyleList)
                        }
                    }

                    override fun onError(e: Throwable) {}
                    override fun onComplete() {}
                })
            )
        }
    }

    override fun getNewMenubyParentCode(featureCode: String) {
        if (isViewAttached) {
            compositeDisposable.add(menuLifestyleSource.getNewMenubyParentCode(featureCode)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableMaybeObserver<List<MenuLifestyle>>() {
                    override fun onSuccess(menuLifestyleList: List<MenuLifestyle>) {
                        if (menuLifestyleList.isNotEmpty()) {
                            if (menuLifestyleList.size >= 2) {
                                onUpdateFlagNewMenu(featureCode)
                            } else {
                                getMenuLifestyleLocal()
                            }
                        } else {
                            onUpdateFlagNewMenu(featureCode)
                        }
                    }

                    override fun onError(e: Throwable) {}
                    override fun onComplete() {}
                })
            )
        }
    }

    fun onUpdateFlagNewMenu(featureCode: String) {
        compositeDisposable.add(
            menuLifestyleSource.updateMenuLifestyle(featureCode, MenuConfig.NewStatus.OLD)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableCompletableObserver() {
                    override fun onComplete() {
                        getMenuLifestyleLocal()
                    }

                    override fun onError(e: Throwable) {
                        // do nothing
                    }
                })
        )
    }

    override fun getBadgeTrx() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataTanpaRequest(mUrlBadgeTrx, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(desc: String) {
                        getView()?.onExceptionIgnore(desc)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val dashboardLifestyleBadgeTrxResponse = response.getData(
                            DashboardLifestyleBadgeTrxResponse::class.java
                        )
                        getView()?.onSuccessGetBadgeTrx(dashboardLifestyleBadgeTrxResponse)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)) {
                            getView()?.onExceptionBadgeTrx(restResponse.desc)
                        } else {
                            //do nothing
                        }
                    }
                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDashboardInformation() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataTanpaRequest(mUrlDashboardInformation, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(desc: String) {
                        getView()?.onExceptionIgnore(desc)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val dashboardLifestyleInfoResponse = response.getData(
                            DashboardLifestyleInfoResponse::class.java
                        )
                        getView()?.onSuccessGetDashboardInfo(dashboardLifestyleInfoResponse)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)) {
                            getView()?.onExceptionBadgeTrx(restResponse.desc)
                        } else {
                            //do nothing
                        }
                    }

                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDashboardSelling() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataTanpaRequest(mUrlDashboardSelling, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(desc: String) {
                        getView()?.onExceptionIgnore(desc)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val dashboardLifestyleSellingResponse = response.getData(
                            DashboardLifestyleSellingResponse::class.java
                        )
                        getView()?.onSuccessGetDashboardSelling(dashboardLifestyleSellingResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)) {
                            getView()?.onExceptionBadgeTrx(restResponse.desc)
                        } else {
                            //do nothing
                        }
                    }

                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getWebViewTugu(partnerIdRequest: PartnerIdRequest?,
                                titleWebview: String,
                                codeMenu: String) {
        if (!isViewAttached) {
            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(mUrlWebviewTugu, partnerIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        if (response.code.equals(Constant.RE_SUCCESS, ignoreCase = true)) {
                            val webviewResponse = response.getData(
                                GeneralWebviewResponse::class.java
                            )
                            getView()?.onSuccessGetWebviewTugu(
                                webviewResponse,
                                titleWebview,
                                codeMenu
                            )
                        } else if (response.code.equals(Constant.RE02, ignoreCase = true)) {
                            val eodLifestyleResponse: EODLifestyleResponse =
                                response.getData(
                                    EODLifestyleResponse::class.java
                                )
                            getView()?.onMenuLifestyleEOD(eodLifestyleResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true)) {
                            getView()?.onException93(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun getFormBus() {
        if (!isViewAttached)
            return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataForm(mUrlFormBus, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val cityFormResponse = response.getData(CityFormResponse::class.java)
                        getView()?.onSuccessGetFormBus(cityFormResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(
                                "99",
                                ignoreCase = true
                            )
                        ) getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getFormKai(titleBar: String) {
        if (!isViewAttached)
            return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(mUrlKai, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val urlWebViewResponse = response.getData(
                            UrlWebViewResponse::class.java
                        )
                        getView()?.onSuccessGetFormKai(urlWebViewResponse, titleBar)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(
                                "99",
                                ignoreCase = true
                            )
                        ) getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun setFourMenu(
        list: List<FeatureDataView>,
        indexStart: Int,
        indexEnd: Int
    ): List<FeatureDataView> {
        val listMenu: MutableList<FeatureDataView> = java.util.ArrayList()
        listMenu.clear()

        if (!list.isNullOrEmpty()) {
            if (list.size >= indexEnd) {
                for (item in indexStart until indexEnd) {
                    listMenu.add(list[item])
                }
            } else {
                for (item in indexStart until list.size) {
                    listMenu.add(list[item])
                }
            }
        }

        return listMenu
    }

    override fun setFilteredMenu(listMenu: List<FeatureDataView>): List<FeatureDataView> {
        val listMenus = listMenu.filter {
            it.status != LifestyleConfig.StatusMenu.MENU_INACTIVE.statusMenu &&
                    it.status != LifestyleConfig.StatusMenu.MENU_HIDE.statusMenu
        }.toMutableList()

        return listMenus
    }

    override fun setFilteredMenuNew(listMenu: List<MenuLifestyle>): List<MenuLifestyle> {
        val listMenus = listMenu.filter {
            it.isNew
        }.toMutableList()

        return listMenus
    }

    override fun getIndihomeRegistrationData(selectedMenu: FeatureDataView) {
        if (brImoPrefRepository.isIndihomeFirstClick) {
            getWebViewTugu(
                partnerIdRequest = PartnerIdRequest(selectedMenu.partnerId),
                titleWebview = selectedMenu.featureName,
                codeMenu = selectedMenu.featureCode
            )
        } else {
            view?.showIndihomeConfirmation(selectedMenu)
        }
    }

    override fun confirmIndihomeRegistration(selectedMenu: FeatureDataView) {
        brImoPrefRepository.saveIndihomeFirstClick(true)
        getWebViewTugu(
            partnerIdRequest = PartnerIdRequest(selectedMenu.partnerId),
            titleWebview = selectedMenu.featureName,
            codeMenu = selectedMenu.featureCode
        )
    }

}