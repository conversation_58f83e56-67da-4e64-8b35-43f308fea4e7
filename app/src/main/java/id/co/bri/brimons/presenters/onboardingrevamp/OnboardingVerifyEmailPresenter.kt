package id.co.bri.brimons.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimons.contract.IPresenter.onboardingrevamp.IOnboardingVerifyEmailPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingrevamp.IOnboardingVerifyEmailView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingEmailRequest
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.OnboardingEmailRes
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingVerifyEmailPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingVerifyEmailPresenter<V> where V : IMvpView, V : IOnboardingVerifyEmailView {

    private var urlResend: String = ""
    private var urlCheckProgress: String = ""

    override fun setUrlResend(url: String) {
        urlResend = url
    }

    override fun setUrlCheckProgress(url: String) {
        urlCheckProgress = url
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

    override fun sendResend(resendRequest: OnboardingEmailRequest) {
        if (urlResend.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlResend, resendRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT_ONBOARDING.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val emailResponse =
                            response.getData(OnboardingEmailRes::class.java)
                        getView().onSuccessResend(emailResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value)){
                            getView().onExceptionStatusNotMatch()
                        } else {
                            getView().onExceptionRevamp(restResponse.desc)
                        }

                    }
                })
        )
    }

    override fun sendCheckProgress() {
        if (urlCheckProgress.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)

        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlCheckProgress, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val onboardingEmailRes =
                            response.getData(OnboardingEmailRes::class.java)
                        if (onboardingEmailRes.status == 4) {
                            getView().onSuccessResend(onboardingEmailRes)
                        } else
                            getView().onSuccessProgress(
                                Gson().toJson(response.data),
                                onboardingEmailRes.status
                            )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}