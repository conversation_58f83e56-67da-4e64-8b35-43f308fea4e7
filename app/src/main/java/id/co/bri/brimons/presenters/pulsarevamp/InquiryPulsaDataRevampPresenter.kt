package id.co.bri.brimons.presenters.pulsarevamp

import id.co.bri.brimons.contract.IPresenter.pulsarevamp.IInquiryPulsaRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.pulsarevamp.IInquiryPulsaRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.FastKonfirmasiRequest
import id.co.bri.brimons.models.apimodel.request.KonfirmasiPulsaRequest
import id.co.bri.brimons.models.apimodel.request.revamppulsa.ConfirmationPaketCustomRequest
import id.co.bri.brimons.models.apimodel.request.revamppulsa.FastConfirmationPaketCustomRequest
import id.co.bri.brimons.models.apimodel.request.revamppulsa.FastListPaketIndosatRequest
import id.co.bri.brimons.models.apimodel.request.revamppulsa.ListPaketIndosatRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.pulsarevamp.DataListCustomRevamp
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class InquiryPulsaDataRevampPresenter<V : IMvpView>(schedulerProvider: SchedulerProvider?,
                                                    compositeDisposable: CompositeDisposable?,
                                                    mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                                    categoryPfmSource: CategoryPfmSource?,
                                                    transaksiPfmSource: TransaksiPfmSource?,
                                                    anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IInquiryPulsaRevampPresenter<V> where V : IInquiryPulsaRevampView? {


    val TAG = "InquiryBrivaPresenter"
    private lateinit var url: String
    private lateinit var urlIndosat: String
    private lateinit var confirmationRequest: Any

    override fun getDataConfirmationPulsaRevamp(
        referenceNumber: String,
        providerId: String,
        phoneNumber: String,
        amount: String,
        saveAs: String,
        item: String,
        type: String,
        note: String,
        fromFast: Boolean
    ) {
        if (url == null || !isViewAttached) {
            return
        }

        if (view != null) {
            //initiate param with getter from view

            confirmationRequest = if (fromFast) {
                FastKonfirmasiRequest(
                    getFastMenuRequest(),
                    referenceNumber,
                    providerId,
                    phoneNumber,
                    amount,
                    saveAs,
                    item,
                    type,
                    note
                )
            } else {
                KonfirmasiPulsaRequest(
                    referenceNumber,
                    providerId,
                    phoneNumber,
                    amount,
                    saveAs,
                    item,
                    type,
                    note
                )
            }
            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(url, confirmationRequest, seqNum) //function(param)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val generalConfirmationResponse =
                                response.getData(GeneralConfirmationResponse::class.java)
                            getView()?.onSuccessGetConfirmation(generalConfirmationResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            when {
                                restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                    ignoreCase = true
                                ) -> getView()?.onException93(restResponse.desc)

                                else -> getView()?.onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun setUrlConfirmation(urlConfirmation: String) {
        this.url = urlConfirmation
    }

    override fun getCustomPaket(phoneNumber: String, operatorId: String, isFromFast: Boolean) {
        if (url == null || !isViewAttached) {
            return
        }

        if (view != null) {
            //initiate param with getter from view

            confirmationRequest = if (isFromFast) {
                FastListPaketIndosatRequest(getFastMenuRequest(), phoneNumber, operatorId)
            } else {
                ListPaketIndosatRequest(phoneNumber, operatorId)
            }

            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlIndosat, confirmationRequest, seqNum) //function(param)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                            getView()?.onFailedGetCustomPackage()
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val dataListCustomRevamp =
                                response.getData(DataListCustomRevamp::class.java)
                            getView()?.onSuccessGetPaketCustom(dataListCustomRevamp, phoneNumber)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView()?.onFailedGetCustomPackage()
                            if (!restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value))
                                getView()?.onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun setUrlIndosat(urlIndosat: String) {
        this.urlIndosat = urlIndosat
    }

    override fun getDataConfirmationPulsaRevampCustom(
        referenceNumber: String,
        providerId: String,
        phoneNumber: String,
        saveAs: String,
        item: String,
        type: String,
        note: String,
        fromFast: Boolean
    ) {
        if (url == null || !isViewAttached) {
            return
        }

        if (view != null) {
            //initiate param with getter from view
            confirmationRequest = if (fromFast) {
                FastConfirmationPaketCustomRequest(
                    getFastMenuRequest(),
                    referenceNumber,
                    phoneNumber,
                    providerId,
                    saveAs,
                    item,
                    type,
                    note
                )
            } else {
                ConfirmationPaketCustomRequest(
                    referenceNumber,
                    phoneNumber,
                    providerId,
                    saveAs,
                    item,
                    type,
                    note
                )
            }
            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(url, confirmationRequest, seqNum) //function(param)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val generalConfirmationResponse =
                                response.getData(GeneralConfirmationResponse::class.java)
                            getView()!!.onSuccessGetConfirmationCustom(generalConfirmationResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            getView().let { view ->
                                when {
                                    restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                        ignoreCase = true
                                    ) -> view?.onException93(restResponse.desc)

                                    else -> view?.onException(restResponse.desc)
                                }
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

}