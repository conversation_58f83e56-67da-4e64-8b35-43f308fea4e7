package id.co.bri.brimons.presenters.britamarencana;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.britamarencana.IDetailRencanaPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.britamarencana.IDetailRencanaView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.StatusKartuRequest;
import id.co.bri.brimons.models.apimodel.response.CertificateResponse;
import id.co.bri.brimons.models.apimodel.response.DetailRencanaResponse;
import id.co.bri.brimons.models.apimodel.response.FormPencairanResponse;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class DetailRencanaPresenter <V extends IMvpView & IDetailRencanaView> extends MvpPresenter<V>
        implements IDetailRencanaPresenter<V> {

    String url, urlPencairan, urlClose, urlCertif;

    public DetailRencanaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlPencairan(String url) {
        this.urlPencairan = url;
    }

    @Override
    public void setUrlClose(String url) {
        this.urlClose = url;
    }

    @Override
    public void setUrlCertif(String url) {
        this.urlCertif = url;
    }

    @Override
    public void getDetailRencana(String accountNumber) {
        if (url == null || !isViewAttached()) {
            return;
        }

        StatusKartuRequest statusKartuRequest = new StatusKartuRequest(accountNumber);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(url, statusKartuRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                    DetailRencanaResponse detailRencanaResponse = response.getData(DetailRencanaResponse.class);
                                    getView().onSuccessGetDetail(detailRencanaResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getFormPencairan(String accountNumber) {
        if (urlPencairan == null || !isViewAttached()) {
            return;
        }

        StatusKartuRequest statusKartuRequest = new StatusKartuRequest(accountNumber);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();

        getCompositeDisposable().add(
                getApiSource().getData(urlPencairan, statusKartuRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                FormPencairanResponse formPencairanResponse = response.getData(FormPencairanResponse.class);
                                getView().onSuccessFormPencairan(formPencairanResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getCertif(String accountNumber) {
        if (url == null || !isViewAttached()) {
            return;
        }
        getView().showProgress();
        StatusKartuRequest statusKartuRequest = new StatusKartuRequest(accountNumber);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlCertif, statusKartuRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                CertificateResponse certificateResponse = response.getData(CertificateResponse.class);
                                getView().onSuccessGetCertif(certificateResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getKonfirmasiTutup(String accountNumber) {
        if (urlClose == null || !isViewAttached()) {
            return;
        }

        StatusKartuRequest statusKartuRequest = new StatusKartuRequest(accountNumber);
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlClose, statusKartuRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                                getView().onSuccessKonfirmasiClose(generalConfirmationResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}