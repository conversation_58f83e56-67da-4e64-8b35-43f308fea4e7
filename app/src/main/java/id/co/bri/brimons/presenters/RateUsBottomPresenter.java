package id.co.bri.brimons.presenters;


import androidx.annotation.NonNull;
import id.co.bri.brimons.contract.IPresenter.IRateUsBottomPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.IRateUsBottomView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.rate.RateSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.converter.RoomDateConverter;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.RateUsSendRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.RateUs;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class RateUsBottomPresenter<V extends IMvpView & IRateUsBottomView> extends MvpPresenter<V> implements IRateUsBottomPresenter<V> {

    protected String urlRateUs;
    protected RateSource rateSource;

    public RateUsBottomPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, RateSource rateSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.rateSource = rateSource;
    }


    @Override
    public void setUrlSendRate(String urlRateUs) {
        this.urlRateUs = urlRateUs;
    }

    @Override
    public void onSendDataRate(String rating, String message, String tag, String date, long counter, long id) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            long rate;

            if (rating.isEmpty())
                rate = 0;
            else
                rate = Long.parseLong(rating);

            if (message.isEmpty())
                message = "";

            if (tag.isEmpty())
                tag = "";


            RateUsSendRequest request = new RateUsSendRequest(rating, message, tag);

            getView().showProgress();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlRateUs, request, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().getDataSuccess();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                }

                                @Override
                                public void onComplete() {
                                    super.onComplete();
                                    onUpdateRateDb(rate, date, counter, id);
                                }
                            }));
        }
    }

    @Override
    public void onSaveRateDb(RateUs rateUs) {
        if (isViewAttached()) {
            getCompositeDisposable().add(rateSource.saveRateUs(rateUs)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribe(rate -> {

                    }, throwable -> getView().onException(throwable.getMessage())));
        }
    }

    @Override
    public void onUpdateRateDb(long rating, String date, long counter, long id) {
        if (isViewAttached()) {
            RateUs rateUs = new RateUs(id, rating, RoomDateConverter.stringToDate(date), counter);

            getCompositeDisposable().add(rateSource.saveRateUs(rateUs)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(@NonNull Long aLong) {
                            // Do nothing
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                            // Do nothing
                        }
                    }));
        }
    }

}