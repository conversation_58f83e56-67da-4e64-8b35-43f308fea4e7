package id.co.bri.brimons.presenters.bundle;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import id.co.bri.brimons.BuildConfig;
import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.bundle.IBundlePresenter;
import id.co.bri.brimons.contract.IView.bundle.IBundleView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.BundleListResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import id.co.bri.brimons.security.MyCryptStatic;
import io.reactivex.disposables.CompositeDisposable;

public class BundlePresenter<V extends IBundleView> extends MvpPresenter<V>
        implements IBundlePresenter<V> {

    private final Gson gson = new Gson();

    @Inject
    public BundlePresenter(SchedulerProvider schedulerProvider,
                          CompositeDisposable compositeDisposable,
                          BRImoPrefSource brImoPrefSource,
                          ApiSource apiSource,
                          TransaksiPfmSource transaksiPfmSource) {
        super(schedulerProvider, compositeDisposable, brImoPrefSource, apiSource, transaksiPfmSource);
    }

    /**
     * Check for new version for specific bundle
     * @param currentVersion current version
     * @param bundleName specific bundle name
     */
    @Override
    public void checkForNewVersion(String currentVersion, String bundleName) {
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Starting version check for bundle: " + bundleName);
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Current version: " + currentVersion);
        
        if (getView() != null) {
            getView().onBundleCheckLoading();
        }

        // Generate sequence number for API call
        String sequenceNumber = getBRImoPrefRepository().getSeqNumber();
        String bundleUrl = GeneralHelper.getString(R.string.url_v5_bundle_list);
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: API URL: " + bundleUrl);
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Sequence number: " + sequenceNumber);
        
        JsonObject jsonObject = new JsonObject();
        String variantId;
        try {
            variantId = MyCryptStatic.decryptAsBase64(BuildConfig.M_VARIANT_ID);
        } catch (Exception e) {
            variantId = "";
        }
        jsonObject.addProperty("variant_id", variantId);

        // Call API using getDataTanpaRequest pattern
        getCompositeDisposable().add(
                getApiSource().getData(bundleUrl, jsonObject, sequenceNumber)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), sequenceNumber) {
                            @Override
                            protected void onFailureHttp(String type) {
                                if (getView() != null) {
                                    getView().onBundleCheckError("HTTP Error: " + type);
                                    getView().onBundleCheckCompleted();
                                }
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: API call successful for bundle: " + bundleName);
                                Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Response code: " + response.getCode());

                                if (getView() != null) {
                                    try {
                                        // Response langsung ke BundleListResponse, bukan ke field data
                                        BundleListResponse bundleListResponse = gson.fromJson(
                                            gson.toJson(response), 
                                            BundleListResponse.class
                                        );

                                        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Parsed bundle list response");
                                        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Available bundles count: " + 
                                            (bundleListResponse != null && bundleListResponse.getData() != null ? bundleListResponse.getData().size() : 0));

                                        if (bundleListResponse != null && bundleListResponse.getData() != null && !bundleListResponse.getData().isEmpty()) {
                                            // Find bundle by name if specified, otherwise use first bundle
                                            BundleListResponse.BundleData bundleData = null;

                                            // Search for specific bundle by name
                                            for (BundleListResponse.BundleData data : bundleListResponse.getData()) {
                                                Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Checking bundle: " + data.getName());
                                                if (bundleName.equals(data.getName())) {
                                                    bundleData = data;
                                                    Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Found matching bundle: " + data.getName());
                                                    break;
                                                }
                                            }

                                            if (bundleData != null) {
                                                Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Bundle found, comparing versions");
                                                Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Current version: " + currentVersion);
                                                Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Remote version: " + bundleData.getManifest().getVersion());
                                                
                                                // Compare versions
                                                if (isNewVersionAvailable(currentVersion, bundleData.getManifest().getVersion())) {
                                                   Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: New version available, triggering download");
                                                   getView().onBundleDataReceived(bundleData);
                                                } else {
                                                    Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: No new version available");
                                                    getView().onNoNewVersionAvailable();
                                                }
                                            } else {
                                                // Bundle not found
                                                Log.e("BundlePresenter", "[BUNDLE] BundlePresenter: Bundle '" + bundleName + "' not found in response");
                                                getView().onBundleCheckError("Bundle '" + bundleName + "' not found");
                                            }
                                        } else {
                                            Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: No bundles available in response");
                                            getView().onNoNewVersionAvailable();
                                        }
                                    } catch (Exception e) {
                                        Log.e("BundlePresenter", "[BUNDLE] BundlePresenter: Error parsing response", e);
                                        getView().onBundleCheckError("Error parsing response: " + e.getMessage());
                                    }
                                    getView().onBundleCheckCompleted();
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (getView() != null) {
                                    getView().onBundleCheckError(restResponse.getDesc());
                                    getView().onBundleCheckCompleted();
                                }
                            }
                        })
        );
    }

    @Override
    public void getPublicKey(String keyId, BundleListResponse.BundleData bundleData) {
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Getting public key for bundle: " + bundleData.getName());
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Key ID: " + keyId);
        
        // Generate sequence number for API call
        String sequenceNumber = getBRImoPrefRepository().getSeqNumber();
        String bundleUrl = GeneralHelper.getString(R.string.url_v5_get_public_key);
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Public key API URL: " + bundleUrl);
        Log.i("BundlePresenter", "[BUNDLE] BundlePresenter: Sequence number: " + sequenceNumber);
        
        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("key_id", keyId);

        // Call API using getDataTanpaRequest pattern
        getCompositeDisposable().add(
                getApiSource().getData(bundleUrl, jsonObject, sequenceNumber)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), sequenceNumber) {
                            @Override
                            protected void onFailureHttp(String type) {
                                if (getView() != null) {
                                    getView().onBundleCheckError("HTTP Error: " + type);
                                    getView().onBundleCheckCompleted();
                                }
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (getView() != null) {
                                    try {
                                        JsonObject root = gson.toJsonTree(response).getAsJsonObject();
                                        JsonObject dataObj = root.getAsJsonObject("data");
                                        String pem = null;
                                        if (dataObj != null && dataObj.has("pem") && !dataObj.get("pem").isJsonNull()) {
                                            pem = dataObj.get("pem").getAsString();
                                        }
                                        if (pem == null || pem.isEmpty()) {
                                            getView().onBundleCheckError("Public key PEM not found");
                                        } else {
                                            getView().onPublicKeyReceived(pem, bundleData);
                                        }
                                    } catch (Exception e) {
                                        getView().onBundleCheckError("Error parsing response: " + e.getMessage());
                                    }
                                    getView().onBundleCheckCompleted();
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (getView() != null) {
                                    getView().onBundleCheckError(restResponse.getDesc());
                                    getView().onBundleCheckCompleted();
                                }
                            }
                        })
        );
    }

    /**
     * Compare version strings to determine if new version is available
     *
     * @param currentVersion current version
     * @param latestVersion  latest version from server
     * @return true if new version is available
     */
    private boolean isNewVersionAvailable(String currentVersion, String latestVersion) {

        // Kalau currentVersion kosong atau null → langsung anggap perlu update (download)
        if (currentVersion == null || currentVersion.isEmpty()) {
            return true;
        }

        try {
            String[] current = currentVersion.split("\\.");
            String[] latest  = latestVersion.split("\\.");

            int maxLength = Math.max(current.length, latest.length);

            for (int i = 0; i < maxLength; i++) {
                int currentPart = (i < current.length) ? Integer.parseInt(current[i]) : 0;
                int latestPart  = (i < latest.length)  ? Integer.parseInt(latest[i])  : 0;

                if (latestPart > currentPart) {
                    // Latest version lebih besar → update
                    return true;
                } else if (latestPart < currentPart) {
                    // Current version lebih besar → tidak perlu update
                    return false;
                }
            }

            // Sampai di sini berarti semua part sama → versi sama → tidak perlu update
            return false;

        } catch (Exception e) {
            // Jika parsing gagal → anggap tidak perlu update untuk menghindari crash
            return false;
        }
    }
} 