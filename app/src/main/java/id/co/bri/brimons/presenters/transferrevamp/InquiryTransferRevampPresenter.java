package id.co.bri.brimons.presenters.transferrevamp;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.transfer.IInquiryTransferRevampPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transfer.IInquiryTransferRevampView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.AmountHelper;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.ConfirmationTransferAftRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.ConfirmationTransferEditAftRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.ConfirmationTransferRevampRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.ConfirmationTransferRtgsRevampRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.FastConfirmationTransferRevampRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.FastConfirmationTransferRtgsRevampRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.FastListCityTransferRequest;
import id.co.bri.brimons.models.apimodel.request.revamptransfer.ListCityTransferRequest;
import id.co.bri.brimons.models.apimodel.request.smarttransfer.RecommendAccountSmartTransferRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryRtgsCityResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryTransferRevampResponse;
import id.co.bri.brimons.models.apimodel.response.NonIndividuRtgsResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.transferrevamp.AftConfirmationResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * Created by FNS
 */

public class InquiryTransferRevampPresenter<V extends IMvpView & IInquiryTransferRevampView> extends MvpPresenter<V> implements IInquiryTransferRevampPresenter<V> {
    protected String mUrlConfirmation = null;
    protected String mUrlListCity = null;
    protected Object confirmationRequest;
    protected Object listCityTransferRequest;
    protected Disposable disposableConfirmation;
    protected String mUrlConfirmationAft = null;
    protected String mUrlConfirmationEditAft = null;

    public InquiryTransferRevampPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataKonfirmasi(String refNumb, String account, String ammount, String favName, boolean isFromFastMenu, String transferMethod, String note) {
        if (mUrlConfirmation == null || !isViewAttached()) {
            return;
        }

        if (getView() != null) {
            //initiate param with getter from view
            if (isFromFastMenu) {
                confirmationRequest = new FastConfirmationTransferRevampRequest(getFastMenuRequest(), refNumb, account, ammount, favName, transferMethod, note);
            } else {
                confirmationRequest = new ConfirmationTransferRevampRequest(refNumb, account, ammount, favName, transferMethod, note);
            }

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            if (isFromFastMenu) {
                disposableConfirmation = generateConfirmationDisposableC2(confirmationRequest, seqNum, refNumb, account, ammount, favName, isFromFastMenu, transferMethod, note);
            } else {
                disposableConfirmation = generateConfirmationDisposable(confirmationRequest, seqNum);
            }

            getCompositeDisposable().add(disposableConfirmation);
        }
    }


    public Disposable generateConfirmationDisposableC2(Object confirmationRequest,
                                                       String seqNum,
                                                       String refNumb,
                                                       String account,
                                                       String ammount,
                                                       String favName,
                                                       boolean isFromFastMenu,
                                                       String transferMethod,
                                                       String note) {
        return getApiSource().getDataC2(mUrlConfirmation, confirmationRequest, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {

                        if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.getValue())) {
                            //update flag DKL C2
                            getBRImoPrefRepository().saveDklC2(false);
                            //retry hit form
                            getDataKonfirmasi(refNumb, account, ammount, favName, isFromFastMenu, transferMethod, note);
                        } else if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.getValue())) {
                            //update flag DKL C2
                            getBRImoPrefRepository().saveDklC2(true);
                            //retry hit form
                            getDataKonfirmasi(refNumb, account, ammount, favName, isFromFastMenu, transferMethod, note);
                        } else {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                        getView().onGetDataKonfirmasi(generalConfirmationResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                            getView().onException93(restResponse.getDesc());
                        } else {
                            getView().onException(restResponse.getDesc());
                        }
                        getView().hideProgress();
                    }
                });
    }

    public Disposable generateConfirmationDisposable(Object confirmationRequest, String seqNum) {
        return getApiSource().getData(mUrlConfirmation, confirmationRequest, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                        getView().onGetDataKonfirmasi(generalConfirmationResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase("93")) {
                            getView().onException93(restResponse.getDesc());
                        } else {
                            getView().onException(restResponse.getDesc());
                        }
                    }
                });
    }

    @Override
    public void getDataKonfirmasiRtgs(ConfirmationTransferRtgsRevampRequest
                                              confirmationTransferRtgsRevampRequest, boolean isFromFastMenu) {
        if (mUrlConfirmation == null || !isViewAttached()) {
            return;
        }

        if (getView() != null) {
            //initiate param with getter from view
            if (isFromFastMenu) {
                confirmationRequest = new FastConfirmationTransferRtgsRevampRequest(getFastMenuRequest(),
                        confirmationTransferRtgsRevampRequest.getBenefCityCode(),
                        confirmationTransferRtgsRevampRequest.getBenefCityName(),
                        confirmationTransferRtgsRevampRequest.getBenefProvinceCode(),
                        confirmationTransferRtgsRevampRequest.getBenefAddress(),
                        confirmationTransferRtgsRevampRequest.getBenefNotes(),
                        confirmationTransferRtgsRevampRequest.getBenefCitizenship(),
                        confirmationTransferRtgsRevampRequest.getDestinationName(),
                        confirmationTransferRtgsRevampRequest.getReferenceNumber(),
                        confirmationTransferRtgsRevampRequest.getAccountNumber(),
                        confirmationTransferRtgsRevampRequest.getAmount(),
                        confirmationTransferRtgsRevampRequest.getSaveAs(),
                        confirmationTransferRtgsRevampRequest.getTransferMethod(),
                        confirmationTransferRtgsRevampRequest.getNote());
            } else {
                confirmationRequest = confirmationTransferRtgsRevampRequest;
            }

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = generateDisposableRtgsConfirmation(confirmationRequest, seqNum, confirmationTransferRtgsRevampRequest, isFromFastMenu);
            getCompositeDisposable().add(disposable);
        }

    }

    private Disposable generateDisposableRtgsConfirmation(Object confirmationReq,
                                                          String seqNumber,
                                                          ConfirmationTransferRtgsRevampRequest confirmationTransferRtgsRevampRequest,
                                                          boolean isFromFastMenu) {
        if(isFromFastMenu){
            return getApiSource().getDataC2(mUrlConfirmation, confirmationReq, seqNumber)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNumber) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            if(errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.getValue())) {
                                //update flag DKL C2
                                getBRImoPrefRepository().saveDklC2(false);
                                //retry hit form
                                getDataKonfirmasiRtgs(confirmationTransferRtgsRevampRequest, isFromFastMenu);
                            } else if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.getValue())) {
                                //update flag DKL C2
                                getBRImoPrefRepository().saveDklC2(true);
                                //retry hit form
                                getDataKonfirmasiRtgs(confirmationTransferRtgsRevampRequest, isFromFastMenu);
                            } else {
                                getView().onException(errorMessage);
                            }
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                            getView().onGetDataKonfirmasi(generalConfirmationResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("05")) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase("93")) {
                                getView().onException93(restResponse.getDesc());
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    });
        } else {
            return getApiSource().getData(mUrlConfirmation, confirmationReq, seqNumber)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNumber) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                            getView().onGetDataKonfirmasi(generalConfirmationResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                getView().onSessionEnd(restResponse.getDesc());
                            }else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException93(restResponse.getDesc());
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    });
        }
    }

    @Override
    public void getListCity(String code, boolean isFromFastMenu) {
        if (mUrlListCity == null || !isViewAttached()) {
            return;
        }


        if (getView() != null) {
            //initiate param with getter from view

            if (isFromFastMenu) {
                listCityTransferRequest = new FastListCityTransferRequest(getFastMenuRequest(), code);
            } else {
                listCityTransferRequest = new ListCityTransferRequest(code);
            }

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = generateDisposableListCity(listCityTransferRequest, seqNum, isFromFastMenu, code);
            getCompositeDisposable().add(disposable);
        }

    }

    private Disposable generateDisposableListCity(Object listCityTransferRequest, String seqNum, boolean isFromFastMenu, String cityCode) {
        if(isFromFastMenu){
            return getApiSource().getDataC2(mUrlListCity, listCityTransferRequest, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            if(errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.getValue())) {
                                //update flag DKL C2
                                getBRImoPrefRepository().saveDklC2(false);
                                //retry hit form
                                getListCity(cityCode, isFromFastMenu);
                            } else if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.getValue())) {
                                //update flag DKL C2
                                getBRImoPrefRepository().saveDklC2(true);
                                //retry hit form
                                getListCity(cityCode, isFromFastMenu);
                            } else {
                                getView().onException(errorMessage);
                                getView().onFailedGetListCity();
                            }
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            InquiryRtgsCityResponse inquiryRtgsCityResponse = response.getData(InquiryRtgsCityResponse.class);
                            getView().onSuccessGetListCity(inquiryRtgsCityResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                           if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                getView().onException93(restResponse.getDesc());
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    });
        } else {

            return getApiSource().getData(mUrlListCity, listCityTransferRequest, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                            getView().onFailedGetListCity();
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                InquiryRtgsCityResponse inquiryRtgsCityResponse = response.getData(InquiryRtgsCityResponse.class);
                                getView().onSuccessGetListCity(inquiryRtgsCityResponse);
                            }else if(response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_03.getValue())){
                                NonIndividuRtgsResponse messageResponse = response.getData(NonIndividuRtgsResponse.class);
                                getView().onException03(messageResponse);
                                getView().onFailedGetListCity();
                            } else {
                                getView().onFailedGetListCity();
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onFailedGetListCity();
                            if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });
        }
    }

    @Override
    public void getDataKonfirmasiAft(String refNumb, String account, String amount, String transferMethod, String note, String frequency, String aftDateStart, String aftDateEnd, String saveAs) {
        if (mUrlConfirmationAft == null || !isViewAttached()) {
            return;
        }

        if (getView() != null) {
            //initiate param with getter from view
            confirmationRequest = new ConfirmationTransferAftRequest(refNumb, account, amount, transferMethod, note, frequency, aftDateStart, aftDateEnd, saveAs);


            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(mUrlConfirmationAft, confirmationRequest, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            AftConfirmationResponse aftConfirmationResponse = response.getData(AftConfirmationResponse.class);
                            getView().onSuccessGetDataKonfirmasiAft(aftConfirmationResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                getView().onSessionEnd(restResponse.getDesc());
                            }else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException93(restResponse.getDesc());
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }


    @Override
    public void setUrlKonfirmasi(String urlKonfirmasi) {
        mUrlConfirmation = urlKonfirmasi;
    }



    @Override
    public void setUrlListCity(String urlListCity) {
        mUrlListCity = urlListCity;
    }

    @Override
    public String getSaldoRekeningUtama() {
        return getBRImoPrefRepository().getSaldoRekeningUtama();
    }

    @Override
    public void setUrlKonfirmasiAft(String urlKonfirmasiAft) {
        mUrlConfirmationAft = urlKonfirmasiAft;
    }

    @Override
    public void getEditKonfirmasiAft(String refNumb, String account, String amount, String transferMethod, String note, String frequency, String aftDateStart, String aftDateEnd, String idAft) {
        if (mUrlConfirmationEditAft == null || !isViewAttached()) {
            return;
        }

        if (getView() != null) {
            //initiate param with getter from view
            confirmationRequest = new ConfirmationTransferEditAftRequest(refNumb, account, amount, transferMethod, note, frequency, aftDateStart, aftDateEnd, null, idAft);


            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(mUrlConfirmationEditAft, confirmationRequest, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            AftConfirmationResponse aftConfirmationResponse = response.getData(AftConfirmationResponse.class);
                            getView().onSuccessGetDataKonfirmasiAft(aftConfirmationResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                getView().onSessionEnd(restResponse.getDesc());
                            }else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException93(restResponse.getDesc());
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void setUrlKonfirmasiEditAft(String urlKonfirmasiEdit) {
        mUrlConfirmationEditAft = urlKonfirmasiEdit;
    }

    @Override
    public boolean getPrefIsFirstTimeAft() {
        return getBRImoPrefRepository().getIsFirstTimeShowAft();
    }

    @Override
    public void setPrefIsFirstTimeAft(boolean isFirstTime) {
        getBRImoPrefRepository().setIsFirstTimeShowAft(isFirstTime);
    }

    @Override
    public String getAccountDefault() {
        return getBRImoPrefRepository().getAccountDefault();
    }

    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        saldo = AmountHelper.parsingSaldoString(saldoText);
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();

        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold);
    }


}