package id.co.bri.brimons.presenters.fastmenunewskin;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.fastmenunewskin.IFastMenuNewSkinPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.fastmenunewskin.IFastMenuNewSkinView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.fastmenu.FastMenuSource;
import id.co.bri.brimons.data.repository.fastmenudefault.FastMenuDefaultSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.config.MenuConfig;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ChangeDeviceRequest;
import id.co.bri.brimons.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimons.models.apimodel.request.FastValidateBrizziRequest;
import id.co.bri.brimons.models.apimodel.request.ListValidateTersimpan;
import id.co.bri.brimons.models.apimodel.request.NotifReadFastMenuRequest;
import id.co.bri.brimons.models.apimodel.request.SafetyModeFMRequest;
import id.co.bri.brimons.models.apimodel.request.ValidateRequest;
import id.co.bri.brimons.models.apimodel.request.ValidateTersimpanRequest;
import id.co.bri.brimons.models.apimodel.request.bilingual.PrefrencesLanguageRequest;
import id.co.bri.brimons.models.apimodel.request.nfcpayment.GeneratePayloadNfcRequest;
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimons.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimons.models.apimodel.response.ImageBannerResponse;
import id.co.bri.brimons.models.apimodel.response.LoginResponse;
import id.co.bri.brimons.models.apimodel.response.MaintenanceAlert;
import id.co.bri.brimons.models.apimodel.response.PromoResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SafetyModeResponse;
import id.co.bri.brimons.models.apimodel.response.nfcpayment.NfcPayloadResponse;
import id.co.bri.brimons.models.daomodel.FastMenu;
import id.co.bri.brimons.models.daomodel.FastMenuDefault;
import id.co.bri.brimons.presenters.MvpPresenter;
import id.co.bri.brimons.util.extension.DebugType;
import io.reactivex.CompletableObserver;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;


public class FastMenuNewSkinPresenter<V extends IMvpView & IFastMenuNewSkinView> extends MvpPresenter<V> implements IFastMenuNewSkinPresenter<V> {
    private static final String TAG = "FastMenuPresenter";

    private String mUsername = null;
    private String mTokenKey = null;
    private String mNama = null;
    private String urlValidate = "";
    private String urlRead;
    private String urlDetailItem;
    private String urlSafetyMode;
    private String urlLogin;
    private String urlLogout;
    private String urlChange;
    private String mUrlPrefrences;
    private String urlPromo;

    private String location = "";
    private boolean isChangeDevice;

    private String mAccountDefault = null;
    private ArrayList<ValidateTersimpanRequest> validateRequestBRIZZI = new ArrayList<>();

    private FastMenuSource fastMenuSource;
    private FastMenuDefaultSource fastMenuDefaultSource;
    private List<FastMenuDefault> fastMenusDefaults = new ArrayList<>();
    private List<FastMenu> fastMenus = new ArrayList<>();
    private Boolean isNfcAvailable = false;

    @Inject
    public FastMenuNewSkinPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, TransaksiPfmSource transaksiPfmSource, FastMenuSource fastMenuSource, FastMenuDefaultSource fastMenuDefaultSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.fastMenuSource = fastMenuSource;
        this.fastMenuDefaultSource = fastMenuDefaultSource;
    }

    @Override
    public Boolean getAktivasiVoiceAssistant() {
        return getBRImoPrefRepository().getAktivasiVoiceAssistant();
    }

    @Override
    public void getInitiateResource() {
        mTokenKey = getBRImoPrefRepository().getTokenKey();
        mNama = getBRImoPrefRepository().getName();
        mUsername = getBRImoPrefRepository().getUsername();
        mAccountDefault = getBRImoPrefRepository().getSaldoRekeningUtama();

        //add resource to View
        getView().onInitiateResourceSuccess(mUsername, mTokenKey, mNama);
    }

    @Override
    public void checkValidateBRIZZI() {
        ListValidateTersimpan listValidateTersimpan = new ListValidateTersimpan();
        validateRequestBRIZZI = getBRImoPrefRepository().getValidateRequest();

        if (validateRequestBRIZZI != null) {
            if (validateRequestBRIZZI.size() > 0) {
                for (int i = 0; i < validateRequestBRIZZI.size(); i++) {
                    if (validateRequestBRIZZI.get(i) != null) {
                        try {
                            reValidateBRIZZI(validateRequestBRIZZI.get(i).getValidateRequest());
                            validateRequestBRIZZI.remove(i);
                        } catch (Exception e) {
                            validateRequestBRIZZI.remove(i);
                        }
                    }
                }

                listValidateTersimpan.setValidateRequest(validateRequestBRIZZI);
                getBRImoPrefRepository().saveBrizziValidateList(listValidateTersimpan);

            } else {
                getBRImoPrefRepository().deleteBrizziValidate();
            }
        }
    }

    @Override
    public void reValidateBRIZZI(ValidateRequest request) {
        if (getUrlValidate().isEmpty())
            return;

        FastValidateBrizziRequest fastValidateBrizziRequest = new FastValidateBrizziRequest(
                getFastMenuRequest(),
                request.getCardNumber(),
                request.getRandomString(),
                request.getBalance(),
                request.getReff(),
                request.getReferenceNumber());

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(getUrlValidate(), fastValidateBrizziRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {

                        try {
                            ValidateTersimpanRequest validateTersimpanRequest = new ValidateTersimpanRequest(request, CalendarHelper.getCurrentDateString());
                            getBRImoPrefRepository().saveBrizziValidate(validateTersimpanRequest);
                        } catch (Exception e) {
                            if (!GeneralHelper.isProd()) {
                                GeneralHelper.debugMessage(e, TAG, DebugType.DEBUG);
                            }
                        }

                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //getBRImoPrefRepository().deleteBrizziValidate();
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        //getBRImoPrefRepository().deleteBrizziValidate();
                    }
                }));
    }


    @Override
    public void start() {
        super.start();

        setDisablePopup(false);

        getInitiateResource();

        checkBiometricConfig();

        getFastMenuFirst();

        getInitiateMenuRevamp(true, true);

    }

    private void getFastMenuFirst() {
        if (Boolean.FALSE.equals(getBRImoPrefRepository().getFastMenuFirst())) {
            getBRImoPrefRepository().saveFirstFastMenu(true);
            saveFirstFastMenuDefault(MenuConfig.fetchFastMenuWithNfcMenu(isNfcAvailable));
        }
    }

    public String getUrlValidate() {
        return urlValidate;
    }

    public void setUrlValidate(String urlValidate) {
        this.urlValidate = urlValidate;
    }

    @Override
    public void setUrlSafetyMode(String urlSafetyMode) {
        this.urlSafetyMode = urlSafetyMode;
    }

    @Override
    public void setUrlReadNotif(String urlRead) {
        this.urlRead = urlRead;
    }

    @Override
    public void getReadNotifFastMenu(String blastId) {
        NotifReadFastMenuRequest notifRequest = new NotifReadFastMenuRequest("android",
                blastId,
                getBRImoPrefRepository().getTokenKey(),
                getBRImoPrefRepository().getUsername());

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlRead, notifRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                    getView().onException99(restResponse.getDesc());
                                } else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getSafetyMode() {
        if (!isViewAttached() || urlSafetyMode.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        SafetyModeFMRequest safetyModeFMRequest = new SafetyModeFMRequest(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey());
        getCompositeDisposable().add(
                getApiSource().getData(urlSafetyMode, safetyModeFMRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    SafetyModeResponse safetyModeResponse = response.getData(SafetyModeResponse.class);
                                    if (safetyModeResponse != null) {
                                        getView().onSuccessTimerSafetyMode(safetyModeResponse);
                                    } else
                                        getView().onSafetyMode01();
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                    getView().onSafetyMode01();
                                } else if (restResponse.getCode().equalsIgnoreCase(Constant.RE_ALERT_MAINTENANCE)) {
                                    MaintenanceAlert alertResponse = restResponse.getData(MaintenanceAlert.class);
                                    getView().onMaintenanceAlert(alertResponse);
                                } else {
                                    getView().onSafetyMode01();
                                }
                            }
                        })
        );
    }

    @Override
    public void updateChangeDeviceFlag(boolean isChangeDevice) {
        getBRImoPrefRepository().saveChangeDeviceFlag(isChangeDevice);
    }

    @Override
    public boolean getStatusAktivasi() {
        return getBRImoPrefRepository().getStatusAktivasi();
    }

    @Override
    public boolean getBioChanged() {
        return getBRImoPrefRepository().getStatusBioChange();
    }

    @Override
    public boolean getStatusUpdateBio() {
        return getBRImoPrefRepository().getStatusUpdateBio();
    }

    @Override
    public void updateStatusAktivasi(boolean statusAktivasi) {
        getBRImoPrefRepository().saveStatusAktivasi(statusAktivasi);
    }

    @Override
    public void updateBioChange(boolean statusBioChange) {
        getBRImoPrefRepository().saveStatusBioChange(statusBioChange);
    }

    @Override
    public void updateStatusUpdateBio(boolean statusUpdate) {
        getBRImoPrefRepository().saveStatusUpdateBio(statusUpdate);
    }

    @Override
    public void setUrlLogin(String urlLogin) {
        this.urlLogin = urlLogin;
    }

    @Override
    public void setUrlLogout(String urlLogout) {
        this.urlLogout = urlLogout;
    }

    @Override
    public void setUrlChange(String urlChange) {
        this.urlChange = urlChange;
    }

    @Override
    public void loginFingerprint() {
        isChangeDevice = false;
        updateChangeDeviceFlag(isChangeDevice);
        if (getView() != null) {

            getView().showProgress();
            String username = getBRImoPrefRepository().getUsername();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().validateUserLoginFingerprint(urlLogin, username, location, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage, urlLogin);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            LoginResponse loginResponse = response.getData(LoginResponse.class);
                            switch (response.getCode()) {
                                case Constant.RE_SUCCESS -> {
                                    //update flag login
                                    updateLoginFlag(true);
                                    //to dashboard
                                    getView().onLoginSuccess();
                                }
                                case Constant.RE_CHECKING_DEVICE -> {
                                    //parsing data akan ada disini
                                    getBRImoPrefRepository().saveTokenKey(loginResponse.getTokenKey());
//                                    getView().onSubmitLoginSuccess(loginResponse);
                                    getBRImoPrefRepository().saveUsername(loginResponse.getUsername());
                                    updateLoginFlag(true);
                                    getView().onLoginSuccess();
                                }
                                case Constant.RE_CHANGE_DEVICE -> {
                                    getView().onDeviceChanged(response.getDesc(), loginResponse);
                                    isChangeDevice = true;
                                    updateChangeDeviceFlag(isChangeDevice);
                                }
                                case Constant.RE_CREATE_PIN -> getView().onCreatePin();
                                case Constant.RE01 ->
                                        getView().onChangeUsername(response.getDesc());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            String code = restResponse.getCode();
                            String desc = restResponse.getDesc();

                            if (code.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(desc);
                            } else if (code.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                getView().onException12();
                            } else if (code.equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(desc);
                            } else if (code.equals(RestResponse.ResponseCodeEnum.RC_LOGIN_EXCEED.getValue())) {
                                getView().onExceptionLoginExceed(restResponse.getData(ExceptionResponse.class));
                            } else {
                                getView().onException(desc);
                            }

                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void changeDevice(String refNum) {
        if (getView() != null) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(urlChange, new ChangeDeviceRequest("", refNum), seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            LoginResponse loginResponse = response.getData(LoginResponse.class);
                            if (response.getCode().equals(Constant.RE_SUCCESS)) {
                                //parsing data akan ada disini
                                getBRImoPrefRepository().saveTokenKey(loginResponse.getTokenKey());
                                getView().onChangeDevice(loginResponse.getOtpExpiredInSecond());
                            } else if (response.getCode().equals(Constant.RE_CHANGE_DEVICE_MNV)) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                                getView().onChangeMNV(response.getDesc(), loginResponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());

                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public String getImageUrl() {
        return getBRImoPrefRepository().getBannerImageUrl();
    }

    @Override
    public String getBioType() {
        return getBRImoPrefRepository().getBiometricType();
    }

    @Override
    public void getLocation(String location) {
        this.location = location;
    }

    @Override
    public void getImageBannerUrl() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        String tokenKey = getBRImoPrefRepository().getTokenKey();
        String username = getBRImoPrefRepository().getUsername();

        getCompositeDisposable().add(getApiSource().getBannerImageUrl(seqNum, tokenKey, username)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onBannerImageUrlFailed();
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        ImageBannerResponse imageBannerResponse = response.getData(ImageBannerResponse.class);
                        getBRImoPrefRepository().saveBannerImageUrl(imageBannerResponse.getImageUrl());
                        getBRImoPrefRepository().saveTitleBanner(imageBannerResponse.getTitle());
                        getView().onBannerImageUrlSuccess(imageBannerResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().onBannerImageUrlFailed();
                    }
                }));
    }

    @Override
    public String getImageBannerUrlLocal() {
        return getBRImoPrefRepository().getBannerImageUrl();
    }

    @Override
    public String getBannerTitleLocal() {
        return getBRImoPrefRepository().getTitleBanner();
    }

    @Override
    public void getDefaultFastMenu() {
        getCompositeDisposable().add(fastMenuSource.getFastMenu()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenus -> {
                    if (fastMenus != null) {
                        getView().onLoadDefaultFastmenu(fastMenus);
                    }
                }, throwable -> {
                    if (!GeneralHelper.isProd()) {
                        GeneralHelper.debugMessage(throwable, TAG, DebugType.DEBUG);
                    }
                }));
    }

    @Override
    public void getSavedFastMenu() {
        getCompositeDisposable().add(fastMenuDefaultSource.getFastMenuDefault()
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenuDefaults -> {
                }, throwable -> {
                    if (!GeneralHelper.isProd()) {
                        GeneralHelper.debugMessage(throwable, TAG, DebugType.DEBUG);
                    }
                }));
    }

    @Override
    public void saveFirstFastMenuDefault(List<FastMenuDefault> fastMenuDefaultList) {
        fastMenuDefaultSource.saveFastMenu(fastMenuDefaultList)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CompletableObserver() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {

                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onError(@NonNull Throwable e) {

                    }
                });
    }

    private void checkBiometricConfig() {
        if (!getBRImoPrefRepository().getUsername().isEmpty() && Boolean.TRUE.equals(GeneralHelper.checkBiometricSupport())) {
            if (getView() != null) {
                getView().setVisibleFingerprint(true);
            }
        } else {
            if (getView() != null) {
                getView().setVisibleFingerprint(false);
            }
        }
    }

    @Override
    public void setUrlPrefrences(String urlPrefrences) {
        mUrlPrefrences = urlPrefrences;
    }

    //need to be review
    @Override
    public void updatePrefrencesLanguage(String id) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        PrefrencesLanguageRequest request = new PrefrencesLanguageRequest(getFastMenuRequest(), id);
        getCompositeDisposable().add(
                getApiSource().getData(mUrlPrefrences, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getBRImoPrefRepository().saveLanguage(id);
                                getView().onSuccessChangeLanguage();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void getInitiateMenuRevamp(boolean isToggled, boolean isAvailable) {
        getCompositeDisposable().add(fastMenuDefaultSource.getAllFastMenuDefaultByPosition(isToggled, isAvailable)
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(fastMenus -> {
                    if (fastMenus != null && !fastMenus.isEmpty()) {
                        cleanAndUpdateMenuList(fastMenus);
                        getView().onInitiateMenuRevamp(fastMenusDefaults);

                        if (fastMenusDefaults.size() <= 3) {
                            getView().onMenuLessThan3();
                        } else {
                            getView().onMenuMoreThan3();
                        }
                    } else {
                        getInitiateMenuRevamp(true, true);
                    }
                }, throwable -> {
                    getView().onInitiateMenu(MenuConfig.fetchFastMenu());
                    if (!GeneralHelper.isProd()) {
                        GeneralHelper.debugMessage(throwable, TAG, DebugType.DEBUG);
                    }
                }));
    }

    private void cleanAndUpdateMenuList(List<FastMenuDefault> menuModelListDefault) {
        fastMenusDefaults.clear();
        fastMenusDefaults.addAll(menuModelListDefault);

        fastMenus.clear();
        for (FastMenuDefault fmd : fastMenusDefaults) {
            fastMenus.add(new FastMenu(
                    fmd.getId(),
                    fmd.getKode(),
                    fmd.getMenuName(),
                    fmd.getGambarMenu(),
                    fmd.getMenu(),
                    fmd.getTag(),
                    fmd.isFlagNew()
            ));
        }
    }

    @Override
    public String getUsernameAlias() {
        return getBRImoPrefRepository().getUserAlias();
    }

    @Override
    public void getDataPayloadNfc(String pin) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        String tokenKey = getBRImoPrefRepository().getTokenKey();
        String username = getBRImoPrefRepository().getUsername();

        GeneratePayloadNfcRequest request = new GeneratePayloadNfcRequest(username, tokenKey, pin, "", "", "");

        getCompositeDisposable().add(getApiSource().getData(GeneralHelper.getString(R.string.url_generate_payload_fm), request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        NfcPayloadResponse data = response.getData(NfcPayloadResponse.class);
                        getView().onSuccessGetNfcPayload(data);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(Constant.RE_FITUR_OFF)) {
                            EmptyStateResponse response = restResponse.getData(EmptyStateResponse.class);
                            getView().onExceptionFO(response);
                        } else {
                            getView().onException(restResponse.getDesc());
                        }
                    }
                })
        );
    }

    @Override
    public void checkAvailabilityNfc(boolean isNfcAvailable) {
        this.isNfcAvailable = isNfcAvailable;
    }

    @Override
    public void setUrlPromo(@NonNull String urlPromo) {
        this.urlPromo = urlPromo;
    }

    @Override
    public void getPromo() {
        if (!isViewAttached() || urlPromo.isEmpty())
            return;
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataForm(urlPromo, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
//                                getView().onException(errorMessage,urlPromo);
                                getView().showPromo(false);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().showPromo(true);
                                PromoResponse promoResponse = response.getData(PromoResponse.class);
                                getView().onSuccessGetPromo(promoResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
//                                getView().onException(restResponse.getDesc());
                                getView().showPromo(false);
                            }
                        })
        );
    }

    @NonNull
    @Override
    public String getNameOfUser() {
        return getBRImoPrefRepository().getUserAlias();
    }

    @Override
    public void setDetailItemPromoUrl(@Nullable String detailItemUrl) {
        urlDetailItem = detailItemUrl != null ? detailItemUrl : "";
    }

    @Override
    public void getDetailPromoItem(@Nullable String id) {
        if (urlDetailItem.isEmpty() || !isViewAttached()) {
            return;
        }

        if (getView() != null) {
            getView().showProgress();
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        DetailPromoRequest detailPromoRequest = new DetailPromoRequest(id);

        getCompositeDisposable().add(
                getApiSource().getData(urlDetailItem, detailPromoRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            public void onFailureHttp(String errorMessage) {
                                if (getView() != null) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }
                            }

                            @Override
                            public void onApiCallSuccess(RestResponse response) {
                                if (getView() != null) {
                                    getView().hideProgress();
                                    PromoResponse responseBriva = response.getData(PromoResponse.class);
                                    getView().onSuccessGetDetailItem(responseBriva);
                                }
                            }

                            @Override
                            public void onApiCallError(RestResponse restResponse) {
                                if (getView() != null) {
                                    getView().hideProgress();
                                    String code = restResponse.getCode();
                                    String desc = restResponse.getDesc();

                                    if (code == RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()) {
                                        view.onSessionEnd(desc);
                                    } else if (code == RestResponse.ResponseCodeEnum.RC_99.getValue()) {
                                        view.onException99(desc);
                                    } else {
                                        view.onException(desc);
                                    }
                                }
                            }
                        })
        );

    }

    @Override
    public void setBioChangedDialogShown(boolean statusBioChangeShown) {
        getBRImoPrefRepository().setBioChangedDialogShown(statusBioChangeShown);
    }

    @Override
    public boolean isBioChangedDialogShown()   {
        return getBRImoPrefRepository().isBioChangedDialogShown();
    }

    @Override
    public void setBiometricLockoutPermanently(boolean IsBiometricLockoutPermanently) {
        getBRImoPrefRepository().setBiometricLockoutPermanently(IsBiometricLockoutPermanently);

    }

    @Override
    public boolean isBiometricLockoutPermanently() {
        return getBRImoPrefRepository().isBiometricLockoutPermanently();
    }



    @NonNull
    @Override
    public String getFirstNameOfUser() {
        return getBRImoPrefRepository().getFirstName();
    }

    @NonNull
    @Override
    public String getNicknameOfUser() {
        return getBRImoPrefRepository().getNickname();
    }
}