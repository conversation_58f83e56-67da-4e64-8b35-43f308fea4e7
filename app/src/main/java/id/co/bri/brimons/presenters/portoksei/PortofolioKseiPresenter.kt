package id.co.bri.brimons.presenters.portoksei

import id.co.bri.brimons.contract.IPresenter.portoksei.IPortofolioKseiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.portoksei.IPortofolioKseiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.portofolioksei.GetDetailKseiRequest
import id.co.bri.brimons.models.apimodel.response.MessageResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.portofolioksei.DetailAssetResponse
import id.co.bri.brimons.models.apimodel.response.portofolioksei.ItemAssetResponse
import id.co.bri.brimons.models.apimodel.response.portofolioksei.RegistrasiResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit

class PortofolioKseiPresenter<V>(schedulerProvider: SchedulerProvider?,
                                 compositeDisposable: CompositeDisposable?,
                                 mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                 categoryPfmSource: CategoryPfmSource?,
                                 transaksiPfmSource: TransaksiPfmSource?,
                                 anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IPortofolioKseiPresenter<V> where V : IMvpView?, V : IPortofolioKseiView {

    var mUrl : String? = null
    var mUrlDetail : String? = null
    var urlRegis : String? = null

    override fun setUrlPortofolio(url: String?) {
        this.mUrl = url
    }

    override fun setUrlDetail(urlDetail: String) {
        this.mUrlDetail = urlDetail
    }

    override fun getDataDetail(req: GetDetailKseiRequest) {
        if (mUrlDetail == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(mUrlDetail, req, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()!!.hideProgress()
                                        getView()!!.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()!!.hideProgress()
                                        GeneralHelper.responseChuck(response)
                                        val detailResponse = response.getData(DetailAssetResponse::class.java)
                                        getView()!!.onSuccessGetDetail(detailResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()!!.hideProgress()
                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()!!.onSessionEnd(restResponse.desc)
                                        else if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_12.value,
                                                        ignoreCase = true
                                                ))
                                            getView().onException12(restResponse.desc)
                                        else getView()!!.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
        )
    }

    override fun setUrlRegisKsei(url: String) {
        urlRegis = url
    }

    override fun getRegisKsei() {
        if (urlRegis == null || !isViewAttached) {
            return
        }
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getDataTanpaRequest(urlRegis, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        //TO-DO onSuccess
                        val detailResponse = response.getData(RegistrasiResponse::class.java)
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)){
                            getView()!!.onSuccessRegisKsei(detailResponse, response.code)
                        }else if(response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)){
                            getView()!!.onSuccessBottomDrawer(detailResponse,response.code)
                        }else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value)){
                            getView()!!.onSuccessBottomDrawer(detailResponse,response.code)
                        }else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_03.value)){
                            getView()!!.onSucessDashboard()
                        }

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                        ignoreCase = true
                                )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView().onException12(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)){
                            getView()!!.onException93(restResponse.desc)
                        }
                        else getView()!!.onException(
                                restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getDataPortofolio() {
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getDataTanpaRequest(mUrl, seqNum) //function(param)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                if (response.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SUCCESS.value,
                                                ignoreCase = true
                                        )){
                                    val response = response.getData(
                                            ItemAssetResponse::class.java
                                    )
                                    getView().onSuccessGetData(response)
                                }
                                else if(response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)){
                                    val response = response.getData(
                                            MessageResponse::class.java
                                    )
                                    getView().onException01(response)
                                }
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)
                                ) getView().onSessionEnd(restResponse.desc)
                                else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                    getView().onException12(restResponse.desc)
                                } else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                    getView().onException21()
                                } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) {
                                    getView()!!.onException93(restResponse.desc)
                                } else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) {
                                    getView()
                                } else getView().onException(restResponse.desc)
                            }
                        })
        compositeDisposable.add(disposable)
    }

}