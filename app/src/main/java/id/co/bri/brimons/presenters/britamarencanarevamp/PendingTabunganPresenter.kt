package id.co.bri.brimons.presenters.britamarencanarevamp

import id.co.bri.brimons.contract.IPresenter.britamarencanarevamp.IPendingTabunganPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.britamarencanarevamp.IPendingTabunganView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.PFMMessageResponse
import id.co.bri.brimons.models.apimodel.request.StatusRequest
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class PendingTabunganPresenter<V>(schedulerProvider: SchedulerProvider,
                                  compositeDisposable: CompositeDisposable,
                                  mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                  categoryPfmSource: CategoryPfmSource,
                                  transaksiPfmSource: TransaksiPfmSource,
                                  anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IPendingTabunganPresenter<V> where V : IMvpView, V : IPendingTabunganView {

    private var mUrl: String? = null

    override fun setUrl(url: String) {
        mUrl = url
    }

    override fun getDataPending(reffnum: String) {
        //initiate param with getter from view
        view.showProgress()
        onLoad = true
        val request = StatusRequest(reffnum)
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable = apiSource.getData(mUrl, request, seqNum) //function(param)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView().hideProgress()
                    val pendingResponse = response.getData(ReceiptRevampResponse::class.java)
                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                        getView().onSuccesReceipt(pendingResponse)
                        onLoad = false
                    } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) {
                        getView().onGetTransaksiGagal(response)
                    }
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onLoad = false
                    getView().hideProgress()
                    val response = restResponse.getData(PFMMessageResponse::class.java)
                    if ( restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) {
                        getView().onException93(restResponse.desc)
                    }
                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_78.value)){
                        getView()?.onExceptionSnakcbarBlue(response.message)
                    }
                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_79.value)){
                        getView()?.onExceptionRedirect(response.message)
                    }
                    else{
                        onApiError(restResponse)
                    }
                }
            })

        compositeDisposable.add(disposable)
    }
}