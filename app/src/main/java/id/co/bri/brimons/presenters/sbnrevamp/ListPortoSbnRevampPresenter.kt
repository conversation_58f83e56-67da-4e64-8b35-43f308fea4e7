package id.co.bri.brimons.presenters.sbnrevamp

import id.co.bri.brimons.contract.IPresenter.sbnrevamp.IListPortoSbnRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.sbnrevamp.IListPortoSbnRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.sbnrevamp.SbnDetailPortoRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.sbnrevamp.SbnDetailPortoResponse
import id.co.bri.brimons.models.apimodel.response.sbnrevamp.SbnListPortoResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ListPortoSbnRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                                     compositeDisposable: CompositeDisposable,
                                     mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                     categoryPfmSource: CategoryPfmSource,
                                     transaksiPfmSource: TransaksiPfmSource,
                                     anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IListPortoSbnRevampPresenter<V> where V : IMvpView, V : IListPortoSbnRevampView {

    private var urlListPorto = ""
    private var urlDetail = ""

    override fun setUrlListPortoSbn(urlListPorto: String) {
        this.urlListPorto = urlListPorto
    }

    override fun setUrlSbnDetailPorto(urlDetailItemPorto: String) {
        this.urlDetail = urlDetailItemPorto
    }

    override fun getListPortoSbn() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(urlListPorto, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(SbnListPortoResponse::class.java)
                            getView().onSuccessGetListPortoSbn(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDataDetailPortoSbn(request: SbnDetailPortoRequest) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlDetail, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data =  response.getData(SbnDetailPortoResponse::class.java)

                            getView().onSuccessGetDetailPorto(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }
}