package id.co.bri.brimons.presenters.general;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.general.IGeneralReceiptPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.general.IGeneralReceiptView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.DetailVoucherStreamingRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryReceiptRequest;
import id.co.bri.brimons.models.apimodel.request.lifestyle.LifestylePatternRequest;
import id.co.bri.brimons.models.apimodel.request.lifestyle.shopping.ConfirmationMobelanjaRequest;
import id.co.bri.brimons.models.apimodel.request.smartrecom.SmartRecomFeedbackRequest;
import id.co.bri.brimons.models.apimodel.request.smartrecom.SmartRecomRequest;
import id.co.bri.brimons.models.apimodel.request.smarttransfer.AnalyticSmartTransferRequest;
import id.co.bri.brimons.models.apimodel.request.smarttransfer.ManageUserSmartTransferConsentRequest;
import id.co.bri.brimons.models.apimodel.request.smarttransfer.SimilarityAccountSmartTransferRequest;
import id.co.bri.brimons.models.apimodel.request.voucher.VoucherRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryReceiptResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryRevampReceiptResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.lifestyle.PatternLifestyleTrackingResponse;
import id.co.bri.brimons.models.apimodel.response.lifestyle.UrlTrackingMobelanjaResponse;
import id.co.bri.brimons.models.apimodel.response.smartrecom.SmartRecomResponse;
import id.co.bri.brimons.models.apimodel.response.smarttransfer.SmartTransferAccountListConsentResponse;
import id.co.bri.brimons.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding;
import id.co.bri.brimons.models.apimodel.response.smarttransfer.SmartTransferGeneralResponse;
import id.co.bri.brimons.models.apimodel.response.voucher.TutorialVoucherResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import id.co.bri.brimons.models.apimodel.request.ccqrismpm.SetDefaultQrPaymentRequest;

public class GeneralReceiptPresenter<V extends IMvpView & IGeneralReceiptView> extends MvpPresenter<V> implements IGeneralReceiptPresenter<V> {

    public GeneralReceiptPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    private String mUrlCaraRedeem;
    private String urlChangeSof;

    private String mUrlTrackingMobelanja;
    public String mUrlSmartRecom;
    public String mUrlSmartRecomFeedback;
    public String inquiryUrl;
    public String mUrlCheckSimilaritySmartTransfer = "";
    public String mUrlAccountListConsent = "";

    public String mAnalyticSmartTransferUrl = "";
    public String urlSmartTransferManageUserConsent = "";

    public String mUrlTrackingPattern;

    @Override
    public void start() {
        super.start();
        setDisablePopup(true);
    }

    @Override
    public void setUrlCaraRedeem(String urlCaraRedeem) {
        mUrlCaraRedeem = urlCaraRedeem;
    }

    @Override
    public void setUrlChangeSof(String urlChangeSof) {
        this.urlChangeSof = urlChangeSof;
    }

    @Override
    public void setUrlGetRecom(String urlGetRecom) {
        mUrlSmartRecom = urlGetRecom;
    }

    @Override
    public void setUrlGetRecomFeedback(String urlGetRecomFeedback) {
        mUrlSmartRecomFeedback = urlGetRecomFeedback;
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }

    @Override
    public void setUrlTrackingMobelanja(String urlTrackingMobelanja) {
        mUrlTrackingMobelanja = urlTrackingMobelanja;
    }

    @Override
    public void setUrlTrackingPattern(String urlTrackingPattern) {
        mUrlTrackingPattern = urlTrackingPattern;
    }

    @Override
    public void setUrlCheckSimilaritySmartTransfer(String urlCheckSimilaritySmartTransfer) {
        mUrlCheckSimilaritySmartTransfer = urlCheckSimilaritySmartTransfer;
    }

    @Override
    public void setUrlAccListConsent(String urlAccountListConsent) {
        mUrlAccountListConsent = urlAccountListConsent;
    }

    @Override
    public void setAnalyticSmartTransferUrl(String url) {
        mAnalyticSmartTransferUrl = url;
    }

    @Override
    public void setUrlSmartTransferManageUserConsent(String urlManageUserConsent) {
        urlSmartTransferManageUserConsent = urlManageUserConsent;
    }

    @Override
    public void getCaraRedeemVoucherGame(VoucherRequest voucherRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();

            getCompositeDisposable().add(getApiSource().getData(mUrlCaraRedeem, voucherRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            TutorialVoucherResponse tutorialVoucherResponse = response.getData(TutorialVoucherResponse.class);
                            getView().onSuccessGetTutorial(tutorialVoucherResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getCaraRedeemVoucherStreaming(DetailVoucherStreamingRequest streamingRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();

            getCompositeDisposable().add(getApiSource().getData(mUrlCaraRedeem, streamingRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            TutorialVoucherResponse tutorialVoucherResponse = response.getData(TutorialVoucherResponse.class);
                            getView().onSuccessGetTutorial(tutorialVoucherResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getUrlTrackingMobelanja(ConfirmationMobelanjaRequest urlTrackingRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();

            getCompositeDisposable().add(getApiSource().getData(mUrlTrackingMobelanja, urlTrackingRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            UrlTrackingMobelanjaResponse urlTrackingMobelanjaResponse =
                                    response.getData(UrlTrackingMobelanjaResponse.class);
                            getView().onSuccessGetUrlTrackingMobelanja(urlTrackingMobelanjaResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }
    public Boolean isSuccessBinding(String bindingResp){
        return bindingResp.contains(Constant.RE_SUCCESS);
    }

    @Override
    public void getSmartRecommendation() {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            SmartRecomRequest request = new SmartRecomRequest(getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(mUrlSmartRecom, request, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().onFailedGetSmartRecom();
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            SmartRecomResponse smartRecomResponse = response.getData(SmartRecomResponse.class);
                            getView().onSuccessGetSmartRecom(smartRecomResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().onFailedGetSmartRecom();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }

    @Override
    public void sendSmartRecommendationFeedback(SmartRecomFeedbackRequest request) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();


            request.setUsername(getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(mUrlSmartRecomFeedback, request, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {

                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }

    @Override
    public void getDataInquiry(InquiryReceiptRequest inquiryReceiptRequest) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataNotif(inquiryUrl, inquiryReceiptRequest.getReqSeq(), seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (inquiryReceiptRequest.getTypeInquiryRevamp() != null && !inquiryReceiptRequest.getTypeInquiryRevamp().isEmpty()) {
                                    getView().onSuccessGetInquiryRevamp(new InquiryRevampReceiptResponse(inquiryReceiptRequest.getTypeInquiry(), response, inquiryReceiptRequest.getUrlKonfirmasi(), inquiryReceiptRequest.getUrlPayment(), inquiryReceiptRequest.getTitle(), inquiryReceiptRequest.getTypeInquiryRevamp(), inquiryReceiptRequest.getReqSeq()));
                                } else if (inquiryReceiptRequest.getTypeInquiry().equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.listrikPrepaid)) {
                                    getView().onSuccessGetInquiryRevamp(new InquiryRevampReceiptResponse(inquiryReceiptRequest.getTypeInquiry(), response, inquiryReceiptRequest.getUrlKonfirmasi(), inquiryReceiptRequest.getUrlPayment(), inquiryReceiptRequest.getTitle(), inquiryReceiptRequest.getTypeInquiryRevamp(), inquiryReceiptRequest.getReqSeq()));
                                } else {
                                    GeneralInquiryResponse generalInquiryResponse = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessGetInquiry(new InquiryReceiptResponse(inquiryReceiptRequest.getTypeInquiry(), generalInquiryResponse, inquiryReceiptRequest.getUrlKonfirmasi(), inquiryReceiptRequest.getUrlPayment(), inquiryReceiptRequest.getTitle()));
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void getUrlTrackingPattern(LifestylePatternRequest urlTrackingRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();

            getCompositeDisposable().add(getApiSource().getData(mUrlTrackingPattern, urlTrackingRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            PatternLifestyleTrackingResponse patternLifestyleTrackingResponse =
                                    response.getData(PatternLifestyleTrackingResponse.class);
                            getView().onSuccessGetTrackingPattern(patternLifestyleTrackingResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equals("04")){
                                getView().onExceptionEticketNotIssued();
                            } else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }


    @Override
    public void changeSofCc(SetDefaultQrPaymentRequest request) {
        if (!isViewAttached()) return;
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getData(urlChangeSof, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        getView().onSuccessChangeSof(
                                restResponse.getDesc()
                        );
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        onApiError(restResponse);

                    }
                });

        getCompositeDisposable().add(disposable);
    }
    @Override
    public void detectIsDialogNeedToShow( ) {
        int maxToShow = 5;
        int count = getBRImoPrefRepository().getCountCcQrisNotSof()+1;
        getBRImoPrefRepository().saveCountCcQrisNotSof(count);
        if (getBRImoPrefRepository().getCountCcQrisNotSof()>=maxToShow){
            getBRImoPrefRepository().saveCountCcQrisNotSof(0);
            getView().onShowBottomSheetCcAsSof();
        }
    }

    @Override
    public void checkSimilaritySmartTransfer(SimilarityAccountSmartTransferRequest similarityAccountRequest) {
        if (isViewAttached()){

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(mUrlCheckSimilaritySmartTransfer, similarityAccountRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            //do nothing
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            SmartTransferGeneralResponse smartTransferResponse = response.getData(SmartTransferGeneralResponse.class);
                            getView().onSuccessCheckSimilaritySmartTransfer(smartTransferResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            //do nothing
                        }
                    }));
        }
    }

    @Override
    public void getAccountListConsent() {
        if (isViewAttached()){

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            SmartRecomRequest request = new SmartRecomRequest(getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(mUrlAccountListConsent, request, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            SmartTransferAccountListConsentResponse smartTransferAccountListConsentResponse  = response.getData(SmartTransferAccountListConsentResponse.class);
                            getView().onSucessGetAccountListConsent(smartTransferAccountListConsentResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void sendAnalyticSmartTransferUrl(AnalyticSmartTransferRequest analyticApprovalRequest) {
        if (isViewAttached()){

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            analyticApprovalRequest.setUsername(getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(mAnalyticSmartTransferUrl, analyticApprovalRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            //do nothing
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            //do nothing

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            //do nothing
                        }
                    }));
        }
    }

    @Override
    public void smartTransferManageUserConsent(Boolean status) {
        if (isViewAttached()){
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            ManageUserSmartTransferConsentRequest request = new ManageUserSmartTransferConsentRequest(status, getBRImoPrefRepository().getUsername());

            getCompositeDisposable().add(getApiSource().getData(urlSmartTransferManageUserConsent, request, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            SmartTransferConfirmAccBinding smartTransferConfirmAccBinding = response.getData(SmartTransferConfirmAccBinding.class);
                            getView().onSuccessSmartTransferManageUserConsent(smartTransferConfirmAccBinding);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("05"))
                                getView().onSessionEnd(restResponse.getDesc());
                            else {
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }
}