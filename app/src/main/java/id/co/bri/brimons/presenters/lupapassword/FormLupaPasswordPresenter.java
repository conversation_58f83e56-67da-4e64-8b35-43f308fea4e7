package id.co.bri.brimons.presenters.lupapassword;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.lupapassword.IFormLupaPasswordPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.lupapassword.IFormLupaPasswordView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.ForgetPassInquReq;
import id.co.bri.brimons.models.apimodel.response.DetailAkunResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class FormLupaPasswordPresenter<V extends IMvpView & IFormLupaPasswordView> extends MvpPresenter<V> implements IFormLupaPasswordPresenter<V> {

    protected String url;

    public FormLupaPasswordPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                     BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                     TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onSendDataInquiry(ForgetPassInquReq request) {
        if (url.isEmpty() || !isViewAttached()) return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable().add(getApiSource().getData(url, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        DetailAkunResponse akunResponse = response.getData(DetailAkunResponse.class);
                        getView().onDataSuccess(akunResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        getView().onException(restResponse.getDesc());
                    }
                }));
    }

    @Override
    public void getIsNotLogin() {
        getView().isNotLogin(getBRImoPrefRepository().getUsername().isEmpty());
    }
}