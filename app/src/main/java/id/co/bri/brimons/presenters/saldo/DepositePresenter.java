package id.co.bri.brimons.presenters.saldo;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.saldo.IDepositoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.saldo.IDepositoView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.converter.MapperHelper;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.RequestDetailDeposito;
import id.co.bri.brimons.models.apimodel.response.DepositoResponse;
import id.co.bri.brimons.models.apimodel.response.DetailDepositoResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.TermConditionTabRes;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Function;
import io.reactivex.observers.DisposableObserver;
import io.reactivex.schedulers.Schedulers;

public class DepositePresenter<V extends IMvpView & IDepositoView> extends MvpPresenter<V> implements IDepositoPresenter<V> {

    private static final String TAG = "DepositoPresenter";
    protected String formUrl;
    protected String detailUrl;
    protected String termUrl;

    DepositoResponse depositoResponse = new DepositoResponse();
    List<DepositoResponse.Account> accountList;

    boolean isLoading = false;

    public DepositePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getDataDeposito() {
        if (isLoading)
            return;

        if (isViewAttached()) {
            isLoading = true;
            accountList = new ArrayList<>();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(formUrl, "", seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                                       @Override
                                       protected void onFailureHttp(String type) {
                                           getView().onException(type);
                                       }

                                       @Override
                                       protected void onApiCallSuccess(RestResponse response) {
                                           depositoResponse = restResponse.getData(DepositoResponse.class);
                                           accountList.addAll(depositoResponse.getAccount());
                                           //7
                                           getView().onSuccessGetData(depositoResponse);
                                       }

                                       @Override
                                       protected void onApiCallError(RestResponse restResponse) {
                                           if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                               getView().onSessionEnd(restResponse.getDesc());
                                           } else {
                                               getView().onException(restResponse.getDesc());
                                           }
                                       }

                                       @Override
                                       public void onComplete() {
                                           super.onComplete();
                                           getSaldoRefresh(accountList, false);
                                       }
                                   }
                    ));
        }

    }

    @Override
    public void getSaldoRefresh(List<DepositoResponse.Account> list, boolean isRefreshed) {
        getView().showProgress();
        Observable.fromIterable(list)
                .flatMap(new Function<DepositoResponse.Account, ObservableSource<DepositoResponse.Account>>() {
                    @Override
                    public ObservableSource<DepositoResponse.Account> apply(DepositoResponse.Account account) throws Exception {
                        return getSaldoObservable(account);
                    }
                })
                .subscribeWith(new DisposableObserver<DepositoResponse.Account>() {

                    @Override
                    public void onNext(DepositoResponse.Account account) {
                        int postion = list.indexOf(account);
                        if (postion == -1)
                            return;
                        list.set(postion, account);

                        getView().hideProgress();
                        getView().onGetSaldo(list, isRefreshed);
                    }

                    @Override
                    public void onError(Throwable e) {
                        getView().hideProgress();
                    }

                    @Override
                    public void onComplete() {
                        getView().onGetSaldoComplete();
                        isLoading = false;
                    }
                });
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    @Override
    public void setTermUrl(String termUrl) {
        this.termUrl = termUrl;
    }

    public Observable<DepositoResponse.Account> getSaldoObservable(DepositoResponse.Account account) {

        String seq = getBRImoPrefRepository().getSeqNumber();

        RequestDetailDeposito requestDetailDeposito = new RequestDetailDeposito(account.getAccount());

        return getApiSource().getData(detailUrl, requestDetailDeposito, seq)

                .delay(90, TimeUnit.MILLISECONDS)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .map(new Function<String, DepositoResponse.Account>() {
                    @Override
                    public DepositoResponse.Account apply(String stringResponse) throws Exception {
                        RestResponse restResponse = null;
                        DetailDepositoResponse detailDepositoResponse;

                        //get checksum response
                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        //jika check
                        if (responseCheck.isEmpty()) {
                            detailDepositoResponse = new DetailDepositoResponse();
                            detailDepositoResponse.setBalanceString("-");
                        }

                        //coba konversi String ke RestResponse model
                        restResponse = MapperHelper.stringToRestResponse(stringResponse, seq);

                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {

                                if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    detailDepositoResponse = new DetailDepositoResponse();
                                    detailDepositoResponse.setBalanceString(restResponse.getDesc());
                                } else {
                                    detailDepositoResponse = new DetailDepositoResponse();
                                    detailDepositoResponse = restResponse.getData(DetailDepositoResponse.class);

                                }
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                detailDepositoResponse = new DetailDepositoResponse();
                                detailDepositoResponse.setBalanceString(restResponse.getCode());
                                detailDepositoResponse.setName(restResponse.getDesc());
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                detailDepositoResponse = new DetailDepositoResponse();
                                detailDepositoResponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                detailDepositoResponse = new DetailDepositoResponse();
                                detailDepositoResponse.setBalanceString("-");
                            }
                        } else {
                            detailDepositoResponse = new DetailDepositoResponse();
                            detailDepositoResponse.setBalanceString("-");
                        }
                        account.setDetailDepositoResponse(detailDepositoResponse);
                        return account;
                    }

                });
    }

    @Override
    public void getDataTerm() {
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgressTerm();
            getCompositeDisposable().add(
                    getApiSource().getData(termUrl, "", seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgressTerm();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSucces
                                    getView().hideProgressTerm();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        TermConditionTabRes termConditionTabRes = response.getData(TermConditionTabRes.class);
                                        getView().onGetDataTerm(termConditionTabRes);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        getView().onGetDataProses();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgressTerm();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            })
            );
        }
    }

    @Override
    public void start() {
        super.start();
        getDataDeposito();
    }

}