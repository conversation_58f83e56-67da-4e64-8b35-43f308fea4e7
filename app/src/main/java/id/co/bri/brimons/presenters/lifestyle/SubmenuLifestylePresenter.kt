package id.co.bri.brimons.presenters.lifestyle

import id.co.bri.brimons.contract.IPresenter.lifestyle.ISubmenuLifestylePresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.lifestyle.ISubmenuLifestyleView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.lifestyle.MenuLifestyleSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.config.MenuConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimons.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.lifestyle.EODLifestyleResponse
import id.co.bri.brimons.models.daomodel.lifestyle.MenuLifestyle
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableCompletableObserver
import io.reactivex.observers.DisposableMaybeObserver
import java.util.concurrent.TimeUnit

class SubmenuLifestylePresenter <V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    private val menuLifestyleSource: MenuLifestyleSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    ISubmenuLifestylePresenter<V> where V : IMvpView?, V : ISubmenuLifestyleView? {

    private var mUrlTugu: String = ""

    override fun setUrlWebviewTugu(urlTugu: String) {
        mUrlTugu = urlTugu
    }

    override fun getMenuLifestyle() {
        if (isViewAttached) {
            compositeDisposable.add(menuLifestyleSource.getAllMenuLifestyle()
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableMaybeObserver<List<MenuLifestyle>>() {
                    override fun onSuccess(menuLifestyleList: List<MenuLifestyle>) {
                        if (menuLifestyleList.isNotEmpty()) {
                            view?.onSuccessGetMenuLocals(menuLifestyleList)
                        }
                    }

                    override fun onError(e: Throwable) {}
                    override fun onComplete() {}
                })
            )
        }
    }

    override fun onUpdateFlagNewMenu(featureCode: String) {
        compositeDisposable.add(
            menuLifestyleSource.updateMenuLifestyle(featureCode, MenuConfig.NewStatus.OLD)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableCompletableObserver() {
                    override fun onComplete() {
                        getMenuLifestyle()
                    }

                    override fun onError(e: Throwable) {
                        // do nothing
                    }
                })
        )
    }

    override fun getWebViewTugu(
        partnerIdRequest: PartnerIdRequest?,
        titleBar: String,
        codeMenu: String
    ) {
        if (!isViewAttached) {
            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(mUrlTugu, partnerIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        if (response.code.equals(Constant.RE_SUCCESS, ignoreCase = true)) {
                            val webviewResponse = response.getData(
                                GeneralWebviewResponse::class.java
                            )
                            getView()?.onSuccessGetWebviewTugu(
                                webviewResponse,
                                titleBar,
                                codeMenu
                            )
                        } else if (response.code.equals(Constant.RE02, ignoreCase = true)) {
                            val eodLifestyleResponse: EODLifestyleResponse =
                                response.getData(
                                    EODLifestyleResponse::class.java
                                )
                            getView()?.onMenuLifestyleEOD(eodLifestyleResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                    }
                })
        )
    }

}