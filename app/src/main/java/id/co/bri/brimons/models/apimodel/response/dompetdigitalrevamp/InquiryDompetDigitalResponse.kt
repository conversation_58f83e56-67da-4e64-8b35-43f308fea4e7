package id.co.bri.brimons.models.apimodel.response.dompetdigitalrevamp

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimons.models.AccountModel
import id.co.bri.brimons.models.BillingDetail
import id.co.bri.brimons.models.BillingDetailOpen
import id.co.bri.brimons.models.apimodel.response.dplkrevamp.DialogKlaimDplkResponse

class InquiryDompetDigitalResponse (
    @field:Expose
    @field:SerializedName("account_list") var accountModel: MutableList<AccountModel> = mutableListOf(),
    @field: Expose
    @field:SerializedName("billing_detail") var billingDetail: MutableList<BillingDetail> = mutableListOf(),
    @field: Expose
    @field:SerializedName("billing_detail_open") var billingDetailOpen: MutableList<BillingDetailOpen> = mutableListOf(),
    @field: Expose
    @field:SerializedName("billing_amount") var billingAmount: MutableList<BillingDetail> = mutableListOf(),
    @field: Expose
    @field:SerializedName("billing_amount_detail") var billingAmountDetail: MutableList<BillingDetail> = mutableListOf(),
    @field: Expose
    @field:SerializedName("billing_purchase_detail") var billingPurchaseDetail: MutableList<BillingDetail> = mutableListOf(),
    @field: Expose
    @field:SerializedName("option_amount") var optionAmount: MutableList<OptionAmountItem> = mutableListOf(),
    @field: Expose
    @field:SerializedName("name_default") var nameDefault: String = "",
    @field: Expose
    @field:SerializedName("open_payment") var openPayment: Boolean = false,
    @field: Expose
    @field:SerializedName("is_billing") var isBilling: Boolean= false,
    @field: Expose
    @field:SerializedName("minimum_payment") var minimumPayment: Boolean= false,
    @field: Expose
    @field:SerializedName("row_data_show") var rowDataShow: Int = 0,
    @field: Expose
    @field:SerializedName("saved") var saved: String = "",
    @field: Expose
    @field:SerializedName("amount") var amount: Int = 0,
    @field: Expose
    @field:SerializedName("amount_string") var amountString: String ="",
    @field: Expose
    @field:SerializedName("minimum_amount") var minimumAmount: Int = 0,
    @field: Expose
    @field:SerializedName("minimum_amount_string") var minimumAmountString: String ="",
    @field: Expose
    @field:SerializedName("admin_fee") var adminFee: Int = 0,
    @field: Expose
    @field:SerializedName("admin_fee_string") var adminFeeString: String ="",
    @field: Expose
    @field:SerializedName("pay_amount") var payAmount: Int =0,
    @field: Expose
    @field:SerializedName("pay_amount_string") var payAmountString: String = "",
    @field: Expose
    @field:SerializedName("minimum_transaction") var minimumTransaction: Long = 0L,
    @field: Expose
    @field:SerializedName("minimum_transaction_string") var minimumTransactionString: String = "",
    @field: Expose
    @field:SerializedName("maximum_transaction") var maximumTransaction: Long = 0L,
    @field: Expose
    @field:SerializedName("maximum_transaction_string") var maximumTransactionString: String = "",
    @field: Expose
    @field:SerializedName("reference_number") var referenceNumber: String= "",
    @field: Expose
    @field:SerializedName("is_allowed") var isAllowed: Boolean= false,
    @field: Expose
    @field:SerializedName("invalid_claim") var invalidClaim: DialogKlaimDplkResponse = DialogKlaimDplkResponse()
    )