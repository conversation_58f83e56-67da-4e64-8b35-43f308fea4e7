package id.co.bri.brimons.presenters.lupapassword;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.lupapassword.IKonfirmasiLupaUserPassPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.lupapassword.IKonfirmasiLupaUserPassView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.PreSumRequest;
import id.co.bri.brimons.models.apimodel.response.MagicLupaPassResponse;
import id.co.bri.brimons.models.apimodel.response.forgetuserpass.OtpNoHpRes;
import id.co.bri.brimons.models.apimodel.response.pengelolaankartu.OtpReissueResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class KonfirmasiLupaUserPassPresenter<V extends IMvpView & IKonfirmasiLupaUserPassView>
        extends MvpPresenter<V> implements IKonfirmasiLupaUserPassPresenter<V> {

    private String urlReissue;
    private String urlForget;

    public KonfirmasiLupaUserPassPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                           BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                           TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlReissue(String url) {
        urlReissue = url;
    }

    @Override
    public void setUrlOtpForgetUserPass(String url) {
        urlForget = url;
    }

    @Override
    public void sendOTPReissue() {
        if (urlReissue.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlReissue, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                OtpReissueResponse otpReissueResponse = response.getData(OtpReissueResponse.class);
                                getView().onSuccessOtpReissue(otpReissueResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void sendOtpForgetUserPass(PreSumRequest request) {
        if (urlForget.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlForget, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                OtpNoHpRes otpResponse = response.getData(OtpNoHpRes.class);
                                if (otpResponse.getType().equalsIgnoreCase("otp"))
                                    getView().onSuccessOtpForget(otpResponse);
                                else {
                                    MagicLupaPassResponse magicResponse = response.getData(MagicLupaPassResponse.class);
                                    getView().onSuccessMagicLink(magicResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}