package id.co.bri.brimons.presenters.saldo;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.saldo.IRekeningPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.saldo.IRekeningView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.converter.MapperHelper;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.BiFastAccountResponse;
import id.co.bri.brimons.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SaldoReponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.observers.DisposableObserver;

public class RekeningPresenter<V extends IMvpView & IRekeningView> extends MvpPresenter<V> implements IRekeningPresenter<V> {

    ListRekeningResponse listRekeningResponse = new ListRekeningResponse();
    List<ListRekeningResponse.Account> accountList;

    boolean isLoading = false;
    String urlBifast;

    public RekeningPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getAccountWithSaldo() {
//        getView().showSkeleton();
        if (isLoading)
            return;


        if (isViewAttached()) {
            isLoading = true;
            accountList = new ArrayList<>();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getRekening(seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            isLoading = false;
                            getView().onException(errorMessage);
//                            getView().hideSkeleton();
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            isLoading = false;
                            listRekeningResponse = restResponse.getData(ListRekeningResponse.class);
                            accountList.addAll(listRekeningResponse.getAccount());
                            getView().onListRekening(listRekeningResponse);
//                            getView().hideSkeleton();
//                            GeneralHelper.responseChuck(response);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
//                            getView().hideSkeleton();
                            isLoading = false;
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                getView().onException12(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else getView().onException(restResponse.getDesc());
                        }

                        @Override
                        public void onComplete() {
//                            getView().hideSkeleton();
//                            getSaldo(accountList, false);
                            isLoading = false;
                            getView().enableButton(false);
                        }
                    }));


        }
    }


    @Override
    public void getSaldo(List<ListRekeningResponse.Account> list, boolean isRefreshed) {
        getView().showProgress();
        getCompositeDisposable().add(
                Observable.fromIterable(list)
                        .flatMap((Function<ListRekeningResponse.Account, ObservableSource<ListRekeningResponse.Account>>) account -> getSaldoObservable(account))
                        .subscribeWith(new DisposableObserver<ListRekeningResponse.Account>() {

                                           @Override
                                           public void onNext(ListRekeningResponse.Account account) {
                                               int postion = list.indexOf(account);
                                               if (postion == -1)
                                                   return;
                                               list.set(postion, account);

                                               getView().hideProgress();
                                               getView().onGetSaldo(list, isRefreshed);
                                               getView().enableButton(false);
                                           }

                                           @Override
                                           public void onError(Throwable e) {
                                               getView().hideProgress();
                                               getView().enableButton(true);
                                           }

                                           @Override
                                           public void onComplete() {
                                               getView().onGetSaldoComplete();
                                               isLoading = false;
                                               getView().enableButton(true);
                                           }
                                       }
                        ));

    }

    @Override
    public void setUrlBiFast(String url) {
        urlBifast = url;
    }

    @Override
    public void getListBiFast() {
        if (urlBifast == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        //view.showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getInfoKurs(urlBifast, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                    }

                    @Override
                    public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                    }

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
//                        getView().onErrorBiFast("");
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        BiFastAccountResponse biFastAccountResponse = response.getData(BiFastAccountResponse.class);
//                            getView().onSuccessBiFast(biFastAccountResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
//                        else if (restResponse.getCode().equalsIgnoreCase("12"))
//                            getView().onErrorBiFast(restResponse.getDesc());
//                        else getView().onErrorBiFast(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);

    }

    @Override
    public Observable<ListRekeningResponse.Account> getSaldoObservable(ListRekeningResponse.Account account) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        return getApiSource().getSaldoNormal(account.getAccount(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .map(new Function<String, ListRekeningResponse.Account>() {
                    @Override
                    public ListRekeningResponse.Account apply(String stringResponse) throws Exception {
                        SaldoReponse saldoReponse;
                        RestResponse restResponse = null;

                        //get checksum response
                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        //jika checksum response kosong
                        if (responseCheck.isEmpty()) {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString("TO");
                        }

                        //coba konversi String ke RestResponse model
                        restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);

                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {

                                if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    saldoReponse = new SaldoReponse();
                                    saldoReponse.setBalanceString(restResponse.getDesc());
                                } else {

                                    saldoReponse = restResponse.getData(SaldoReponse.class);

                                    //jika rekening adalah default maka saldo local diupdate
                                    if (account.getDefault() == 1) {
                                        getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                        getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                        getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                        getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                                        getBRImoPrefRepository().saveAccountDefault(saldoReponse.getAccount());
                                        getBRImoPrefRepository().saveSaldoHold(saldoReponse.isOnHold());
                                    }
                                }
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                saldoReponse.setName(restResponse.getDesc());
                                getView().onExceptionTotalSaldo();

                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString("TO");
                            }
                        } else {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString("TO");
                        }
                        account.setSaldoReponse(saldoReponse);
                        return account;
                    }
                });
    }

    @Override
    public void start() {
        super.start();
//        getListBiFast();
        getAccountWithSaldo();
    }

    @Override
    public void stop() {
        if (isViewAttached())
            getView().onGetSaldoComplete();
        super.stop();
    }
}