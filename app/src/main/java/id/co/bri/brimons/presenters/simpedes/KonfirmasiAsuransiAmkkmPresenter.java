package id.co.bri.brimons.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimons.contract.IPresenter.simpedes.IKonfirmasiAsuransiAmkkmPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IKonfirmasiAsuransiAmkkmView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PaymentAmkkmRequest;
import id.co.bri.brimons.models.apimodel.response.ReceiptAmkkmResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiAsuransiAmkkmPresenter<V extends IMvpView & IKonfirmasiAsuransiAmkkmView> extends MvpPresenter<V> implements IKonfirmasiAsuransiAmkkmPresenter<V> {

    private String url;

    @Inject
    public KonfirmasiAsuransiAmkkmPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlKonfirmasiAsuransiAmkkm(String url) {
        this.url = url;
    }

    @Override
    public void getKonfirmasiAsuransiAmkkm(String pin) {
        //ganti method jadi payment amkkm
        getView().showProgress();
        String seq = getBRImoPrefRepository().getSeqNumber();

        PaymentAmkkmRequest paymentAmkkmRequest = new PaymentAmkkmRequest(
                getView().getRefNumber(),
                pin
        );

        getCompositeDisposable().add(getApiSource().getData(url,paymentAmkkmRequest, seq)
        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .subscribeWith(new ApiObserver(getView(),seq) {
            @Override
            protected void onFailureHttp(String type) {
                getView().hideProgress();
                getView().onException(type);
            }

            @Override
            protected void onApiCallSuccess(RestResponse response) {
                getView().hideProgress();
                ReceiptAmkkmResponse receiptAmkkmResponse = response.getData(ReceiptAmkkmResponse.class);
                getView().onGetKonfirmasiAmkkm(receiptAmkkmResponse);
            }

            @Override
            protected void onApiCallError(RestResponse restResponse) {
                getView().hideProgress();
                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                    getView().onSessionEnd(restResponse.getDesc());
                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                    getView().onException12(restResponse.getDesc());
                else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                    getView().onException93(restResponse.getDesc());
                }
                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                    getView().onException99(restResponse.getDesc());
                else
                    getView().onException(restResponse.getDesc());
            }
        }));
    }
}