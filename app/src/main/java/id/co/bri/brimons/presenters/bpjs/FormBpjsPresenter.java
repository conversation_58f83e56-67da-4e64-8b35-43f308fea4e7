package id.co.bri.brimons.presenters.bpjs;

import id.co.bri.brimons.contract.IPresenter.bpjs.IFormBpjsPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.bpjs.IFormBpjsView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryBPJSRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryBPJSTKRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryBpjstkDues;
import id.co.bri.brimons.models.apimodel.response.BpjsTkResponse;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.disposables.CompositeDisposable;

/**
 * Created by ojik on 04/02/2021
 */
public class FormBpjsPresenter<V extends IMvpView & IBaseFormView & IFormBpjsView> extends BaseFormPresenter<V> implements IFormBpjsPresenter<V> {

    public FormBpjsPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    /**
     * Method yang digunakan untuk get Data Inquiry di setiap FORM PRESENTER
     * @param request
     * @param isFromFastmenu
     */
    @Override
    public void getDataInquiry(InquiryBPJSRequest request, boolean isFromFastmenu) {
        if (inquiryUrl == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();

        InquiryBPJSRequest inquiryRequest = new InquiryBPJSRequest(request.getBpjsNumber(), request.getBpjsCode());
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                try {
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                    if (inquiryUrl != null && konfirmasiUrl != null)
                                        getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl, isFromFastmenu);
                                }catch (Exception e){
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryBPJSTK(InquiryBpjstkDues inquiryBpjstkDues, boolean isFromFastmenu) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryBpjstkDues, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                if(response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    try {
                                        BpjsTkResponse bpjsTkResponse = response.getData(BpjsTkResponse.class);
                                        if (inquiryUrl != null && konfirmasiUrl != null)
                                            getView().onSelectMonth(bpjsTkResponse);
                                    }catch (Exception e){
                                    }
                                }
                                else if(response.getCode().equals(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                    try {
                                        GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                        if (inquiryUrl != null && konfirmasiUrl != null)
                                            getView().onSuccessInquiryWithBill(responsebriva, konfirmasiUrl, paymentUrl, isFromFastmenu, true);
                                    }catch (Exception e){
                                    }
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInqiryBill(InquiryBPJSTKRequest inquiryBPJSTKRequest, boolean isFromFastMenu) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryBPJSTKRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                try {
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                    if (inquiryUrl != null && konfirmasiUrl != null)
                                        getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl, isFromFastMenu);
                                }catch (Exception e){

                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }
}