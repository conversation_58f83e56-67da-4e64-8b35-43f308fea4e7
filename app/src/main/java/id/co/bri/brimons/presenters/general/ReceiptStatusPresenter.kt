package id.co.bri.brimons.presenters.general

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.general.IReceiptStatusPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.general.IReceiptStatusView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.ReceiptStatusRequest
import id.co.bri.brimons.models.apimodel.request.dplkrevamp.DetailReceiptKlaimDplkRequest
import id.co.bri.brimons.models.apimodel.request.rdn.RdnReceiptWithdrawRequest
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ReceiptStatusPresenter<V>(schedulerProvider: SchedulerProvider,
                                compositeDisposable: CompositeDisposable,
                                mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource?,
                                categoryPfmSource: CategoryPfmSource,
                                transaksiPfmSource: TransaksiPfmSource,
                                anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IReceiptStatusPresenter<V> where V : IMvpView, V : IReceiptStatusView {

    private var mUrlGetDetailAmbilFisikEmas : String = ""
    private var mUrlGetDetailTransaksi : String = ""
    private var urlPortoReksadana = ""
    private var urlPortoReceipt = ""
    private var urlReceiptKlaimDplk = ""
    private var urlReceiptWdRdn = ""


    override fun setUrlGetDetailTransaction(urlGetDetailTrx: String) {
        mUrlGetDetailTransaksi = urlGetDetailTrx
    }



    override fun setUrlReceipt(urlReceipt: String) {
        urlPortoReceipt = urlReceipt
    }

    override fun setUrlReceiptDetailKlaimDplk(url: String) {
        urlReceiptKlaimDplk = url
    }

    override fun setUrlReceiptWdRdn(url: String) {
        urlReceiptWdRdn = url
    }


    override fun getReceiptDetail(request: ReceiptStatusRequest) {
        if (urlPortoReceipt == null || !isViewAttached) {
            return
        }

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlPortoReceipt, request, seq) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seq) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val response = response.getData(ReceiptRevampResponse::class.java)
                        getView().onSuccessGetPaymentRevamp(response)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        when(restResponse.restResponse.code){
                            Constant.RE12 -> getView().onExceptionNoBackAction(restResponse.desc)
                            else -> onApiError(restResponse)
                        }
                    }
                })
        )
    }

    override fun getReceiptDetailKlaimDplk(request: DetailReceiptKlaimDplkRequest) {
        if (urlReceiptKlaimDplk == null || !isViewAttached) {
            return
        }
        view.showProgress()

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlReceiptKlaimDplk, request, seq) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seq) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val response = response.getData(ReceiptRevampResponse::class.java)
                        getView().onSuccessGetPaymentRevamp(response)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        when(restResponse.restResponse.code){
                            Constant.RE12 -> getView().onExceptionNoBackAction(restResponse.desc)
                            else -> onApiError(restResponse)
                        }
                    }
                })
        )
    }

    override fun getReceiptWdRdn(request: RdnReceiptWithdrawRequest) {
        if (!isViewAttached) {
            return
        }
        view.showProgress()

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlReceiptWdRdn, request, seq)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(
                    object : ApiObserver(view, seq) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val responseData = response.getData(ReceiptRevampResponse::class.java)
                            getView().onSuccessGetPaymentRevamp(responseData)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.restResponse.code) {
                                Constant.RE12 -> getView().onExceptionNoBackAction(restResponse.desc)
                                else -> onApiError(restResponse)
                            }
                        }
                    }
                )
        )
    }


}