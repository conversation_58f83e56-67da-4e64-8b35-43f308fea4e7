package id.co.bri.brimons.presenters.esbn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.esbn.IDetailPembelianSbnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.esbn.IDetailPembelianSbnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.DataProcedureSbnResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class DetailPembelianSbnPresenter<V extends IMvpView & IDetailPembelianSbnView> extends MvpPresenter<V> implements IDetailPembelianSbnPresenter<V> {
    private String url_tata_cara;

    public DetailPembelianSbnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void start() {
        super.start();
    }

    @Override
    public void stop() {
        super.stop();
    }

    @Override
    public void setUrlGetProcedure(String urlGetProcedure) {
        this.url_tata_cara=urlGetProcedure;
    }

    @Override
    public void getProcedure() {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_tata_cara, "", seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                DataProcedureSbnResponse dataResponse = response.getData(DataProcedureSbnResponse.class);
                                getView().onSuccesGetProcedure(dataResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                    getView().onSessionEnd(restResponse.getDesc());
                                }else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }
                        })
        );
    }
}