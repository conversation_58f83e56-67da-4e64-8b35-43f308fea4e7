package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IEditSetoranEmasPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IEditSetoranEmasView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.emas.EditSetoranUpdateRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class EditSetoranEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                  compositeDisposable: CompositeDisposable?,
                                  mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                  categoryPfmSource: CategoryPfmSource?,
                                  transaksiPfmSource: TransaksiPfmSource?,
                                  anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IEditSetoranEmasPresenter<V> where V : IMvpView?, V : IEditSetoranEmasView {

    var urlEditSetoran : String? = null

    override fun setUrlEditUpdate(url : String) {
        urlEditSetoran=url
    }

    override fun getUpdateEditSetoran(request: EditSetoranUpdateRequest) {
        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(apiSource.getData(urlEditSetoran, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()!!.hideProgress()
                                getView()!!.onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                //TO-DO onSuccess
                                getView()!!.hideProgress()
                                getView().onSuccessUpdateSetoran()
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView()!!.hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                        restResponse.desc
                                )
                            }
                        })
                )
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }

    fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText: String = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc: String = brImoPrefRepository.accountDefault
        val saldoString: String = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold: Boolean = brImoPrefRepository.saldoHold
        view!!.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }
}