package id.co.bri.brimons.presenters;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.IBayarPinjamanPresenter;
import id.co.bri.brimons.contract.IPresenter.IBayarPinjamanView;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryPinjamanRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class BayarPinjamanPresenter <V extends IMvpView & IBayarPinjamanView> extends MvpPresenter<V> implements IBayarPinjamanPresenter<V> {
    private static final String TAG = "BayarPinjamanPresenter";
    protected String inquiryUrl;

    public BayarPinjamanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataInquiry(InquiryPinjamanRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                Log.d(TAG+"failureHttp", errorMessage);
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                Log.d(TAG+"success", response.toString());
                                getView().hideProgress();
                                GeneralInquiryResponse responsePinjaman = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiry(responsePinjaman);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                Log.d(TAG+"apicallError", restResponse.toString());
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }
}