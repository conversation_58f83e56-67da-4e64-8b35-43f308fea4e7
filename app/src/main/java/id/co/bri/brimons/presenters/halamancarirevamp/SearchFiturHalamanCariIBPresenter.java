package id.co.bri.brimons.presenters.halamancarirevamp;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.R;
import id.co.bri.brimons.contract.IPresenter.halamancarirevamp.ISearchFiturHalamanCariIBPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.halamancarirevamp.ISearchFiturHalamanCariIBView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.lifestyle.MenuLifestyleSource;
import id.co.bri.brimons.data.repository.menudashboard.MenuDashboardSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.config.MenuConfig;
import id.co.bri.brimons.domain.converter.MapperHelper;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.LifestyleHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.nfcpayment.GeneratePayloadNfcRequest;
import id.co.bri.brimons.models.apimodel.request.WebviewBrilifeRequest;
import id.co.bri.brimons.models.apimodel.request.splitbill.ListSplitBillRequest;
import id.co.bri.brimons.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimons.models.apimodel.request.RevokeSessionRequest;
import id.co.bri.brimons.models.apimodel.response.ChatBankingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse;
import id.co.bri.brimons.models.apimodel.response.lifestyle.FeatureDataView;
import id.co.bri.brimons.models.apimodel.response.splitbill.SplitBillHistoryResponse;
import id.co.bri.brimons.models.apimodel.response.splitbill.viewentity.BillEntity;
import id.co.bri.brimons.models.daomodel.lifestyle.MenuLifestyle;
import id.co.bri.brimons.models.apimodel.response.nfcpayment.NfcPayloadResponse;
import id.co.bri.brimons.models.apimodel.request.WebviewBrilifeRequest;
import id.co.bri.brimons.models.apimodel.response.voip.CategoryVoipRes;
import id.co.bri.brimons.models.daomodel.DashboardMenu.MenuDashboard;
import id.co.bri.brimons.models.apimodel.response.nfcpayment.NfcPayloadResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.observers.DisposableMaybeObserver;
import io.reactivex.schedulers.Schedulers;

/*
 * Created by Muhamad Ikhsan Laisa on 31/10/23
 * Copyright (c) 2023
 */
public class SearchFiturHalamanCariIBPresenter<V extends IMvpView & ISearchFiturHalamanCariIBView> extends MvpPresenter<V> implements ISearchFiturHalamanCariIBPresenter<V> {

    private MenuDashboardSource menuDashboardSource;
    private MenuLifestyleSource menuLifestyleSource;
    private DashboardLifestyleMenuResponse lifestyleMenuResponse;
    public String urlValidateMiniApp = "";
    protected String urlChatBanking;
    private static final String TAG = "SearchFiturHalamanCariIBPresenter";
    protected String urlVoip;
    private String urlRevoke;
    private String sessionRevoke;
    private String mUrlSplit;

    private RevokeSessionRequest reqRevoke;


    public SearchFiturHalamanCariIBPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                             BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                             TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource,
                                             MenuDashboardSource menuDashboardSource, MenuLifestyleSource menuLifestyleSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.menuDashboardSource = menuDashboardSource;
        this.menuLifestyleSource = menuLifestyleSource;
    }

    private String urlGetPayload = "";

    @Override
    public void getDataSearch() {
        getCompositeDisposable().add(menuDashboardSource.getAllMenuDashboard()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(menuDashAlls -> {
                    if (menuDashAlls != null) {
                        for(int i = 0; i < menuDashAlls.size(); i++) {
                            if (menuDashAlls.get(i).getId() == MenuConfig.MenuId.MENU_LTMPT){
                                menuDashAlls.remove(menuDashAlls.get(i));
                            }
                        }
                        getView().onGetDataSearchSuccess(menuDashAlls);
                    }
                }, throwable -> {
                    String stringBio = String.format(
                            GeneralHelper.getString(R.string.login_fingerprint),
                            getBRImoPrefRepository().getBiometricType()
                    );
                    initiateMenuDashboard(MenuConfig.onCheckBiometic(
                            MenuConfig.fetchMenuDashboardAll(), stringBio
                    ));
                }));
    }


    private void initiateMenuDashboard(List<MenuDashboard> menuDashboardList) {
        getCompositeDisposable().add(menuDashboardSource.insertMenuDashboard(menuDashboardList)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDBMenuRevamp(true);
                    }

                    @Override
                    public void onError(Throwable e) {
                        getBRImoPrefRepository().saveDBMenuRevamp(false);
                    }
                })
        );

    }


    @SuppressWarnings("unchecked")
    @Override
    public void getDataDashboardLifestyleMenu(String url) {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getDataTanpaRequest(url, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            lifestyleMenuResponse =
                                    response.getData(DashboardLifestyleMenuResponse.class);

                            String parentfeatureCode = "";
                            String subfeatureCode = "";
                            String updateParentFeature = "";
                            String updateSubFeature = "";
                            boolean parentIsNew = false;
                            boolean subIsNew = false;

                            List<FeatureDataView> featureDataLocals = new ArrayList<>();
                            for (int menuData = 0; menuData < lifestyleMenuResponse.getMenuDataView().size();
                                 menuData++) {

                                featureDataLocals.addAll(
                                        lifestyleMenuResponse.getMenuDataView().get(menuData).getFeature());

                                for (int parentMenu = 0; parentMenu < lifestyleMenuResponse.getMenuDataView()
                                        .get(menuData).getFeature().size(); parentMenu++) {

                                    parentfeatureCode = lifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getFeatureCode();
                                    parentIsNew = lifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).isNew();
                                    updateParentFeature = lifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getUpdatedDate();

                                    //to check if parent menu not have submenu and isNew true then set isNew to false
                                    checkMenuLifestyle(parentfeatureCode);

                                    if (lifestyleMenuResponse.getMenuDataView().get(menuData)
                                            .getFeature().get(parentMenu).getSubFeature() != null &&
                                            !lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu).getSubFeature().isEmpty()
                                    ) {

                                        for (int subMenu = 0; subMenu < lifestyleMenuResponse.getMenuDataView()
                                                .get(menuData).getFeature().get(parentMenu).getSubFeature().size(); subMenu++) {

                                            if (subMenu != 0) {
                                                featureDataLocals.addAll(
                                                        lifestyleMenuResponse.getMenuDataView()
                                                                .get(menuData).getFeature().get(parentMenu).getSubFeature()
                                                );
                                            }

                                            updateSubFeature = lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).getUpdatedDate();

                                            subfeatureCode = lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).getFeatureCode();

                                            subIsNew = lifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).isNew();
                                        }
                                    }

                                }
                            }

                            if (response.getCode().equals("00")) {
                                if (Objects.equals(getBRImoPrefRepository().getDateMenuUpdate(), "")) {
                                    saveMenuLifestyle(featureDataLocals);
                                } else if (Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(
                                                lifestyleMenuResponse.getUpdatedDate()))) {
                                    saveMenuLifestyle(featureDataLocals);
                                } else if (Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(updateParentFeature))) {
                                    updateFlagNewLifestyle(parentfeatureCode, parentIsNew ? 1 : 0);
                                } else if (updateSubFeature != null &&
                                        !updateSubFeature.isEmpty() &&
                                        Boolean.TRUE == CalendarHelper.compareDate(
                                                getBRImoPrefRepository().getDateMenuUpdate(),
                                                CalendarHelper.stringDateTimeToddMMyyyy(updateSubFeature))) {
                                    updateFlagNewLifestyle(subfeatureCode, subIsNew ? 1 : 0);
                                } else {
                                    getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                                }
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });
            getCompositeDisposable().add(disposable);
        }
    }

    private void saveMenuLifestyle(List<FeatureDataView> featureDataViews) {
        getCompositeDisposable().add(menuLifestyleSource
                .insertMenuLifestyle(MapperHelper.dashboardLifestyleModelConverter(featureDataViews))
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        lifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    private void updateFlagNewLifestyle(String featureCode, int isNew) {
        getCompositeDisposable().add(menuLifestyleSource.
                updateMenuLifestyle(featureCode, isNew)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        lifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                })
        );
    }

    public void checkMenuLifestyle(String parentfeatureCode) {
        if (isViewAttached()) {
            getCompositeDisposable().add(menuLifestyleSource.getNewMenubyParentCode(parentfeatureCode)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableMaybeObserver<List<MenuLifestyle>>() {
                        @Override
                        public void onSuccess(List<MenuLifestyle> menuLifestyles) {
                            if (menuLifestyles.size() == 1) {
                                updateFlagNewLifestyle(parentfeatureCode, 0);
                            }
                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    }));
        }
    }

    @Override
    public void setUrlValidateMiniApp(@NonNull String url) {
        urlValidateMiniApp = url;
    }

    @Override
    public void validateMiniProgram(@NonNull String miniAppId) {
        if (!isViewAttached()) return;
        mView.showProgress();

        WebviewBrilifeRequest request = new WebviewBrilifeRequest(miniAppId);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(urlValidateMiniApp, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                mView.hideProgress();
                                mView.onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                mView.hideProgress();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                mView.hideProgress();
                            }
                        })
                );
    }

    @Override
    public void setUrlChatBanking(String urlChatBanking) {
        this.urlChatBanking = urlChatBanking;
    }

    @Override
    public void onGetChatBanking() {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlChatBanking, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ChatBankingResponse chatBankingResponse = response.getData(ChatBankingResponse.class);
                                    getView().onSuccessChatBanking(chatBankingResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().onException12(restResponse.getDesc());

                                }
                            })
            );
        }
    }

    @Override
    public void setUrlVoip(String url) {
        this.urlVoip = url;
    }

    @Override
    public void getVoip() {
        if (urlVoip == null && !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(urlVoip, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                CategoryVoipRes categoryVoipRes = response.getData(CategoryVoipRes.class);
                                getView().onSuccessGetListVoip(categoryVoipRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void setRevokeSession(String sessionRevoke) {
        this.sessionRevoke = sessionRevoke;
    }

    @Override
    public void setUrlRevoke(String urlRevoke) {
        this.urlRevoke = urlRevoke;
    }

    @Override
    public void revokeSession() {
        if (urlRevoke == null || !isViewAttached())
            return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        reqRevoke = new RevokeSessionRequest(getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey(), sessionRevoke);

        getCompositeDisposable().add(
                getApiSource().getData(urlRevoke, reqRevoke, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getDataPayloadNfc(String pin) {
        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        GeneratePayloadNfcRequest request = new GeneratePayloadNfcRequest("", "", pin, "", "", "");

        getCompositeDisposable().add(
                getApiSource().getData(urlGetPayload, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                NfcPayloadResponse data = response.getData(NfcPayloadResponse.class);
                                getView().onSuccessGetNfcPayload(data);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(Constant.RE_FITUR_OFF)) {
                                    EmptyStateResponse response = restResponse.getData(EmptyStateResponse.class);
                                    getView().onExceptionFO(response);
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    @Override
    public void setUrlGetDataPayload(String url) {
        this.urlGetPayload = url;
    }

    @Override
    public void setUrlSplitBill(String urlSplitBill) {
        mUrlSplit = urlSplitBill;
    }

    @Override
    public void getSplitBill() {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(mUrlSplit,
                            new ListSplitBillRequest(),
                            seqNum
                    )
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().single())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            SplitBillHistoryResponse billListResponse =
                                    response.getData(SplitBillHistoryResponse.class);

                            List<BillEntity> bills = LifestyleHelper.Companion.mapToBillEntity(billListResponse);

                            if (bills != null) {
                                getView().onSuccessGetSplitBillHistory(bills);
                            } else {
                                if (getBRImoPrefRepository().isBillCreated()) {
                                    getView().onSuccessGetSplitBillHistory(bills);
                                } else {
                                    getView().onSuccessGetSplitBillOnboard();
                                }
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

}