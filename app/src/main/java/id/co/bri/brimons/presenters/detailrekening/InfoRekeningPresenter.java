package id.co.bri.brimons.presenters.detailrekening;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.detailrekening.IInfoRekeningPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.detailrekening.IInfoRekeningView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.DefaultRekeningRequest;
import id.co.bri.brimons.models.apimodel.request.IdRequest;
import id.co.bri.brimons.models.apimodel.request.PinFinansialRequest;
import id.co.bri.brimons.models.apimodel.request.notificationsetting.GetListNotificationSettingRequest;
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimons.models.apimodel.request.StatusKartuRequest;
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimons.models.apimodel.response.BiFastAccountResponse;
import id.co.bri.brimons.models.apimodel.response.notificationsetting.NotificationSettingResponse;
import id.co.bri.brimons.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimons.models.apimodel.response.EnableKartuResponse;
import id.co.bri.brimons.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimons.models.apimodel.response.QuestionResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.ForceUpdateResponse;
import id.co.bri.brimons.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class InfoRekeningPresenter<V extends IMvpView & IInfoRekeningView>
        extends MvpPresenter<V> implements IInfoRekeningPresenter<V> {

    private String urlStatus;
    private String url;
    private String urlBifast;
    private String urlFinansialRek;
    protected String urlInfoSaldoHold;
    protected String urlDetail;
    private String urlGetListNotification = "";

    public InfoRekeningPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlStatus(String urlStatus) {
        this.urlStatus = urlStatus;
    }

    @Override
    public void setUrlDefault(String urlDefault) {
        this.url = urlDefault;
    }

    @Override
    public void setUrlFinansialRek(String urlFinansialRek) {
        this.urlFinansialRek = urlFinansialRek;
    }

    @Override
    public void setUrlDetailStatus(String urlDetail) {
        this.urlDetail = urlDetail;
    }

    @Override
    public void getStatusKartu(String accNumb) {
        if (urlStatus == null || !isViewAttached()) {
            return;
        }

        StatusKartuRequest statusKartuRequest = new StatusKartuRequest(accNumb);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlStatus, statusKartuRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    EnableKartuResponse enableKartuResponse = response.getData(EnableKartuResponse.class);
                                    getView().onResp00(enableKartuResponse);
                                } else
                                    getView().onException01(response);

                                GeneralHelper.responseChuck(response);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                    getView().onException01(restResponse);
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                                GeneralHelper.responseChuck(restResponse);

                            }
                        })
        );
    }

    @Override
    public void setChangeDefault(String accNumb) {
        if (url == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        DefaultRekeningRequest defaultRekeningRequest = new DefaultRekeningRequest(accNumb);

        getCompositeDisposable().add(
                getApiSource().getData(url, defaultRekeningRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getBRImoPrefRepository().saveAccountDefault(accNumb);
                                getView().onSuccessGetChangeDefault(response.getDesc());
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc(), Constant.REQUEST_REKENING_UTAMA);
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setUrlBiFast(String url) {
        this.urlBifast = url;
    }

    @Override
    public void getListBiFast() {
        if (urlBifast == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        //view.showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataForm(urlBifast, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                    }

                    @Override
                    public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                    }

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
//                        getView().onErrorBiFast("");
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        BiFastAccountResponse biFastAccountResponse = response.getData(BiFastAccountResponse.class);
                        getView().onSuccessBiFast(biFastAccountResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                            getView().onErrorBiFast(restResponse.getDesc());
                        else getView().onErrorBiFast(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);

    }

    @Override
    public void getDataDetailStatus(DetailKelolaKartuReq detailKelolaKartuReq) {
        if (urlDetail != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(urlDetail, detailKelolaKartuReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            DetailKelolaKartuRes detailResponse = response.getData(DetailKelolaKartuRes.class);
                            getView().onGetSuccessDetail(detailResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });
            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void setUrlInfoSaldoHold(String urlInfoSaldoHold) {
        this.urlInfoSaldoHold = urlInfoSaldoHold;
    }

    @Override
    public void getInfoSaldoHold() {
        if (urlInfoSaldoHold != null || isViewAttached()) {
            getView().showProgress();

            IdRequest idRequest = new IdRequest("105");
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInfoSaldoHold, idRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    //do nothing
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    QuestionResponse questionResponse = response.getData(QuestionResponse.class);
                                    getView().onSuccessInfoSaldoHold(questionResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void sendFinansialRek(PinFinansialRequest pinRequest, int statusFinansial) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlFinansialRek, pinRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    ForceUpdateResponse forceUpdateRes = response.getData(ForceUpdateResponse.class);
                                    getView().onUpdateVersion(forceUpdateRes);
                                } else {
                                    if (statusFinansial == 0) {
                                        GeneralOtpResponse generalOtpResponse = response.getData(GeneralOtpResponse.class);
                                        getView().onSuccessGetFinansial(generalOtpResponse);
                                    } else {
                                        getView().onSuccessNonaktifFinansial();
                                    }
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    if (restResponse.getDesc().contains("HSAO")) {
                                        getView().onSuccessNonaktifFinansial();
                                    } else {
                                        getView().onException12(restResponse.getDesc(), Constant.REQUEST_STATUS_FINANSIAL);
                                    }
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setUrlGetListNotification(String url) {
        this.urlGetListNotification = url;
    }

    @Override
    public void getListNotificationSetting(GetListNotificationSettingRequest request) {
        if (isViewAttached()) {
//            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getData(urlGetListNotification, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
//                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
//                                    getView().hideProgress();
                                    NotificationSettingResponse notificationSettingResponse = response.getData(NotificationSettingResponse.class);
                                    getView().onSuccessGetListNotification(notificationSettingResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
//                                    getView().hideProgress();
                                    getView().onException12(restResponse.getDesc(), Constant.REQUEST_NOTIFIKASI_TRANSAKSI);

                                }
                            })
            );
        }
    }

    @Override
    public void start() {
        super.start();
//        getListBiFast();
//        getAccountWithSaldo();
    }
}