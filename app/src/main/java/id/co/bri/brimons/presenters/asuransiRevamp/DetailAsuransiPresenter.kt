package id.co.bri.brimons.presenters.asuransiRevamp

import id.co.bri.brimons.contract.IPresenter.asuransiRevamp.IDetailAsuransiPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.asuransiRevamp.IDetailAsuransiView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.asuransirevamp.DetailInsuranceRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.asuransi.FormAsuransiResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DetailAsuransiPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDetailAsuransiPresenter<V> where V : IMvpView, V : IDetailAsuransiView  {


    private lateinit var mUrlDetailInsurance: String
    override fun setUrlFormAsuransi(url: String) {
        mUrlDetailInsurance = url
    }

    override fun getDataFormAsuransi(request: DetailInsuranceRequest) {
        if (isViewAttached) {
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlDetailInsurance, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()!!.hideProgress()
                            val formAsuransiResponse = response.getData(
                                FormAsuransiResponse::class.java
                            )
                            getView()!!.onSuccesFormAsuransi(formAsuransiResponse)


                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }


}