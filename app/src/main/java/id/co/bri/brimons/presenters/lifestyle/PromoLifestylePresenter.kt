package id.co.bri.brimons.presenters.lifestyle

import id.co.bri.brimons.contract.IPresenter.lifestyle.IPromoLifestylePresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.lifestyle.IPromoLifestyleView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.DetailPromoRequest
import id.co.bri.brimons.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimons.models.apimodel.response.*
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class PromoLifestylePresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    IPromoLifestylePresenter<V> where V : IMvpView?, V : IPromoLifestyleView? {

    lateinit var mUrlDetailPromo: String
    private lateinit var mRequest: Any
    private var mUrlFormBus = ""
    private var mUrlKai = ""
    private var mUrlWebviewTugu = ""

    override fun setUrlDetailPromo(urlPromo: String) {
        mUrlDetailPromo = urlPromo
    }

    override fun getDetailPromoItem(detailPromoResponse: DetailPromoResponse) {
        if (!isViewAttached) return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        mRequest = DetailPromoRequest(detailPromoResponse.id.toString())
        compositeDisposable.add(
            apiSource.getData(mUrlDetailPromo, mRequest, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val promoResponse = response.getData(PromoResponse::class.java)
                        getView()!!.onSuccessGetDetailPromo(promoResponse, detailPromoResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun setUrlBusShuttle(urlFormBus: String) {
        mUrlFormBus = urlFormBus
    }

    override fun setUrlKai(urlFormKai: String) {
        mUrlKai = urlFormKai
    }

    override fun setUrlWebviewTugu(urlTugu: String) {
        mUrlWebviewTugu = urlTugu
    }

    override fun getFormBus() {
        if (!isViewAttached) return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataForm(mUrlFormBus, seqNum).subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val cityFormResponse = response.getData(CityFormResponse::class.java)
                        getView()!!.onSuccessGetFormBus(cityFormResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getFormKai(titleBar: String, appType: Int) {
        if (!isViewAttached) return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(mUrlKai, seqNum).subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val urlWebViewResponse = response.getData(
                            UrlWebViewResponse::class.java
                        )
                        getView()!!.onSuccessGetFormKai(urlWebViewResponse, titleBar, appType)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException99(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getWebViewTugu(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    ) {
        if (!isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(mUrlWebviewTugu, partnerIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io()).observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val webviewResponse = response.getData(
                            GeneralWebviewResponse::class.java
                        )
                        getView()?.onSuccessGetWebviewTugu(
                            webviewResponse,
                            titleWebview,
                            codeMenu,
                            appType
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals("93", ignoreCase = true)) {
                            getView()?.onExceptionTrxExpired(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun getIndihomeRegistrationData(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    ) {
        if (brImoPrefRepository.isIndihomeFirstClick) {
            getWebViewTugu(
                PartnerIdRequest(partnerIdRequest?.partnerId), titleWebview, codeMenu, appType
            )
        } else {
            view?.showIndihomeConfirmation(
                PartnerIdRequest(partnerIdRequest?.partnerId), titleWebview, codeMenu,
                appType
            )
        }
    }

    override fun confirmIndihomeRegistration(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    ) {
        brImoPrefRepository.saveIndihomeFirstClick(true)
        getWebViewTugu(
            PartnerIdRequest(partnerIdRequest?.partnerId), titleWebview, codeMenu, appType
        )
    }
}