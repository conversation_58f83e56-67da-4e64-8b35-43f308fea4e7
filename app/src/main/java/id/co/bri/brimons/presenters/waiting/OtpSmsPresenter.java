package id.co.bri.brimons.presenters.waiting;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.waiting.IOtpSmsPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.waiting.IOtpSmsView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;
import id.co.bri.brimons.models.apimodel.response.AktivasiResponse;
import id.co.bri.brimons.models.apimodel.response.LoginResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class OtpSmsPresenter<V extends IMvpView & IOtpSmsView> extends MvpPresenter<V> implements IOtpSmsPresenter<V> {
    public OtpSmsPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                           BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                           TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    protected String urlValidate;
    protected String urlResend;

    @Override
    public void setUrlValidate(String url) {
        urlValidate = url;
    }

    @Override
    public void setUrlResend(String url) {
        urlResend = url;
    }

    @Override
    public void resendOtp(ResendOtpReissueReq resendOtpReissueReq) {
        if (urlResend.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlResend, resendOtpReissueReq, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                LoginResponse otpResponse = response.getData(LoginResponse.class);
                                getView().onSuccessResendOtp(otpResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );

    }

    @Override
    public void validateOtp(ValidateOtpUserPassReq request) {
        if (urlValidate.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlValidate, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getBRImoPrefRepository().saveUserType(Constant.IB_TYPE);

                                AktivasiResponse response2 = restResponse.getData(AktivasiResponse.class);
                                getBRImoPrefRepository().deteleSavedMenu();

                                switch (response.getCode()) {
                                    case "02":
                                        getView().onEditUsername(response.getDesc());
                                        break;
                                    case "01":
                                        getBRImoPrefRepository().saveUsername(response2.getUsername());
                                        getView().onCreatePin();
                                        break;
                                    case "00":
                                        getBRImoPrefRepository().saveUsername(response2.getUsername());
                                        //update flag login
                                        updateLoginFlag(true);
                                        //goto view change device
                                        getView().onSuccessValidateOtp();
                                        break;
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().deleteInputOtp();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}