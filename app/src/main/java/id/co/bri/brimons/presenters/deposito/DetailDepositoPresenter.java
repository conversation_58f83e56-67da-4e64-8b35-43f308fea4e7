package id.co.bri.brimons.presenters.deposito;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.deposito.IDetailDepositoPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.deposito.IDetailDepositoView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.RefnumRequest;
import id.co.bri.brimons.models.apimodel.request.RequestRenewalDeposito;
import id.co.bri.brimons.models.apimodel.response.ConfirmationPenutupanDepositoResponse;
import id.co.bri.brimons.models.apimodel.response.InquiryPenutupanDepositoResponse;
import id.co.bri.brimons.models.apimodel.response.RenewalDepositoResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailDepositoPresenter <V extends IMvpView & IDetailDepositoView> extends MvpPresenter<V> implements IDetailDepositoPresenter<V> {

    private String url;
    private String urlInquiryPenutupan;
    private String urlConfirmationPenutupan;

    public DetailDepositoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlRenewalDeposito(String urlDeposito) {
        this.url = urlDeposito;
    }

    @Override
    public void setUrlInquiryPenutupanDeposito(String urlInquiryPenutupanDeposito) {
        this.urlInquiryPenutupan = urlInquiryPenutupanDeposito;
    }

    @Override
    public void setUrlConfirmationPenutupanDeposito(String urlConfirmationPenutupanDeposito) {
        this.urlConfirmationPenutupan = urlConfirmationPenutupanDeposito;
    }

    @Override
    public void getDataRenewalDeposito(String account) {
        if (isViewAttached()) {
            //initiate param with getter from view

            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            RequestRenewalDeposito request = new RequestRenewalDeposito(account);

            getCompositeDisposable()
                    .add(getApiSource().getData(url, request,seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    RenewalDepositoResponse renewalDepositoResponse = response.getData(RenewalDepositoResponse.class);
                                    getView().getDataRenewalDeposito(renewalDepositoResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getInquiryPenutupanDeposito(String account) {
        if (isViewAttached()) {
            //initiate param with getter from view

            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            RequestRenewalDeposito request = new RequestRenewalDeposito(account);

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInquiryPenutupan, request,seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InquiryPenutupanDepositoResponse inquiryPenutupanDepositoResponse = response.getData(InquiryPenutupanDepositoResponse.class);
                                    getView().getDataInquiryPenutupanDeposito(inquiryPenutupanDepositoResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getConfirmationPenutupanDeposito(String refnum) {
        if (isViewAttached()) {
            //initiate param with getter from view

            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            RefnumRequest request = new RefnumRequest(refnum);

            getCompositeDisposable()
                    .add(getApiSource().getData(urlConfirmationPenutupan, request,seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ConfirmationPenutupanDepositoResponse confirmationPenutupanDepositoResponse = response.getData(ConfirmationPenutupanDepositoResponse.class);
                                    getView().getDataConfirmationPenutupanDeposito(confirmationPenutupanDepositoResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }
}