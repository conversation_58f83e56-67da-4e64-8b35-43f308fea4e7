package id.co.bri.brimons.presenters.splitbill

import id.co.bri.brimons.contract.IPresenter.splitbill.ISplitBillDetailPerMemberPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.splitbill.ISplitBillDetailPerMemberView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.splitbill.MarkPaymentStatusRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.splitbill.MarkPaymentStatusResponse
import id.co.bri.brimons.models.splitbill.SplitBillAddMemberItemViewModel
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class SplitBillDetailPerMemberPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    ISplitBillDetailPerMemberPresenter<V> where V : IMvpView?, V : ISplitBillDetailPerMemberView? {

    private var mIsCreator = true
    private var mIsOtherParticipant = false
    private var memberModel = SplitBillAddMemberItemViewModel()
    private var mUrlMarkPayment = ""
    private var mBillId: Int = 0

    override fun setUrlMarkPaymentStatus(urlMarkPay: String) {
        mUrlMarkPayment = urlMarkPay
    }

    override fun fetchData(
        billId: Int,
        memberItemViewModel: SplitBillAddMemberItemViewModel,
        isCreator: Boolean,
        isOtherParticipant: Boolean
    ) {
        mBillId = billId
        mIsCreator = isCreator
        mIsOtherParticipant = isOtherParticipant

        memberModel = memberItemViewModel

        view?.apply {
            showSplitBillData(memberModel)
            shouldShowButtonPaidUnPaid(memberModel.isPaid)
        }
    }

    override fun updateMemberPaymentStatus(
        markPaymentStatusResponse: MarkPaymentStatusResponse,
        isPaidOff: Boolean
    ) {
        memberModel.isPaid = isPaidOff

        view?.backAndSetPaymentStatusActivityResult(
            memberModel,
            markPaymentStatusResponse
        )
    }

    override fun getDataMarkPaymentStatus(mIsPaidOff: Boolean) {
        if (mUrlMarkPayment.isEmpty() || !isViewAttached) {
            return
        }

        if (view != null) {

            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable = apiSource.getData(mUrlMarkPayment,
                MarkPaymentStatusRequest(
                    billId = mBillId,
                    status = if (mIsPaidOff) "paid" else "unpaid",
                    target = "one",
                    amount = memberModel.totalAmountPay.toInt().toLong(),
                    qrId = memberModel.qrId.toInt()
                ),
                seqNum
            )
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        val markPaymentResponse = response.getData(
                            MarkPaymentStatusResponse::class.java
                        )

                        getView()?.onSuccessGetMarkPaymentStatus(markPaymentResponse, mIsPaidOff)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view?.hideProgress()
                        getView()?.onException(restResponse.desc)
                    }
                })
            compositeDisposable.add(disposable)
        }
    }

}
