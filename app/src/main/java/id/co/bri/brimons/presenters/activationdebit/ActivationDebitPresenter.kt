package id.co.bri.brimons.presenters.activationdebit

import id.co.bri.brimons.contract.IPresenter.activationdebit.IActivationDebitPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.activationdebit.IActivationDebitView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.activationdebit.request.ValidateActivationDebitRequest
import id.co.bri.brimons.models.activationdebit.response.ActivationDebitResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.util.toRestResponseCodeEnum
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class ActivationDebitPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IActivationDebitPresenter<V> where V : IMvpView, V : IActivationDebitView {

    override fun getActivationDebit(
        urlActivationDebit: String,
        validateActivationDebitRequest: ValidateActivationDebitRequest?
    ) {
        view.getDataWithOrWithoutRequest(
            urlActivationDebit,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            validateActivationDebitRequest,
            onApiCallError = { restResponse ->
                val responseCode = restResponse.code
                val responseMessage = restResponse.desc
                when (responseCode.toRestResponseCodeEnum()) {
                    RestResponse.ResponseCodeEnum.RC_IC,
                    RestResponse.ResponseCodeEnum.RC_NB,
                    RestResponse.ResponseCodeEnum.MAX_ATTEMPT_ACTIVATION -> view.onErrorActivationDebit(responseCode, responseMessage)
                    else -> view.onException(responseMessage)
                }
            }
        ) {
            view.onSuccessActivationDebit(it.getData(ActivationDebitResponse::class.java))
        }
    }
}