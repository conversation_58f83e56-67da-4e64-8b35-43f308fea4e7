package id.co.bri.brimons.presenters.transfer;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.transfer.IKonfirmasiTransferPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transfer.IKonfirmasiTransferView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.PurchaseResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;

/**
 * Created by FNS
 */

public class KonfirmasiTransferActivityPresenter<V extends IMvpView & IKonfirmasiTransferView> extends MvpPresenter<V> implements IKonfirmasiTransferPresenter<V> {

    private static final String TAG = "KonfirmasiTransferActiv";

    Transaksi newTransaksi = null;

    public KonfirmasiTransferActivityPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getPaidTransfer(String refNumb, String pin, String pfmCategory, boolean immediately, int amount,String bankDestination, String accountDestination, String note) {
        if (getView() != null) {
            //initiate param with getter from view
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getDataKonfirmasi(refNumb, pin, pfmCategory, note,seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (immediately) {
                                //sesama BRI
                                PendingResponse transferPaidResponse = response.getData(PendingResponse.class);
                                newTransaksi = generateTransaksiModel(pfmCategory, amount, refNumb, bankDestination, accountDestination);
                                saveTransaksiPfm(newTransaksi);
                                getView().onPaidTransfer(transferPaidResponse);
                            } else {
                                //antar bank
                                PurchaseResponse transResponse = response.getData(PurchaseResponse.class);
                                getView().onPendingTransfer(transResponse,pfmCategory, amount, refNumb,  accountDestination, bankDestination);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public Transaksi generateTransaksiModel(String kategoriId, int amount, String refnum, String bankName, String bankDestination) {
        Transaksi transaksi = null;
        try{
            transaksi = new Transaksi(
                    Long.valueOf(kategoriId),
                    1,
                    bankName+"-"+bankDestination,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(refnum),
                    0
            );
        }catch (Exception e){
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();

             */
        }

        return transaksi;
    }

    @Override
    public void saveTransaksiPfm(Transaksi transaksi) {
        //create disposible
        if (transaksi != null) {
            DisposableSingleObserver disposableSingleObserver = new DisposableSingleObserver<Long>() {
                @Override
                public void onSuccess(Long id) {
                    Log.d(TAG, "onSuccess Save : " + transaksi.getRefnum());
                }

                @Override
                public void onError(Throwable e) {
                    /*
                    if(BuildConfig.DEBUG)
                    e.printStackTrace();

                     */
                }
            };


            getTransaksiPfmSource().saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribe(disposableSingleObserver);

            getCompositeDisposable().add(disposableSingleObserver);
        }
    }

    @Override
    public void stop() {
        newTransaksi = null;
        super.stop();
    }


}