package id.co.bri.brimons.presenters.transfer;

import id.co.bri.brimons.contract.IPresenter.transfer.IInquiryTransferPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.transfer.IInquiryTransferView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.response.KonfirmasiTransferResponse2;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by FNS
 */

public class InquiryTransferPresenter<V extends IMvpView & IInquiryTransferView> extends MvpPresenter<V> implements IInquiryTransferPresenter<V> {


    public InquiryTransferPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataKonfirmasi(String refNumb, String account, String ammount, String favName ) {
        if (getView() != null) {
            //initiate param with getter from view

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getDataInquiry(refNumb, account, ammount, favName, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            KonfirmasiTransferResponse2 konfirmasiTransferResponse2 = response.getData(KonfirmasiTransferResponse2.class);
                            getView().onGetDataKonfirmasi(konfirmasiTransferResponse2);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }


}