package id.co.bri.brimons.presenters.lupapin;

import id.co.bri.brimons.contract.IPresenter.lupapin.ILupaPinOtpPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.lupapin.ILupaPinOtpView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.LupaPinOtpFastRequest;
import id.co.bri.brimons.models.apimodel.request.LupaPinOtpRequest;
import id.co.bri.brimons.models.apimodel.request.ResendOtpFastRequest;
import id.co.bri.brimons.models.apimodel.request.ResendOtpRequest;
import id.co.bri.brimons.models.apimodel.response.PinRefnumResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class LupaPinOtpPresenter  <V extends IMvpView & ILupaPinOtpView> extends MvpPresenter<V> implements ILupaPinOtpPresenter<V> {

    String url, urlResend,urlResendFast,  otp, urlFast;

    public LupaPinOtpPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onResendOtp(ResendOtpRequest resendOtpRequest) {
        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(urlResend, resendOtpRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            ResendOtpRequest responResend = response.getData(ResendOtpRequest.class);
                            getView().onSuccessResend(responResend.getReferenceNumber());
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void onResendOtpFast(ResendOtpFastRequest resendOtpFastRequest) {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlResendFast, resendOtpFastRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            ResendOtpRequest responResend = response.getData(ResendOtpRequest.class);
                            getView().onSuccessResend(responResend.getReferenceNumber());
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getOtp(String otp) {
        this.otp = otp;
    }

    @Override
    public void onSendOtp() {
        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(url, createRequest(), seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
//                            Log.d(TAG, "onApiCallSuccess: masuk kesini");

                            PinRefnumResponse pinRefnumResponse = response.getData(PinRefnumResponse.class);
                            getView().onSuccessSendOtp(pinRefnumResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onSalahPin(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void onSendOtpFast() {
        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(urlFast, createRequestFast(), seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
//                            Log.d(TAG, "onApiCallSuccess: masuk kesini");

                            PinRefnumResponse pinRefnumResponse = response.getData(PinRefnumResponse.class);
                            getView().onSuccessSendOtpFast(pinRefnumResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onSalahPin(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }


    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlFast(String url) {
        this.urlFast = url;
    }

    @Override
    public void setUrlResendFast(String url) {
        this.urlResendFast = url;
    }

    @Override
    public void setUrlResend(String urlResend) {
        this.urlResend = urlResend;
    }

    @Override
    public LupaPinOtpRequest createRequest() {
         return new LupaPinOtpRequest(getView().refNum(), otp);
    }

    @Override
    public LupaPinOtpFastRequest createRequestFast() {
        return new LupaPinOtpFastRequest(getView().refNum(), getBRImoPrefRepository().getUsername(), otp, getBRImoPrefRepository().getTokenKey());
    }
}