package id.co.bri.brimons.presenters.travel;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.travel.IMenuTravelActivity;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.travel.IMenuTravelView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PartnerIdRequest;
import id.co.bri.brimons.models.apimodel.response.*;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class MenuTravelPresenter <V extends IMvpView & IBaseFormView & IMenuTravelView> extends
        BaseFormPresenter<V> implements IMenuTravelActivity<V> {

    private String url;
    private String urlForm;
    private String urlFormKai;
    private String urlInjourney;
    private String urlKcic;
    private String urlPesawat;

    public MenuTravelPresenter(SchedulerProvider schedulerProvider,
                               CompositeDisposable compositeDisposable,
                               BRImoPrefSource mBRImoPrefRepository,
                               ApiSource apiSource,
                               CategoryPfmSource categoryPfmSource,
                               TransaksiPfmSource transaksiPfmSource,
                               AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider,
                compositeDisposable,
                mBRImoPrefRepository,
                apiSource,
                categoryPfmSource,
                transaksiPfmSource,
                anggaranPfmSource);
    }

    @Override
    public void getListMenu() {
        if (url == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataForm(url,seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {


                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        MenuTravelResponse menuTravelResponse = response.getData(MenuTravelResponse.class);
                        getView().onSuccessGetMenu(menuTravelResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.url = formUrl;
    }

    @Override
    public void getFormBus() {
        if (urlForm == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataForm(urlForm,seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {


                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        CityFormResponse cityFormResponse = response.getData(CityFormResponse.class);
                        getView().onSuccessGetForm(cityFormResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    @Override
    public void getFormWebViewKai() {
        if (urlFormKai == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataTanpaRequest(urlFormKai, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {


                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        UrlWebViewResponse urlWebViewResponse =
                                response.getData(UrlWebViewResponse.class);
                        getView().onSuccessGetUrlKai(urlWebViewResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }


    @Override
    public void setUrlFormBus(String url) {
        this.urlForm = url;
    }

    @Override
    public void setUrlFormWebViewKai(String url) {
        this.urlFormKai = url;
    }

    @Override
    public void setUrlInjourney(String urlWebView) {
        this.urlInjourney = urlWebView;
    }

    @Override
    public void getWebViewInjourney(PartnerIdRequest partnerIdRequest) {
        if (urlInjourney == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlInjourney, partnerIdRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                GeneralWebviewResponse webviewResponse =
                                        response.getData(GeneralWebviewResponse.class);
                                getView().onSuccessWebview(webviewResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onException93(restResponse.getDesc());
                                } else {
                                    getView().onExceptionWebview(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    @Override
    public void setUrlFormWebviewKcic(String url) {
        this.urlKcic = url;
    }

    @Override
    public void getFormWebviewKcic(PartnerIdRequest partnerIdRequest) {
        if (urlKcic == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlKcic, partnerIdRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                GeneralWebviewResponse webviewResponse =
                                        response.getData(GeneralWebviewResponse.class);
                                getView().onSuccessGetUrlKcic(webviewResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onException93(restResponse.getDesc());
                                } else {
                                    getView().onExceptionWebview(restResponse.getDesc());
                                }
                            }
                        })
        );

    }

    @Override
    public void setUrlFormWebviewPesawat(String url) {
        this.urlPesawat = url;
    }

    @Override
    public void getFormWebviewPesawat(PartnerIdRequest partnerIdRequest) {
        if (urlPesawat == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlPesawat, partnerIdRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                GeneralWebviewResponse webviewResponse =
                                        response.getData(GeneralWebviewResponse.class);
                                getView().onSuccessGetUrlPesawat(webviewResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onException93(restResponse.getDesc());
                                } else {
                                    getView().onExceptionWebview(restResponse.getDesc());
                                }
                            }
                        })
        );

    }

    @Override
    public void updateFirstKcic(boolean firstKcic) {
        getBRImoPrefRepository().saveFirstKcic(firstKcic);
    }

}