package id.co.bri.brimons.presenters.finansialrek;

import id.co.bri.brimons.contract.IPresenter.finansialrek.IInputDataFinansialPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.finansialrek.IInputDataFinansialView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.KtpFinansialRequest;
import id.co.bri.brimons.models.apimodel.request.KtpRefnumRequest;
import id.co.bri.brimons.models.apimodel.response.DataKtpFinansialResponse;
import id.co.bri.brimons.models.apimodel.response.DataKtpResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class InputDataFinansialPresenter<V extends IMvpView & IInputDataFinansialView>
        extends MvpPresenter<V> implements IInputDataFinansialPresenter<V> {

    private String url;
    private String urlKtp;

    public InputDataFinansialPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                       BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                       TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlKtp(String urlKtp) {
        this.urlKtp = urlKtp;
    }

    @Override
    public void getDataFinansial(KtpRefnumRequest ktpRefnumRequest) {
        if (url != null && isViewAttached()) {

            String seq = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(url, ktpRefnumRequest, seq)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seq) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            DataKtpFinansialResponse ktpFinResponse = response.getData(DataKtpFinansialResponse.class);
                            getView().onSuccessGetData(ktpFinResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onExceptionKtp(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void sendDataKtp(KtpFinansialRequest request) {
        if (urlKtp != null && isViewAttached()) {

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlKtp, request, seq)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seq) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            DataKtpResponse dataKtpResponse = response.getData(DataKtpResponse.class);
                            getView().onSuccessDataKtp(dataKtpResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }
}