package id.co.bri.brimons.presenters.britamarencanarevamp

import id.co.bri.brimons.contract.IPresenter.britamarencanarevamp.IProductBriefTabunganRevPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.britamarencanarevamp.IProductBriefTabunganRevView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimons.models.apimodel.request.bukarekening.InquiryOpenAccountRequest
import id.co.bri.brimons.models.apimodel.response.onExceptionWH
import id.co.bri.brimons.models.apimodel.response.QuestionResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.bukarekening.InquiryOpenAccJunioResponse
import id.co.bri.brimons.models.apimodel.response.bukarekening.InquiryOpenAccResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ProductBriefTabunganRevPresenter<V>(schedulerProvider: SchedulerProvider,
                                          compositeDisposable: CompositeDisposable,
                                          mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                          categoryPfmSource: CategoryPfmSource,
                                          transaksiPfmSource: TransaksiPfmSource,
                                          anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IProductBriefTabunganRevPresenter<V> where V : IMvpView, V : IProductBriefTabunganRevView {

    private var mUrlInquiry: String? = null
    private var mUrlSafety : String? = null
    private var inquiryGeneralOpenAccountResponse = InquiryOpenAccResponse()
    private var inquiryJunioOpenAccountResponse = InquiryOpenAccJunioResponse()


    override fun setUrlInquiry(url: String) {
        mUrlInquiry = url
    }

    override fun getInquiryTabungan(request: InquiryOpenAccountRequest) {
        if (isViewAttached) {
            //sho loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(mUrlInquiry, request, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView().hideProgress()
                        getView().onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        inquiryGeneralOpenAccountResponse = response.getData(
                            InquiryOpenAccResponse::class.java
                        )
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            getView().onSuccessInquiry(inquiryGeneralOpenAccountResponse)
                        }
                        else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_03.value, ignoreCase = true)) {
                            val onExceptionWH = response.getData(onExceptionWH::class.java)
                            getView().onException03(onExceptionWH)
                        }

                        if(!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getInquiryTabunganJunio(request: InquiryOpenAccountRequest) {
        if (isViewAttached) {
            //sho loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(mUrlInquiry, request, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView().hideProgress()
                        getView().onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        inquiryJunioOpenAccountResponse = response.getData(
                            InquiryOpenAccJunioResponse::class.java
                        )
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            getView().onSuccessJunioInquiry(inquiryJunioOpenAccountResponse)
                        }
                        else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_03.value, ignoreCase = true)) {
                            val onExceptionWH = response.getData(onExceptionWH::class.java)
                            getView().onException03(onExceptionWH)
                        }

                        if(!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlSafety(url: String) {
        mUrlSafety = url
    }

    override fun getPusatBantuanSafety(id: String) {
        if (mUrlSafety == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(id)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlSafety, safetyPusatBantuanRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val topicQuestionResponse = response.getData(
                                QuestionResponse::class.java
                            )
                            if (mUrlSafety != null) getView().onSuccessPusatBantuanSafety(
                                topicQuestionResponse
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                restResponse.desc
                            )
                        }
                    })
            )

    }

    override fun getInquiryRencana(rencana: InquiryOpenAccountRequest) {
    }

}