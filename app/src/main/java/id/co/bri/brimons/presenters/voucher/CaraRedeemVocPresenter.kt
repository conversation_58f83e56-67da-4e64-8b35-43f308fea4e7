package id.co.bri.brimons.presenters.voucher

import id.co.bri.brimons.contract.IPresenter.voucher.ICaraRedeemVocPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.voucher.ICaraRedeemVocView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig

import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.voucher.VoucherRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.voucher.TutorialVoucherResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class CaraRedeemVocPresenter<V>(schedulerProvider: SchedulerProvider?,
                               compositeDisposable: CompositeDisposable?,
                               mBRImoPrefRepository: BRImoPrefSource?,
                               apiSource: ApiSource?, categoryPfmSource: CategoryPfmSource?,
                               transaksiPfmSource: TransaksiPfmSource?,
                               anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    ICaraRedeemVocPresenter<V> where V : IMvpView?, V : ICaraRedeemVocView {

    private var urlCaraRedeemUrl: String? = null

    override fun setUrlCaraRedeem(caraRedeemUrl: String) {
        urlCaraRedeemUrl = caraRedeemUrl
    }

    override fun getCaraRedeem(voucherRequest: VoucherRequest) {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view!!.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlCaraRedeemUrl, voucherRequest, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val caraRedeemVoucherResponse = response.getData(
                                TutorialVoucherResponse::class.java
                            )
                            getView()!!.hideProgress()
                            getView()!!.onSuccessData(caraRedeemVoucherResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }
                    })
            )
        }
    }
}