package id.co.bri.brimons.presenters.remittence;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.remittence.IKonfirmasiIntlPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.remittence.IKonfirmasiIntlView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PaymentRequest;
import id.co.bri.brimons.models.apimodel.response.KonfirmasiInternasionalResponse;
import id.co.bri.brimons.models.apimodel.response.ReceiptInternasionalResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiIntlPresenter<V extends IMvpView & IKonfirmasiIntlView> extends MvpPresenter<V> implements IKonfirmasiIntlPresenter<V> {

    private static final String TAG = "KonfirmasiIntlPresenter";
    String url;

    public KonfirmasiIntlPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void getPaymentInternational(PaymentRequest paymentRequest, KonfirmasiInternasionalResponse konfirmasiInternasionalResponse) {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(url, paymentRequest, seqNum)
                    .timeout(60, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                ReceiptInternasionalResponse receiptInternasionalResponse = response.getData(ReceiptInternasionalResponse.class);
                                getView().onSuccessGetPayment(receiptInternasionalResponse);
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        konfirmasiInternasionalResponse.getPfmCategory(),
                                        konfirmasiInternasionalResponse.getAmountTransfer().longValue(),
                                        konfirmasiInternasionalResponse.getReferenceNumber(),
                                        konfirmasiInternasionalResponse.getPfmDescription()
                                        )
                                );
                            } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().onException01(response.getDesc());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void isGeneral(boolean general) {

    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                            .saveTransaksiPfm(transaksi)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new DisposableSingleObserver<Long>() {
                                @Override
                                public void onSuccess(Long aLong) {

                                }

                                @Override
                                public void onError(Throwable e) {
//                            Log.d(TAG, "onError: " + e.toString());
                                }
                            })
            );
        }
    }
}