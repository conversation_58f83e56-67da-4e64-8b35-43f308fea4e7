package id.co.bri.brimons.presenters.general;

import id.co.bri.brimons.contract.IPresenter.general.IBrowserPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.general.IBrowserView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.BrowserIntentRequest;
import id.co.bri.brimons.models.apimodel.response.BrowserResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class BrowserPresenter<V extends IMvpView & IBrowserView> extends MvpPresenter<V> implements IBrowserPresenter<V> {

    private static final String TAG = "BrowserPresenter";

    protected String url;

    public BrowserPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataLauncher(int menuCode) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        BrowserIntentRequest browserIntentRequest = new BrowserIntentRequest(String.valueOf(menuCode));
        getCompositeDisposable()
                         //ubah request ke menucode
                        .add(getApiSource().getData(url, browserIntentRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                BrowserResponse browserResponse = response.getData(BrowserResponse.class);
                                getView().onSuccessGetUrl(browserResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                               getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                    getView().onException(restResponse.getDesc());
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }
                        }));
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void start() {
        super.start();

    }
}