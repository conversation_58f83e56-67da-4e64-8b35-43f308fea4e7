package id.co.bri.brimons.presenters.simpedes;

import android.util.Log;

import com.google.gson.Gson;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.simpedes.IDetailSimpedesPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IDetailSimpedesView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PencairanSimpedesRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SimpedesPencairanResponse;
import id.co.bri.brimons.models.apimodel.response.TopupImpianResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailSimpedesPresenter<V extends IMvpView & IDetailSimpedesView>
        extends MvpPresenter<V> implements IDetailSimpedesPresenter<V> {

    protected String urlPencairan;
    protected String urlTopup;
    protected String urlPenutupan;

    public DetailSimpedesPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                   BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                   TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlPencairanForm(String urlPencairan) {
        this.urlPencairan = urlPencairan;
    }

    @Override
    public void setUrlTopupForm(String urlTopup) {
        this.urlTopup = urlTopup;
    }

    @Override
    public void setUrlPenutupan(String urlPenutupan) {
        this.urlPenutupan = urlPenutupan;
    }

    @Override
    public void getDataPencairanForm(String parentAccount, String childAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            PencairanSimpedesRequest pencairanRequest = new PencairanSimpedesRequest(parentAccount, childAccount);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlPencairan, pencairanRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    SimpedesPencairanResponse responseData = response.getData(SimpedesPencairanResponse.class);
                                    getView().getDataSuccessPencairan(responseData);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getDataTopUpForm(String parentAccount, String childAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            PencairanSimpedesRequest pencairanRequest = new PencairanSimpedesRequest(parentAccount, childAccount);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlTopup, pencairanRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    Log.d("TestAPI", "setupView: "+new Gson().toJson(response.getData()));
                                    TopupImpianResponse topupData = response.getData(TopupImpianResponse.class);
                                    getView().getDataSuccessTopup(topupData);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getDataPenutupan(String parentAccount, String childAccount) {
        if (isViewAttached()) {
            //initiate param with getter from view

            PencairanSimpedesRequest penutupanRequest = new PencairanSimpedesRequest(parentAccount, childAccount);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlPenutupan, penutupanRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralConfirmationResponse confirmationData = response.getData(GeneralConfirmationResponse.class);
                                    getView().getdataSuccessPenutupan(confirmationData);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }
}