package id.co.bri.brimons.presenters.property

import id.co.bri.brimons.contract.IPresenter.property.IFormPropertyPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimons.contract.IView.property.IFormPropertyView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.enumconfig.JourneyType
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimons.models.apimodel.request.property.InquiryPropertyRequest
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.SavedResponse
import id.co.bri.brimons.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormPropertyPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : BaseFormRevampPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormPropertyPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormPropertyView {


    override fun setUpdateItem(url: String, savedResponse: SavedResponse, position: Int, type: Int, journeyType: JourneyType?) {
        if (url.isEmpty() || !isViewAttached || onLoad) return

        onLoad = true

        val str1 = savedResponse.value.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val saveId = str1[0]

        val updateSavedRequest = UpdateSavedRequest(saveId, "")
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(apiSource.getData(url, updateSavedRequest, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onSuccessUpdate(savedResponse, position, type)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onApiError(restResponse)
                }
            })
        )
    }

    override fun getDataInquiry(inquiryPropertyRequest: InquiryPropertyRequest) {
        if (mUrlInquiry.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(mUrlInquiry, inquiryPropertyRequest, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView().hideProgress()
                    val responsebriva = response.getData(InquiryBrivaRevampResponse::class.java)
                    getView().onSuccessGetInquiry(responsebriva, mUrlPayment)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onLoad = false
                    getView().hideProgress()
                    when(restResponse.code){
                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView().onSessionEnd(restResponse.desc)
                        RestResponse.ResponseCodeEnum.RC_99.value, RestResponse.ResponseCodeEnum.RC_FO.value -> getView().onException99(restResponse.desc)
                        RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value -> getView().onBillingAlreadyPaid(restResponse.desc)
                        else -> getView().onException(restResponse.desc)
                    }
                }
            })
        )
    }


}