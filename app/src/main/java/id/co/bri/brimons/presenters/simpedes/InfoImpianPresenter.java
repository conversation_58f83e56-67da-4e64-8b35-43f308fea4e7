package id.co.bri.brimons.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.simpedes.IInfoImpianPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IInfoImpianView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.ImpianForm;
import id.co.bri.brimons.models.apimodel.request.AccountRequest;
import id.co.bri.brimons.models.apimodel.request.PencairanSimpedesRequest;
import id.co.bri.brimons.models.apimodel.response.InfoImpianSimpedesResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoImpianPresenter<V extends IMvpView & IInfoImpianView>
        extends MvpPresenter<V> implements IInfoImpianPresenter<V> {

    protected String urlAddImpian;
    protected String urlDetailImpian;

    public InfoImpianPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlAddImpian(String urlAddImpian) {
        this.urlAddImpian = urlAddImpian;
    }

    @Override
    public void setUrlDetailImpian(String urlDetailImpian) {
        this.urlDetailImpian = urlDetailImpian;
    }

    @Override
    public void getAddImpian(String account) {
        if (isViewAttached()) {
            //initiate param with getter from view

            AccountRequest accountRequest = new AccountRequest(account);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlAddImpian, accountRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ImpianForm impianForm = response.getData(ImpianForm.class);
                                    getView().getDataImpianForm(impianForm);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void getDataDetailImpian(String parentNumber, String childNumber) {
        if (isViewAttached()) {
            //initiate param with getter from view

            PencairanSimpedesRequest detailImpianReq = new PencairanSimpedesRequest(parentNumber, childNumber);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlDetailImpian, detailImpianReq, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InfoImpianSimpedesResponse infoImpianRes = response.getData(InfoImpianSimpedesResponse.class);
                                    getView().getDataDetailImpian(infoImpianRes);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }
}