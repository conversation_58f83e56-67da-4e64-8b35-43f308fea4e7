package id.co.bri.brimons.presenters.dompetdigitalreskin

import id.co.bri.brimons.contract.IPresenter.dompetdigitalreskin.IConfirmationDompetDigitalReskinPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.dompetdigitalreskin.IConfirmationDompetDigitalReskinView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.DbConfig
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.ConfirmationDompetDigitalRevRequest
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.FastConfirmationDompetDigitalRevRequest
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.FastPaymentDompetDigitalRevRequest
import id.co.bri.brimons.models.apimodel.request.dompetdigitalrevamp.PaymentDompetDigitalRevRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.GeneralResponse
import id.co.bri.brimons.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.onExceptionWH
import id.co.bri.brimons.models.daomodel.Transaksi
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver

class ConfirmationDompetDigitalReskinPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IConfirmationDompetDigitalReskinPresenter<V> where V : IMvpView?, V : IConfirmationDompetDigitalReskinView? {
    private lateinit var mUrlConfirm: String
    private lateinit var mUrlPayment: String
    private lateinit var confirmationRequest: Any
    protected var paymentRequest: Any? = null
    private var isGeneral: Boolean = false
    private var disposablePayment: Disposable? = null

    override fun getDataPayment(
        pin: String,
        generalConfirmationResponse: GeneralConfirmationResponse,
        fromFast: Boolean,
        isUsingC2: Boolean
    ) {
        if (mUrlPayment == null) {
            return
        }

        if (!isViewAttached) {
            return
        }

        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber

            paymentRequest = if (fromFast) FastPaymentDompetDigitalRevRequest(
                getFastMenuRequest(),
                generalConfirmationResponse.referenceNumber,
                generalConfirmationResponse.pfmCategory.toString(), pin, ""
            )
            else PaymentDompetDigitalRevRequest(
                generalConfirmationResponse.referenceNumber,
                generalConfirmationResponse.pfmCategory.toString(), pin, ""
            )

            disposablePayment = generateRevampPaymentDisposable(
                generalConfirmationResponse,
                mUrlPayment,
                paymentRequest,
                seqNum
            )
            disposablePayment?.let { compositeDisposable.add(it) }
        }
    }

    override fun isGeneral(general: Boolean) {
        isGeneral = general
    }

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun setUrlPayment(urlPayment: String) {
        mUrlPayment = urlPayment
    }

    override fun getDataConfirmation(
        refNum: String,
        accountNum: String,
        amount: String,
        save: String,
        note: String,
        fromFast: Boolean
    ) {
        if (mUrlConfirm.isEmpty() || !isViewAttached) return

        confirmationRequest = if (fromFast) {
            FastConfirmationDompetDigitalRevRequest(
                getFastMenuRequest(),
                refNum,
                accountNum,
                amount,
                save,
                note
            )
        } else {
            ConfirmationDompetDigitalRevRequest(
                refNum,
                accountNum,
                amount,
                save,
                note
            )
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
            apiSource.getData(mUrlConfirm, confirmationRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val generalConfirmationResponse = response.getData(
                            GeneralConfirmationResponse::class.java
                        )
                        getView()?.onGetDataConfirmation(generalConfirmationResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) {
                            getView()?.onExceptionTrxExpired(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        compositeDisposable.add(disposable)
    }

    fun generateRevampPaymentDisposable(
        generalConfirmationResponse: GeneralConfirmationResponse,
        urlPayment: String?,
        paymentRequest: Any?,
        seqNum: String?
    ): Disposable {
        val disposable: Disposable =
            apiSource.getData(urlPayment, paymentRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        val receiptRevampResponse = response.getData(
                            ReceiptRevampResponse::class.java
                        )

                        getView()?.onSuccessGetPayment(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        val currentView = getView()
                        currentView?.hideProgress()

                        when {
                            restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                ignoreCase = true
                            ) -> currentView?.onExceptionTrxExpired(restResponse.desc)

                            restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_01.value,
                                ignoreCase = true
                            ) -> currentView?.onException01(restResponse.desc)

                            restResponse.code == RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value -> {
                                currentView?.onExceptionLimitExceed(
                                    restResponse.getData(GeneralResponse::class.java)
                                )
                            }

                            restResponse.code == "MR" -> currentView?.onExceptionRevamp("")

                            restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_02.value,
                                ignoreCase = true
                            ) -> {
                                val exception02 = restResponse.getData(onExceptionWH::class.java)
                                currentView?.onExceptionWH(exception02)
                            }

                            else -> currentView?.onException(restResponse.desc)
                        }
                    }
                })
        return disposable
    }


    override fun start() {
        super.start()
        setDisablePopup(true)
    }

    override fun stop() {
        super.stop()
    }
}