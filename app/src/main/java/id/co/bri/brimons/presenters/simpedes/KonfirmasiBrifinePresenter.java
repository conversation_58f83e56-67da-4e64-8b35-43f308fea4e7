package id.co.bri.brimons.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.simpedes.IKonfirmasiBrifinePresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.simpedes.IKonfirmasiBrifineView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PaymentBrifineRequest;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiBrifinePresenter<V extends IMvpView & IKonfirmasiBrifineView>
        extends MvpPresenter<V> implements IKonfirmasiBrifinePresenter<V> {

    protected String urlPayment;

    public KonfirmasiBrifinePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void getDataPayment(String imgKtp, String referenceNumber, String pin) {
        if (isViewAttached()) {

            PaymentBrifineRequest paymentBrifineRequest = new PaymentBrifineRequest(imgKtp, referenceNumber, pin);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getData(urlPayment, paymentBrifineRequest, seq)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserverKonfirmasi(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    PendingResponse pendingResponse = response.getData(PendingResponse.class);
                                    getView().getDataSuccessPaytment(pendingResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                        getView().onException93(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}