package id.co.bri.brimons.presenters.onboardingnewskin

import id.co.bri.brimons.contract.IPresenter.newskinonboarding.IChangePinPresenter
import id.co.bri.brimons.contract.IView.newskinonboarding.IChangePinView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.onboardingrevamp.PinChangeRequest
import id.co.bri.brimons.models.apimodel.response.onboardingrevamp.PinCheckSuccessData
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ChangePinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), IChangePinPresenter<V> where V : IChangePinView {

    private var urlValidatePin = ""
    private var errorMassage = ""
    private var code = ""
    override fun setChangePinUrl(url: String) {
        urlValidatePin = url
    }

    override fun onCheckPinCreate(referenceNumber: String) {
        if (!isViewAttached || urlValidatePin.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = PinChangeRequest(reference_number = referenceNumber)

        compositeDisposable.add(
            apiSource.getData(urlValidatePin, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PinCheckSuccessData::class.java)
                        getView().onPinValid(checkResponse.referenceNumber, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PinCheckSuccessData::class.java)
                        getView().onPinCheckFailed(res, restResponse.code)
                        errorMassage = restResponse.desc
                        code = restResponse.code
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailure(true, code)
                    }
                })
        )
    }
}