package id.co.bri.brimons.presenters.esbn;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimons.contract.IPresenter.esbn.IInquiryMpnSbnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.esbn.IInquiryMpnSbnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.esbn.ConfirmationDataRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InquirySbnMpnPresenter<V extends IMvpView & IInquiryMpnSbnView> extends MvpPresenter<V> implements IInquiryMpnSbnPresenter<V> {

    private String url;

    @Inject
    public InquirySbnMpnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlKonfirmasi(String url) {
        this.url = url;
    }

    @Override
    public void getKonfirmasi(ConfirmationDataRequest confirmationDataRequest) {
        getView().showProgress();

        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(url, confirmationDataRequest, seq)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seq) {
                    @Override
                    protected void onFailureHttp(String type) {
                        getView().hideProgress();
                        getView().onException(type);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        GeneralConfirmationResponse dataResponse = response.getData(GeneralConfirmationResponse.class);
                        getView().onSuccessGetConfirmation(dataResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                            getView().onException(restResponse.getDesc());
                        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                }));
    }

    @Override
    public void start() {
        super.start();
        getDefaultSaldo();
    }


    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();

        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold);
    }

}