package id.co.bri.brimons.presenters.dplk;

import id.co.bri.brimons.contract.IPresenter.dplk.IInfoDplkPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.dplk.IInfoDplkView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.AccountDplkRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryDplkRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.InfoDplkResponse;
import id.co.bri.brimons.models.apimodel.response.ListRekeningDplkResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.dplk.DplkBoardingResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.EsbnExceptionResponse;
import id.co.bri.brimons.presenters.MvpPresenter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoDplkPresenter<V extends IMvpView & IInfoDplkView> extends MvpPresenter<V> implements IInfoDplkPresenter<V> {

    private static final String TAG = "RekeningPresenter";
    private String url;
    private String urlAccount;
    private String urlInquiry;
    private String urlOnboarding;
    private boolean isLoading = false;

    ListRekeningDplkResponse listRekeningResponse = new ListRekeningDplkResponse();
    List<ListRekeningDplkResponse.Account> accountList;

    public InfoDplkPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlDplk(String url) {
        this.url = url;
    }

    @Override
    public void setUrlAccount(String url) {
        this.urlAccount = url;
    }

    @Override
    public void setUrlOnboarding(String url) {
        this.urlOnboarding = url;
    }

    @Override
    public void getAccountRekening() {
        if (isLoading)
            return;

        if (isViewAttached()) {
            isLoading = true;
            accountList = new ArrayList<>();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlAccount, "",seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum){

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            isLoading = false;
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            isLoading = false;
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                accountList.clear();
                                listRekeningResponse = response.getData(ListRekeningDplkResponse.class);

                                accountList.addAll(listRekeningResponse.getAccount());
                                getView().onSuccessGetListRekening(listRekeningResponse);
                            }
                            else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                                EsbnExceptionResponse response1 = response.getData(EsbnExceptionResponse.class);
                                getView().onException02(response1);
                            }
                            else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                listRekeningResponse = response.getData(ListRekeningDplkResponse.class);
                                getView().onException01(listRekeningResponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            isLoading = false;
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())){
                                getView().onException12(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }

                        @Override
                        public void onComplete() {
                            super.onComplete();
                            isLoading = false;
                            getView().SuccessGetAccountRek(accountList);
                        }
                    }));




        }
    }

    @Override
    public void getOnboarding() {
        getView().onProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(urlOnboarding, "",seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum){

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().onHide();
                        DplkBoardingResponse response1 = response.getData(DplkBoardingResponse.class);
                        getView().onSuccessOnBoarding(response1);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().onHide();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                            getView().onSessionEnd(restResponse.getDesc());
                        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())){
                            getView().onException12(restResponse.getDesc());
                        }
                        else
                            getView().onException(restResponse.getDesc());
                    }
                }));
    }

    @Override
    public void setInfoDplk(AccountDplkRequest accountDplkRequest) {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(url, accountDplkRequest, seqNum)
                    .delay(90, TimeUnit.MILLISECONDS)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            InfoDplkResponse infoDplkRes = response.getData(InfoDplkResponse.class);
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                getView().SuccessGetAccountDplk(infoDplkRes);
                            } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().notFoundDplk();
                            }

                            getView().hideProgress();
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().notFoundDplk();
                        }

                    }));

        }
    }

    @Override
    public void getDataInquiry(String no_dplk) {
        if (isViewAttached()) {
            //initiate param with getter from view
            getView().onProgress();
            InquiryDplkRequest inquiryRequest = new InquiryDplkRequest(no_dplk);
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlInquiry, inquiryRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().onHide();
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessGetInquiry(responsebriva);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().onHide();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    @Override
    public void setInquiryUrl(String url) {
        this.urlInquiry = url;
    }
}