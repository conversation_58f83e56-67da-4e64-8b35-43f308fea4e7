package id.co.bri.brimons.presenters.emas

import id.co.bri.brimons.contract.IPresenter.emas.IDashboardEmasPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.emas.IDashboardEmasView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimons.models.apimodel.request.emas.GrafikEmasRequest
import id.co.bri.brimons.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimons.models.apimodel.response.onExceptionWH
import id.co.bri.brimons.models.apimodel.response.InfoResponse
import id.co.bri.brimons.models.apimodel.response.QuestionResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.TopupEmasResponse
import id.co.bri.brimons.models.apimodel.response.emas.*
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DashboardEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                compositeDisposable: CompositeDisposable?,
                                mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDashboardEmasPresenter<V> where V : IMvpView?, V : IDashboardEmasView? {

    private var urlPortolio : String? = null
    private var urlRate : String? = null
    private var urlMonthly : String? = null
    private var urlDashboard : String? = null
    private var urlPusatBantuan : String? = null
    private var urlDetailRekening : String? = null
    private var urlBeliEmas : String? = null
    private var urlJualEmas : String? = null
    private var urlGrafik : String? = null
    private var urlRiwayat : String? = null
    private var urlAmbilFisikEmas : String? = null
    var mUrlHistoryAmbilFisikEmas : String? = null

    override fun setUrlDashboard(url: String) {
        urlDashboard = url
    }

    override fun setUrlPortofolio(url: String) {
        urlPortolio = url
    }

    override fun setUrlRate(url: String) {
        urlRate = url
    }

    override fun setUrlMonthly(url: String) {
        urlMonthly = url
    }

    override fun setUrlPusatBantuan(url: String) {
        urlPusatBantuan = url
    }

    override fun setUrlDetailEmas(url: String) {
        urlDetailRekening=url
    }

    override fun setUrlGetFormBeli(urlBeli: String) {
        urlBeliEmas = urlBeli
    }

    override fun setUrlFormJual(urlJual: String) {
        urlJualEmas = urlJual
    }

    override fun setUrlGrafikJual(url: String) {
        urlGrafik = url
    }

    override fun setUrlRiwayatTransaksi(url: String) {
        urlRiwayat = url
    }

    override fun setUrlAmbilFisikEmas(url: String) {
        urlAmbilFisikEmas = url
    }

    override fun setUrlRiwayatAmbilFisikEmas(url: String) {
        mUrlHistoryAmbilFisikEmas = url
    }


    override fun getDataDashboardEmas() {
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
                apiSource.getData(urlDashboard, "", seqNum)
                        .subscribeOn(schedulerProvider.single())
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(type: String) {
                                getView()?.onException(type)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                    getView()?.onSuccessGetDasboardData()
                                } else if (response.code.contains(RestResponse.ResponseCodeEnum.RC_01.value)) {
                                    val esbnExceptionResponse = response.getData(OnboardingSliderResponse::class.java)
                                    getView()?.onSuccessRegistrasiEmas(esbnExceptionResponse)
                                }
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) getView()?.onSessionEnd(restResponse.desc)
                                else getView()?.onException(restResponse.desc)
                            }
                        })
        )
    }

    override fun getDataPortofolio() {
        //initiate param with getter from view
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable = apiSource.getDataTanpaRequest(urlPortolio, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideSkeletonPortofolio()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val dashboardEmasPortofolioResponse = response.getData(DashboardEmasPortofolioResponse::class.java)
                        if (dashboardEmasPortofolioResponse.portofolio?.isGoldAccountStatusNotActive() == true) {
                            getView()?.onGoldAccountNotActive(dashboardEmasPortofolioResponse)
                            return
                        }
                        getView()?.onSuccessPortofolio(dashboardEmasPortofolioResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value,ignoreCase = true)){
                            getView()?.onExceptionPortofolio12()
                        }
                        else {
                            getView()?.hideSkeletonPortofolio()
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        compositeDisposable.add(disposable)
    }

    override fun getDataRate() {
        //initiate param with getter from view
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable = apiSource.getDataTanpaRequest(urlRate, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideSkeletonRate()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val dashboardEmasRateResponse = response.getData(DashboardEmasRateResponse::class.java)
                        getView()?.onSuccessRate(dashboardEmasRateResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value,ignoreCase = true)){
                            getView()?.onExceptionRate12()
                        }
                        else {
                            getView()?.hideSkeletonRate()
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        compositeDisposable.add(disposable)
    }

    override fun getDataMonthly() {
        //initiate param with getter from view
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable = apiSource.getDataTanpaRequest(urlMonthly, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideSkeletonMonthly()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val dashboardEmasMonthlyResponse = response.getData(DashboardEmasMonthlyResponse::class.java)
                        getView()?.onSuccessMonthly(dashboardEmasMonthlyResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {

                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()?.onSessionEnd(restResponse.desc)
                        } else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value,ignoreCase = true)){
                            getView()?.onExceptionMonthly12()
                        }
                        else {
                            getView()?.hideSkeletonMonthly()
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        compositeDisposable.add(disposable)
    }

    override fun getPusatBantuanSafety(code: String) {
        if (urlPusatBantuan == null || !isViewAttached) {
            return
        }

        view?.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(code)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getData(urlPusatBantuan, safetyPusatBantuanRequest, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()?.hideProgress()
                                        getView()?.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()?.hideProgress()
                                        val topicQuestionResponse = response.getData(
                                                QuestionResponse::class.java
                                        )
                                        if (urlPusatBantuan != null) getView()?.onSuccessGetPusatBantuan(
                                                topicQuestionResponse
                                        )
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()?.hideProgress()
                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()?.onSessionEnd(restResponse.desc) else getView()?.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun getDetailEmas() {
        if (urlDetailRekening == null || !isViewAttached) {
            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(urlDetailRekening, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()?.hideProgress()
                                        getView()?.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()?.hideProgress()
                                        GeneralHelper.responseChuck(response)
                                        val detailEmasResponse = response.getData(DetailEmasResponse::class.java)
                                        getView()?.onSuccessDetailEmas(detailEmasResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()?.hideProgress()
                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()?.onSessionEnd(restResponse.desc) else getView()?.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun getJualEmas() {
        if (urlJualEmas == null || !isViewAttached) {

            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getData(urlJualEmas, "", seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()?.hideProgress()
                                        getView()?.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()?.hideProgress()
                                        val dataJualEmas = response.getData(
                                                InquiryOpenEmasResponse::class.java
                                        )
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                            getView()?.onSuccessFormJualEmas(
                                                    dataJualEmas
                                            )
                                        }
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                            val onOnExceptionWH: onExceptionWH =
                                                    restResponse.getData(
                                                            onExceptionWH::class.java
                                                    )
                                            getView()?.exceptionEODEOM(onOnExceptionWH)
                                        }
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()?.hideProgress()
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                            getView()?.onSessionEnd(restResponse.desc)
                                        }
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SM.value, ignoreCase = true)) {
                                            val response = restResponse.getData(
                                                    SafetyModeDrawerResponse::class.java
                                            )
                                            getView()?.onSafetyMode(response)
                                        }
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_58.getValue(), ignoreCase = true)){
                                            val response =  restResponse.getData(
                                                    InfoResponse::class.java
                                            )
                                            getView()?.onException58(response)
                                        }
                                        else getView()?.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun getBeliEmas() {
        if (urlBeliEmas == null || !isViewAttached) {
            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(urlBeliEmas, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.single())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()?.hideProgress()
                                        getView()?.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()?.hideProgress()
                                        val data = response.getData(
                                                TopupEmasResponse::class.java
                                        )
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                            getView()?.onSuccessFormBeliEmas(data)
                                        }

                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                            val onOnExceptionWH: onExceptionWH =
                                                    restResponse.getData(
                                                            onExceptionWH::class.java
                                                    )
                                            getView()?.exceptionEODEOM(onOnExceptionWH)
                                        }
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()?.hideProgress()
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                            getView()?.onSessionEnd(restResponse.desc)
                                        }
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SM.value, ignoreCase = true)) {
                                            val response = restResponse.getData(
                                                    SafetyModeDrawerResponse::class.java
                                            )
                                            getView()?.onSafetyMode(response)
                                        }
                                        else getView()?.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun getDataGrafikJual(request: GrafikEmasRequest) {
        if (urlGrafik == null || !isViewAttached) {
            return
        }
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlGrafik, request, seqNum) //function(param)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()?.onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView()?.hideProgress()

                                val response = response.getData(
                                        GrafikEmasResponse::class.java
                                )

                                getView()?.onSuccessGrafikEmas(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView()?.hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView()?.onSessionEnd(restResponse.desc) else getView()?.onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getDataRiwayatTransaksi() {
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getDataForm(urlRiwayat, seqNum) //function(param)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()?.onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView()?.hideProgress()

                                val responseRiwayat = response.getData(
                                        RiwayatTransaksiEmasResponse::class.java
                                )
                                getView()?.onSuccessRiwayatTransaksi(responseRiwayat)

                                GeneralHelper.responseChuck(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView()?.hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView()?.onSessionEnd(restResponse.desc) else getView()?.onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getDataAmbilFisikEmas() {
        if (isViewAttached) {
            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                    apiSource.getDataTanpaRequest(urlAmbilFisikEmas, seqNum).subscribeOn(
                            schedulerProvider.io()
                    ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .replay()
            compositeDisposable.add(
                    listConnectableObservable.
                    timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String) {
                                    getView()?.hideProgress()
                                    getView()?.onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView()?.hideProgress()
                                    when(response.code){
                                        RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> {
                                            val data = response.getData(InquiryAmbilFisikEmasResponse::class.java)
                                            getView()!!.onSuccessAmbilFisikEmas(data)
                                        }
                                        RestResponse.ResponseCodeEnum.RC_02.value -> {
                                            val onExceptionWH = restResponse.getData(
                                                onExceptionWH::class.java)
                                            getView()?.onException02(onExceptionWH)
                                        }
                                    }
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView()?.hideProgress()
                                    onApiError(restResponse)


                                }

                            })
            )
            listConnectableObservable.connect()
        }
    }

    override fun showBubbleDashboardEmas() {
        if (!brImoPrefRepository.emasBubble) {
            getView()?.showBubbleDashboardEmas()
            brImoPrefRepository.saveEmasBubble(true)
        }
    }

    override fun getRiwayatAmbilFisikEmas(request: RiwayatFilterRequest?) {
        if (mUrlHistoryAmbilFisikEmas == null || !isViewAttached) {
            return
        }

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlHistoryAmbilFisikEmas, request, seq) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seq) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val mutasiResponse = response.getData(RiwayatAmbilFisikEmasResponse::class.java)

                        getView()?.onSuccessGetRiwayatAmbilFisik(mutasiResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun start() {
        super.start()
    }

    override fun stop() {
        super.stop()
    }
}