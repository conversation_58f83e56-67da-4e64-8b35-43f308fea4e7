package id.co.bri.brimons.presenters.bripoin;


import id.co.bri.brimons.contract.IPresenter.bripoin.IBripoinPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.bripoin.IBripoinView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class BripoinPresenter<V extends IMvpView & IBripoinView> extends MvpPresenter<V> implements IBripoinPresenter<V> {

    public BripoinPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                            BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                            TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }
}