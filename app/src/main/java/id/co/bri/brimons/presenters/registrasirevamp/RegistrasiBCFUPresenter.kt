package id.co.bri.brimons.presenters.registrasirevamp

import id.co.bri.brimons.contract.IPresenter.registrasirevamp.IRegistrasiBCFUPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.registrasirevamp.IRegistrasiBCFUView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.RegisNoAtmRequest
import id.co.bri.brimons.models.RegisIdModel
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiBCFUPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiBCFUPresenter<V> where V : IMvpView, V : IRegistrasiBCFUView? {

    private var urlAtm: String? = null

    override fun setUrlAtm(url: String) {
        urlAtm = url
    }

    override fun sendNoAtm(regisNoAtmRequest: RegisNoAtmRequest) {
        if (urlAtm != null && isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlAtm, regisNoAtmRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisIdResponse =
                                response.getData(RegisIdModel::class.java)
                            getView().onSuccessATM(regisIdResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun regisId() {
        view?.getRegisId(brImoPrefRepository.deviceId)
    }

}