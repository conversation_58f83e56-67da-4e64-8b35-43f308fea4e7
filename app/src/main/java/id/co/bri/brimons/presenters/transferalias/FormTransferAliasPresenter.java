package id.co.bri.brimons.presenters.transferalias;



import id.co.bri.brimons.contract.IPresenter.transferalias.IFormTransferAliasPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.transferalias.IFormTransferAliasView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.FastInquiryTransferBifastRequest;
import id.co.bri.brimons.models.apimodel.request.FastInquiryTransferRequest;
import id.co.bri.brimons.models.apimodel.request.FastInquiryTransferRtgsRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryBiFastRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryRtgsRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryTransferAliasBriRequest;
import id.co.bri.brimons.models.apimodel.request.InquiryTransferAliasRequest;
import id.co.bri.brimons.models.apimodel.request.UpdateSavedRequestTfMethod;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.OperationalTimeRtgsResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.SavedResponse;
import id.co.bri.brimons.models.apimodel.response.TransferAbnormalResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormTransferAliasPresenter <V extends IMvpView & IBaseFormView & IFormTransferAliasView> extends BaseFormPresenter<V> implements IFormTransferAliasPresenter<V> {

    private static final String TAG = "FormTransferAliasPresen";
    public String mUrlPending;
    public String mUrlInquiryBri;
    public String mUrlInquiryRtgs;
    public String mUrlInquiryBifast;
    private String saveId = "";
    private String purchaseType = "";
    private String tfMethod = "";

    private boolean onLoad = false;

    public FormTransferAliasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getDataInquiry(InquiryTransferAliasRequest inquiryTransferAliasRequest, boolean isFromFastMenu) {

        if (inquiryUrl == null || !isViewAttached() || onLoad) {
            return;
        }

        if(isFromFastMenu){
            inquiryRequest = new FastInquiryTransferRequest(getFastMenuRequest(),inquiryTransferAliasRequest.getBankCode(), inquiryTransferAliasRequest.getAccount());
        } else {
            inquiryRequest = inquiryTransferAliasRequest;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        onLoad = false;
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                onLoad = false;
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                if (inquiryUrl != null && konfirmasiUrl != null)
                                    getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl,mUrlPending, isFromFastMenu);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryBri(InquiryTransferAliasBriRequest inquiryTransferAliasBriRequest, boolean isFromFastMenu) {

        if (mUrlInquiryBri == null || !isViewAttached() || onLoad) {
            return;
        }

        if(isFromFastMenu){
            inquiryRequest = new FastInquiryTransferRequest(getFastMenuRequest(),inquiryTransferAliasBriRequest.getBankCode(), inquiryTransferAliasBriRequest.getAccount());
        } else {
            inquiryRequest = inquiryTransferAliasBriRequest;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        onLoad = false;
        getCompositeDisposable()
                .add(getApiSource().getData(mUrlInquiryBri, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                onLoad = false;
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                if (mUrlInquiryBri != null && konfirmasiUrl != null)
                                    getView().onSuccessGetInquiryBri(responsebriva,  isFromFastMenu);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryRtgs(InquiryRtgsRequest inquiryRtgsRequest, boolean isFromFastMenu) {
        if (mUrlInquiryRtgs == null || !isViewAttached() || onLoad) {
            return;
        }

        if(isFromFastMenu){
            inquiryRequest = new FastInquiryTransferRtgsRequest(getFastMenuRequest(),inquiryRtgsRequest.getBankCode(), inquiryRtgsRequest.getAccount());
        } else {
            inquiryRequest = inquiryRtgsRequest;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        onLoad = true;
        getCompositeDisposable()
                .add(getApiSource().getData(mUrlInquiryRtgs, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                onLoad = false;
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessGetInquiryRtgs(responsebriva, isFromFastMenu);
                                }else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                                    OperationalTimeRtgsResponse operationalTimeRtgsResponse = response.getData(OperationalTimeRtgsResponse.class);
                                    getView().onException02Rtgs(operationalTimeRtgsResponse);
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_03.getValue())) {
                                    TransferAbnormalResponse abnormalResponse = response.getData(TransferAbnormalResponse.class);
                                    getView().onSuccessInquiry03(abnormalResponse);
                                }
                            }


                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataInquiryBiFast(InquiryBiFastRequest inquiryBiFastRequest, boolean isFromFastMenu) {
        if (mUrlInquiryBifast == null || !isViewAttached() || onLoad) {
            return;
        }


        if(isFromFastMenu){
            inquiryRequest = new FastInquiryTransferBifastRequest(getFastMenuRequest(),inquiryBiFastRequest.getBankCode(), inquiryBiFastRequest.getAccount(), inquiryBiFastRequest.getMethodBifast());
        } else {
            inquiryRequest = inquiryBiFastRequest;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        onLoad = true;
        getCompositeDisposable()
                .add(getApiSource().getData(mUrlInquiryBifast, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                onLoad = false;
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                getView().onSuccessGetInquiryBifast(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }


    @Override
    public void setUrlPending(String urlPending) {
        this.mUrlPending = urlPending;
    }

    public void setUrlInquiryRtgs(String urlInquiryRtgs) {
        this.mUrlInquiryRtgs = urlInquiryRtgs;
    }

    /**
     * @param url
     * @param savedResponse
     * @param position
     * @param type
     */
    @Override
    public void setUpdateItemTf(String url, SavedResponse savedResponse, int position, int type) {
        if (url == null || !isViewAttached() || onLoad) {
            return;
        }

        onLoad = true;
        String s = savedResponse.getValue();
        String[] str1 = s.split("\\|");


        if(str1.length > 1){
            saveId = str1[0];
            purchaseType = str1[1];

        }else{
            saveId = str1[0];
        }

        if (str1.length > 3){
            tfMethod = str1[3];
        }

        UpdateSavedRequestTfMethod updateSavedRequest = new UpdateSavedRequestTfMethod(saveId,purchaseType, tfMethod);


        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        onLoad = false;
                        getView().hideProgress();
                        getView().onSuccessUpdate(savedResponse, position, type);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onApiError(restResponse);
                    }
                }));

    }

    @Override
    public void setUrlInquiryBiFast(String urlInquiryBiFast) {
        this.mUrlInquiryBifast = urlInquiryBiFast;
    }

    @Override
    public void setUrlInquiryBri(String urlInquiryBri) {
        this.mUrlInquiryBri = urlInquiryBri;
    }
}