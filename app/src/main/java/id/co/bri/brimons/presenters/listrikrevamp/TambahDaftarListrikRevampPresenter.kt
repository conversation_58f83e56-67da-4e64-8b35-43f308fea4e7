package id.co.bri.brimons.presenters.listrikrevamp

import id.co.bri.brimons.contract.IPresenter.listrikrevamp.ITambahDaftarListrikRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.listrikrevamp.ITambahDaftarListrikRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.transferrevamp.SavedDataExistResponse
import id.co.bri.brimons.models.apimodel.response.transferrevamp.SavedDataResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class TambahDaftarListrikRevampPresenter<V>(schedulerProvider: SchedulerProvider?, compositeDisposable: CompositeDisposable?, mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?, categoryPfmSource: CategoryPfmSource?, transaksiPfmSource: TransaksiPfmSource?, anggaranPfmSource: AnggaranPfmSource?) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),

    ITambahDaftarListrikRevampPresenter<V> where V : IMvpView, V : ITambahDaftarListrikRevampView {
    private var inquiryUrl: String? = null

    override fun getDataInquiry(request: InquiryPlnRequest) {
        if (inquiryUrl == null || !isViewAttached) {
            return
        }
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getData(inquiryUrl, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()?.hideProgress()
                            val responPln = response.getData(InquiryBrivaRevampResponse::class.java)
                            getView()?.onSuccessInquiry(responPln)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onApiError(restResponse)
                        }
                    })
            )
    }

    override fun getDataInquiryAddSavedList(inquiryPlnRequest: InquiryPlnRequest) {
        if (inquiryUrl == null || !isViewAttached) {
            return
        }
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getData(inquiryUrl, inquiryPlnRequest, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse?) {
                            getView()?.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                val resData = response?.getData(SavedDataExistResponse::class.java)
                                getView().onExceptionAlreadySaved(resData?.title, resData?.subtitle)
                            } else {
                                val resData = response?.getData(SavedDataResponse::class.java)
                                getView().onSuccessGetSavedDataInquiry(resData)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onApiError(restResponse)
                        }
                    })
            )
    }

    override fun setInquiryUrl(url: String?) {
        inquiryUrl = url
    }

}