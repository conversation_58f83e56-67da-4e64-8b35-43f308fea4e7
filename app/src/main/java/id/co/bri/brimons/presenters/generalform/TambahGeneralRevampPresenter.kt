package id.co.bri.brimons.presenters.generalform

import id.co.bri.brimons.contract.IPresenter.generalform.ITambahGeneralRevampPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.generalform.ITambahGeneralRevampView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.transferrevamp.SavedDataExistResponse
import id.co.bri.brimons.models.apimodel.response.transferrevamp.SavedDataResponse
import id.co.bri.brimons.models.apimodel.util.toRestResponseCodeEnum
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequestState
import id.co.bri.brimons.util.extension.handleBaseResponseConvertData
import id.co.bri.brimons.util.extension.onError
import id.co.bri.brimons.util.extension.onLoading
import id.co.bri.brimons.util.extension.onSuccess
import io.reactivex.disposables.CompositeDisposable

class TambahGeneralRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource,
), ITambahGeneralRevampPresenter<V> where V : IMvpView, V : ITambahGeneralRevampView {
    private var mUrlInquiry: String = ""

    override fun setInquiryUrl(urlInquiry: String) {
        mUrlInquiry = urlInquiry
    }

    override fun getDataInquiry(request: Any?) {
        view.getDataWithOrWithoutRequestState(
            mUrlInquiry,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request
        ) {
            it.handleBaseResponseConvertData { restResponse -> restResponse.getData(InquiryBrivaRevampResponse::class.java) }
                .onSuccess { inquiryBrivaRevampResponse -> view.onSuccessGetInquiry(inquiryBrivaRevampResponse) }
                .onError { restResponse ->
                    when (restResponse.code.toRestResponseCodeEnum()) {
                        RestResponse.ResponseCodeEnum.RC_58 -> getView().onException58(restResponse.desc ?: "")
                        RestResponse.ResponseCodeEnum.RC_59 -> getView().onException59(restResponse.desc ?: "")
                        RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID -> getView().onBillAlreadyPaid(restResponse.desc ?: "")
                        else -> onApiError(restResponse)
                    }
                }
                .onLoading { isShow -> if (isShow) view.showProgress() else view.hideProgress() }
        }
    }

    override fun getDataInquiryAddSavedList(request: Any?) {
        view.getDataWithOrWithoutRequestState(
            mUrlInquiry,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request
        ) {
            it
                .onSuccess {restResponse->
                    when {
                        restResponse.code.equals(Constant.RE01, ignoreCase = true) -> {
                            val resData = restResponse.getData(SavedDataExistResponse::class.java)
                            view.onExceptionAlreadySaved(resData.title, resData.subtitle)
                        }
                        else -> view.onSuccessGetDataInquiryAddSavedList(restResponse.getData(SavedDataResponse::class.java))
                    }
                }
                .onError { restResponse -> onApiError(restResponse) }
                .onLoading { isShow -> if (isShow) view.showProgress() else view.hideProgress() }
        }
    }
}
