package id.co.bri.brimons.presenters.login;

import id.co.bri.brimons.contract.IPresenter.login.ILoginTambahBaruPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.login.ILoginTambahBaruView;
import id.co.bri.brimons.contract.IView.login.ILoginView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;

import io.reactivex.disposables.CompositeDisposable;

public class LoginTambahBaruPresenter<V extends IMvpView & ILoginView & ILoginTambahBaruView> extends LoginActivityPresenter<V> implements ILoginTambahBaruPresenter<V> {

    public LoginTambahBaruPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }
}