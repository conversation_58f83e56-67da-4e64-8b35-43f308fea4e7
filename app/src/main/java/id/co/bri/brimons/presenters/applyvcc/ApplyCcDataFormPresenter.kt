package id.co.bri.brimons.presenters.applyvcc

import id.co.bri.brimons.R
import id.co.bri.brimons.contract.IPresenter.applyvcc.IApplyCcDataFormPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.onboardingcc.IApplyCcDataFormView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.GeneralHelper
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.applyccrevamp.ApplyCcGetPostalCodeRequest
import id.co.bri.brimons.models.apimodel.response.applyccrevamp.ApplyCcGetPostalCodeResponse
import id.co.bri.brimons.models.applyccrevamp.toApplyCcGetPostalCodeModel
import id.co.bri.brimons.presenters.MvpPresenter
import id.co.bri.brimons.util.extension.getDataWithOrWithoutRequestState
import id.co.bri.brimons.util.extension.handleBaseResponseConvertData
import io.reactivex.disposables.CompositeDisposable

class ApplyCcDataFormPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IApplyCcDataFormPresenter<V> where V : IMvpView, V : IApplyCcDataFormView {
    override fun getDataForm() {
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_get_data_from),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
        ) {
            view.onGetDataFormSuccessState(it)
        }
    }

    override fun getPostalCode(keyword: String) {
        val request = ApplyCcGetPostalCodeRequest(keyword)
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_get_postal_code),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request,
        ) {
            view.getPostalCodeSuccessState(it.handleBaseResponseConvertData { restResponse ->
                restResponse.getData(ApplyCcGetPostalCodeResponse::class.java).toApplyCcGetPostalCodeModel()
            })
        }
    }
}