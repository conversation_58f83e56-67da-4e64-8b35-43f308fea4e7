package id.co.bri.brimons.presenters.regisSbn;

import id.co.bri.brimons.contract.IPresenter.regisEsbn.IEsbnFormPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.regisSbn.IEsbnFormView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.EsbnFormRequest;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.regisESBN.EsbnInquiryResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class EsbnFormPresenter <V extends IMvpView & IEsbnFormView> extends MvpPresenter<V> implements IEsbnFormPresenter<V> {

    public String url ;

    public EsbnFormPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onGetData(EsbnFormRequest esbnFormRequest) {
        if (url != null && isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(url,esbnFormRequest, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        EsbnInquiryResponse esbnInquiryResponse = response.getData(EsbnInquiryResponse.class);
                                        getView().onSuccessGetData(esbnInquiryResponse);
                                    }

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());

                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }
}