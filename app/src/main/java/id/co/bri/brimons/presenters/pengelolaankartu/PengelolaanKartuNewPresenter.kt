package id.co.bri.brimons.presenters.pengelolaankartu

import id.co.bri.brimons.contract.IPresenter.pengelolaankartu.IPengelolaanKartuNewPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.pengelolaankartu.IPengelolaanKartuNewView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.entity.pengelolaankartu.CardAccountEntity
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.pengelolaankartu.PengelolaanKartuSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.converter.toEntity
import id.co.bri.brimons.domain.converter.toResponse
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.activationdebit.response.SubmitActivationDebitResponse
import id.co.bri.brimons.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq
import id.co.bri.brimons.models.apimodel.response.ListPengelolaanKartuRes
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes
import id.co.bri.brimons.models.apimodel.response.pengelolaankartu.activationdebit.ProductBriefResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableSingleObserver
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class PengelolaanKartuNewPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
    private val pengelolaanKartuSource: PengelolaanKartuSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IPengelolaanKartuNewPresenter<V> where V : IMvpView, V : IPengelolaanKartuNewView {

    private var urlList: String? = null
    private var urlDetail: String? = null
    private var urlInitActivation: String? = null

    override fun setUrlCardList(urlCardList: String) {
        this.urlList = urlCardList
    }

    override fun setUrlCardDetail(urlDetail: String) {
        this.urlDetail = urlDetail
    }

    override fun setUrlInitActivation(urlInitActivation: String) {
        this.urlInitActivation = urlInitActivation
    }

    override fun getAccountWithCardNumber() {
        urlList?.let {
            if (isViewAttached()) {
                getView().showProgress()
                val seqNum = brImoPrefRepository.seqNumber

                compositeDisposable.add(
                    apiSource.getDataTanpaRequest(it, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(getView(), seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val listKartuRes = response.getData(ListPengelolaanKartuRes::class.java)
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                    getView().savePengelolaanKartu(listKartuRes)
                                } else {
                                    getView().onSuccessNoCard()
                                }
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                restResponse.let { response ->
                                    getView().apply {
                                        when (response.code) {
                                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                            RestResponse.ResponseCodeEnum.RC_12.value -> this.onException12(restResponse.desc.orEmpty(), "")
                                            else -> this.onException(restResponse.desc.orEmpty())
                                        }
                                    }
                                }
                            }
                        })
                )
            }
        }
    }

    override fun getCardDetail(detailKelolaKartuReq: DetailKelolaKartuReq) {
        urlDetail?.let {
            if (isViewAttached()) {
                getView().showProgress()
                val seqNum = brImoPrefRepository.seqNumber

                compositeDisposable.add(
                    apiSource.getData(it, detailKelolaKartuReq, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(getView(), seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val detailResponse = response.getData(DetailKelolaKartuRes::class.java)
                                getView().onSuccessCardDetail(detailResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                restResponse.let { response ->
                                    getView().apply {
                                        when (response.code) {
                                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                            RestResponse.ResponseCodeEnum.RC_12.value -> this.onException12(restResponse.desc.orEmpty(), "")
                                            else -> this.onException(restResponse.desc.orEmpty())
                                        }
                                    }
                                }
                            }
                        })
                )
            }
        }
    }

    override fun savePengelolaanKartu(pengelolaanKartuRes: ListPengelolaanKartuRes) {
        compositeDisposable.add(
            pengelolaanKartuSource.deleteAllPengelolaanKartu()
                .andThen(pengelolaanKartuSource.insertListAccountEntity(pengelolaanKartuRes.toEntity()))
                .andThen(pengelolaanKartuSource.getAllPengelolaanKartu())
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableSingleObserver<List<CardAccountEntity>>() {
                    override fun onSuccess(pengelolaanKartuEntities: List<CardAccountEntity>) {
                        val updatedResponse = pengelolaanKartuEntities.toResponse(
                            pengelolaanKartuRes.referenceNumber,
                            pengelolaanKartuRes.hasCardLess,
                            pengelolaanKartuRes.cardLessImage,
                            pengelolaanKartuRes.cardLessTitleText,
                            pengelolaanKartuRes.cardLessDecText
                        )
                        view?.onSuccessCardList(updatedResponse) // Show the updated list
                    }

                    override fun onError(e: Throwable) {
                        view?.onException(e.message ?: "Unknown Error")
                    }
                })
        )
    }

    override fun getInitActivation() {
        if (!isViewAttached || urlInitActivation?.isEmpty() == true) return

        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlInitActivation, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(message: String) {
                        view.hideProgress()
                        view.onException(message)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val productBriefResponse = response.getData(ProductBriefResponse::class.java)
                        getView().onSuccessProductBrief(productBriefResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse?) {
                        view.hideProgress()
                        if (restResponse?.code == RestResponse.ResponseCodeEnum.MAX_ATTEMPT_ACTIVATION.value) {
                            getView().onMaxRetriesReached(restResponse?.desc.orEmpty())
                        } else {
                            view.onException(restResponse?.desc.orEmpty())
                        }
                    }
                })
        )
    }

    override fun getSavedCardList(response: SubmitActivationDebitResponse) {
        compositeDisposable.add(
            pengelolaanKartuSource.insertNewCardAccount(response.newCard.toEntity().copy(isNewBadgeShown = true))
                .andThen(pengelolaanKartuSource.getAllPengelolaanKartu())
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableSingleObserver<List<CardAccountEntity>>() {
                    override fun onSuccess(list: List<CardAccountEntity>) {
                        val updatedResponse = list.toResponse(
                            referenceNumber = null,
                            hasCardLess = response.hasCardless,
                            cardLessImage = response.cardlessImage,
                            cardLessTitleText = response.cardlessTitleText,
                            cardLessDecText = response.cardlessDescText
                        )
                        updatedResponse.accounts = updatedResponse.accounts.sortedBy { !it.badgeShown }
                        view?.onSuccessCardList(updatedResponse)
                    }

                    override fun onError(e: Throwable) {
                        view?.onException(e.message ?: "Unknown Error")
                    }
                })
        )
    }

    override fun updateCardAccount(cardNumber: String) {
        compositeDisposable.add(
            pengelolaanKartuSource.updateCardAccount(cardNumber)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribe()
        )
    }
}