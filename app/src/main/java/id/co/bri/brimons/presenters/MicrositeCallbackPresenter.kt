package id.co.bri.brimons.presenters

import id.co.bri.brimons.contract.IPresenter.IMicrositeCallbackPresenter
import id.co.bri.brimons.contract.IView.IMicrositeCallbackView
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.revamppulsa.FastOmniPaketConfirmationRequest
import id.co.bri.brimons.models.apimodel.request.revamppulsa.OmniPaketConfirmationRequest
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimons.models.apimodel.response.RestResponse
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class MicrositeCallbackPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IMicrositeCallbackPresenter<V> where V : IMvpView?, V : IMicrositeCallbackView? {

    var request : Any? = null

    override fun getConfirmationPaketData(paymentCode: String, providerId: String, urlConfirmation: String, phoneNumber:  String, saveAs: String, isFromFast: Boolean) {
        if (urlConfirmation.isEmpty() || !isViewAttached) {
            return
        }

        if (view != null) {
            //initiate param with getter from view

            request = if (isFromFast) {
                FastOmniPaketConfirmationRequest(getFastMenuRequest(), paymentCode, providerId, phoneNumber, saveAs)
            } else {
                OmniPaketConfirmationRequest(paymentCode, providerId, phoneNumber, saveAs)
            }
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable = apiSource.getData(urlConfirmation, request, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onExceptionMicrosite(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val generalConfirmationResponse = response.getData(GeneralConfirmationResponse::class.java)
                        getView()!!.onSuccessGetInquiryPaketData(generalConfirmationResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()!!.onSessionEnd(restResponse.desc)
                        else
                            getView()!!.onExceptionMicrosite(restResponse.desc)
                    }
                })
            compositeDisposable.add(disposable)
        }
    }

}