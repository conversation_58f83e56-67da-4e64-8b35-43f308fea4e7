package id.co.bri.brimons.presenters.qrtransfer;


import id.co.bri.brimons.contract.IPresenter.qrtransfer.IQrTransferCodeListPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.qrtransfer.IQrTransferCodeListView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.QrFormReq;
import id.co.bri.brimons.models.apimodel.request.QrGenerateReq;
import id.co.bri.brimons.models.apimodel.response.QrBenefResponse;
import id.co.bri.brimons.models.apimodel.response.QrGenerateResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class QrTransferCodeListPresenter<V extends IMvpView & IQrTransferCodeListView>
        extends MvpPresenter<V> implements IQrTransferCodeListPresenter<V> {

    protected String formUrl;
    protected String generateUrl;
    protected Object requestObject;

    public QrTransferCodeListPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getData(boolean isNewQr, String deletedMpan) {
        if (formUrl == null || !isViewAttached()) {
            return;
        }

        if (deletedMpan == null || deletedMpan.isEmpty()) {
            deletedMpan = "";
        }

        requestObject = new QrFormReq(isNewQr, deletedMpan);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(formUrl, requestObject, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeleton(true);
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().isHideSkeleton(true);
                                QrBenefResponse qrResponse = response.getData(QrBenefResponse.class);
                                getView().onSuccesGetData(qrResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeleton(true);
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getDataPayment(String pin, String account, String amount, String deletedMpan) {
        if (isViewAttached()) {
            //initiate param with getter from view
            getView().showProgress();

            requestObject = new QrGenerateReq(account, pin, amount, deletedMpan);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(generateUrl, requestObject, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().isHideSkeleton(true);
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().isHideSkeleton(true);
                            getView().hideProgress();
                            QrGenerateResponse generateQrResponse = response.getData(QrGenerateResponse.class);
                            getView().onGenerateRequest(generateQrResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().isHideSkeleton(true);
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold);
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setGenerateUrl(String generateUrl) {
        this.generateUrl = generateUrl;
    }
}