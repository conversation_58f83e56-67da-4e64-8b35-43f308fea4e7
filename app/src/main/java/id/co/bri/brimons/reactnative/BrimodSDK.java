package id.co.bri.brimons.reactnative;

import static id.co.bri.brimons.models.apimodel.response.RestResponse.CODE_SESSION_END;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.brimodsdk.RNBrimodSDKDelegate;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.domain.converter.MapperHelper;
import id.co.bri.brimons.domain.helpers.BrimoGeneralErrorView;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.security.MyCryptStatic;
import id.co.bri.brimons.ui.activities.DashboardIBActivity;
import id.co.bri.brimons.ui.activities.ReactNativeActivity;

/**
 * BrimodSDK
 * <p>
 * Main React Native bridge for BRImo Android. This class provides API calls, navigation, event emitters,
 * and other utilities to ensure consistency with the iOS implementation. All main methods are accessible from JS.
 * <p>
 * Main features:
 * - requestApiCall: Call backend APIs from React Native
 * - Navigation to native and React Native screens
 * - Two-way event emitter (send data to React Native & native)
 * - Dismiss React Native screens from JS
 * - Helper for dynamic JSON parsing
 */
public class BrimodSDK implements RNBrimodSDKDelegate {
    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private final ApiSource apiSource;
    private final BRImoPrefSource prefSource;
    private static Context context = null;

    private static final String TAG = "BrimodSDK";
    public static final String COMPONENT_NAME_KEY = "component_name";
    public static final String INITIAL_PROPS_KEY = "initial_props";
    public static final String BUNDLE_NAME_KEY = "bundle_name";
    public static final String ARG_MODULE_NAME = "module_name";

    // Bundle Name
    public static final String BUNDLE_HOMEPAGE = "homepage";
    public static final String BUNDLE_PORTFOLIO = "portfolio";

    // Central registry of registered bundles for OTA
    public static final List<String> REGISTERED_BUNDLES = Arrays.asList(BUNDLE_HOMEPAGE, BUNDLE_PORTFOLIO);

    // Default Module Name
    public static final String DEFAULT_MODULE_HOMEPAGE = "BrimoHomepage";
    public static final String DEFAULT_MODULE_PORTFOLIO = "BrimoPortfolio";

    public static boolean isForDemo() {
        // Demo/Development Mode Config
        // Default is true, can be changed via setter
        return true;
    }

    // Handle CODE_SESSION_END
    private static final AtomicBoolean isSessionEndHandled = new AtomicBoolean(false);
    public static void resetSessionEndHandled() {
        isSessionEndHandled.set(false);
    }

    public BrimodSDK(Context context, ApiSource apiSource, BRImoPrefSource prefSource) {
        BrimodSDK.context = context;
        this.apiSource = apiSource;
        this.prefSource = prefSource;
    }

    @Override
    public void requestApiCall(Map<String, Object> apiObject, Map<String, Object> payload, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "===== REQUEST API CALL =====");

                // Extract URL from apiObject (similar to iOS implementation)
                String encryptedUrl = null;
                if (apiObject != null && apiObject.containsKey("android")) {
                    Object androidValue = apiObject.get("android");
                    if (androidValue instanceof String) {
                        encryptedUrl = (String) androidValue;
                    } else {
                        Log.e(TAG, "Invalid android value type: " + (androidValue != null ? androidValue.getClass().getName() : "null"));
                        onError.onError("INVALID_API_OBJECT", "Android value is not a string", null);
                        return;
                    }
                }

                if (encryptedUrl == null || encryptedUrl.isEmpty()) {
                    onError.onError("INVALID_API_OBJECT", "Api Object not found or invalid", null);
                    return;
                }

                Log.d(TAG, "===== API " + encryptedUrl + " =====");

                Object requestData;
                if (payload == null || payload.isEmpty()) {
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.addProperty("", "");
                    requestData = jsonObject;
                } else {
                    requestData = new Gson().toJsonTree(payload);
                }

                Gson gson = new Gson();
                String seqNum = prefSource.getSeqNumber();
                io.reactivex.Observable<String> apiCall;
                if (payload == null || payload.isEmpty()) {
                    apiCall = apiSource.getDataTanpaRequest(encryptedUrl, seqNum);
                } else {
                    apiCall = apiSource.getData(encryptedUrl, requestData, seqNum);
                }
                apiCall
                        .timeout(30, TimeUnit.SECONDS)
                        .subscribeOn(io.reactivex.schedulers.Schedulers.io())
                        .observeOn(io.reactivex.android.schedulers.AndroidSchedulers.mainThread())
                        .subscribe(
                                response -> {
                                    try {
                                        RestResponse restResponse = MapperHelper.stringToRestResponse(response, seqNum);
                                        if (restResponse == null) {
                                            onError.onError("API_ERROR", "Response validation failed", null);
                                            return;
                                        }

                                        Log.i(TAG, "Response Code: " + restResponse.getCode(), null);
                                        if ("00".equals(restResponse.getCode()) || "01".equals(restResponse.getCode())) {
                                            try {
                                                String jsonResponse = gson.toJson(restResponse.getData());
                                                onSuccess.onSuccess(jsonResponse);
                                            } catch (Exception e) {
                                                Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                                                onError.onError("JSON_ERROR", "Failed to serialize response data", e);
                                            }
                                        } else if (CODE_SESSION_END.equals(restResponse.getCode())) {
                                            if (isSessionEndHandled.compareAndSet(false, true)) {
                                                // Use Handler to ensure UI operations run on main thread
                                                Handler mainHandler = new Handler(Looper.getMainLooper());
                                                mainHandler.post(() -> {
                                                    try {
                                                        // Check if currentActivity is FragmentActivity
                                                        if (currentActivity instanceof FragmentActivity) {
                                                            FragmentActivity fragmentActivity = (FragmentActivity) currentActivity;
                                                            BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), false);
                                                        } else {
                                                            // Fallback: try to get the current activity from context
                                                            if (context instanceof FragmentActivity) {
                                                                FragmentActivity fragmentActivity = (FragmentActivity) context;
                                                                BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), false);
                                                            } else {
                                                                Log.w(TAG, "Cannot show session end dialog: no FragmentActivity available");
                                                                onError.onError("API_ERROR", "Cannot show session end dialog: no FragmentActivity available", null);
                                                            }
                                                        }
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "Error showing session end dialog: " + e.getMessage(), e);
                                                        onError.onError("API_ERROR", e.getMessage(), null);
                                                    }
                                                });
                                            }
                                            String errorMessage = "API returned error code: " + restResponse.getCode() + " - " + restResponse.getDesc();
                                            Log.i(TAG, "Error message: " + errorMessage, null);
                                            onError.onError("UNAUTHORIZED", errorMessage, null);
                                        } else {
                                            if (!("12".equals(restResponse.getCode()))) {
                                                // Use Handler to ensure UI operations run on main thread
                                                Handler mainHandler = new Handler(Looper.getMainLooper());
                                                mainHandler.post(() -> {
                                                    try {
                                                        // Check if currentActivity is FragmentActivity
                                                        if (currentActivity instanceof FragmentActivity) {
                                                            FragmentActivity fragmentActivity = (FragmentActivity) currentActivity;
                                                            BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), true);
                                                        } else {
                                                            // Fallback: try to get the current activity from context
                                                            if (context instanceof FragmentActivity) {
                                                                FragmentActivity fragmentActivity = (FragmentActivity) context;
                                                                BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), true);
                                                            } else {
                                                                Log.w(TAG, "Cannot show session end dialog: no FragmentActivity available");
                                                                onError.onError("API_ERROR", "Cannot show session end dialog: no FragmentActivity available", null);
                                                            }
                                                        }
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "Error showing session end dialog: " + e.getMessage(), e);
                                                        onError.onError("API_ERROR", e.getMessage(), null);
                                                    }
                                                });
                                            }
                                            String errorMessage = "API returned error code: " + restResponse.getCode() + " - " + restResponse.getDesc();
                                            Log.i(TAG, "Error message: " + errorMessage, null);
                                            onError.onError("API_ERROR", errorMessage, null);
                                        }
                                    } catch (Exception e) {
                                        Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                                        onError.onError("JSON_ERROR", "Failed to parse API response: " + e.getMessage(), e);
                                    }
                                },
                                error -> {
                                    String errorMessage = error != null ? error.getMessage() : "Unknown network error";
                                    Log.i(TAG, "Error message: " + errorMessage, null);
                                    onError.onError("NETWORK_ERROR", errorMessage, error);
                                }
                        );
            } catch (Exception e) {
                Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                onError.onError("BRIDGE_ERROR", "Bridge execution error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void selectTabBarItem(int index, Map<String, Object> params, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.d(TAG, "===== SELECT TAB BAR ITEM index: " + index + ", params: " + params + " =====");
        if (currentActivity instanceof DashboardIBActivity) {
            ((DashboardIBActivity) currentActivity).selectTabByIndex(index, params);
            onSuccess.onSuccess("onSuccess");
        } else {
            onError.onError("TABBAR_NOT_FOUND", "TabBarController not found", null);
        }
    }

    @Override
    public void sendDataToNative(String name, Map<String, Object> data) {
        Log.d(TAG, "===== SEND DATA TO NATIVE, name: " + name + ", data: " + data + " =====");
        Intent intent = new Intent(name);
        if (data != null) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue().toString());
            }
        }
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    @Override
    public void dismiss(OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.d(TAG, "===== DISMISS =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity instanceof ReactNativeActivity) {
                currentActivity.finish();
                onSuccess.onSuccess("onSuccess");
            } else {
                onError.onError("DISMISS ERROR", "Top View Controller not found", null);
            }
        });
    }

    @Override
    public void showBottomSheet(String type, String image, String title, String subTitle, String buttonTitle, String buttonPrimaryTitle, String buttonSecondaryTitle, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.d(TAG, "===== SHOW BOTTOM SHEET, type: " + type + ", title: " + title + " =====");

        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                // Check if currentActivity is FragmentActivity
                if (currentActivity instanceof FragmentActivity) {
                    FragmentActivity fragmentActivity = (FragmentActivity) currentActivity;

                    // Always use BrimoGeneralErrorView.showErrorBottomDialog
                    // If type is empty or null, it will use custom parameters provided
                    BrimoGeneralErrorView.showErrorBottomDialog(
                            fragmentActivity,
                            type,
                            "",
                            () -> {
                                // onAction callback
                                if (onSuccess != null) {
                                    onSuccess.onSuccess("Bottom sheet action completed");
                                }
                            },
                            () -> {
                                // onDismiss callback
                                if (onSuccess != null) {
                                    onSuccess.onSuccess("Bottom sheet dismissed");
                                }
                            },
                            image, // customImage
                            title, // customTitle
                            subTitle, // customSubTitle
                            buttonTitle // customButtonTitle
                    );
                } else {
                    Log.e(TAG, "Current activity is not FragmentActivity, cannot show bottom sheet");
                    if (onError != null) {
                        onError.onError("ACTIVITY_ERROR", "Current activity is not FragmentActivity", null);
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Show bottom sheet error: " + e.getMessage(), e);
                if (onError != null) {
                    onError.onError("BOTTOM_SHEET_ERROR", "Failed to show bottom sheet: " + e.getMessage(), e);
                }
            }
        });
    }

    @Override
    public void refetchBundle(String bundleName, String appName, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.d(TAG, "===== REFETCH BUNDLE, bundleName: " + bundleName + ", appName: " + appName + " =====");

        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                // Check if currentActivity is DashboardIBActivity
                if (currentActivity instanceof DashboardIBActivity) {
                    DashboardIBActivity dashboardActivity = (DashboardIBActivity) currentActivity;
                    dashboardActivity.triggerOTADownload(bundleName);
                    onSuccess.onSuccess("Bundle refetch initiated for: " + bundleName);
                } else {
                    // Try to find DashboardIBActivity from context
                    if (context instanceof DashboardIBActivity) {
                        DashboardIBActivity dashboardActivity = (DashboardIBActivity) context;
                        dashboardActivity.triggerOTADownload(bundleName);
                        onSuccess.onSuccess("Bundle refetch initiated for: " + bundleName);
                    } else {
                        Log.e(TAG, "DashboardIBActivity not found, cannot trigger OTA download");
                        onError.onError("DASHBOARD_NOT_FOUND", "DashboardIBActivity not found", null);
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Refetch bundle error: " + e.getMessage(), e);
                onError.onError("REFETCH_ERROR", "Failed to refetch bundle: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void push(Map<String, Object> navigateObject, Map<String, Object> params, Activity currentActivity) {
        Log.d(TAG, "===== PUSH " + navigateObject + " =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                String activityName = extractActivityName(navigateObject, params, currentActivity);
                if (activityName == null || activityName.isEmpty()) {
                    Log.e(TAG, "ViewController not found");
                    return;
                }
                openNativeActivity(activityName, params);
            } catch (Exception e) {
                Log.e(TAG, "Push navigation error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void present(Map<String, Object> navigateObject, Map<String, Object> params, Activity currentActivity) {
        Log.d(TAG, "===== PRESENT " + navigateObject + " =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                String activityName = extractActivityName(navigateObject, params, currentActivity);
                if (activityName == null || activityName.isEmpty()) {
                    Log.e(TAG, "ViewController not found");
                    return;
                }

                // For present, we create a new activity with NEW_TASK flag
                Intent intent = new Intent(currentActivity != null ? currentActivity : context,
                        Class.forName(activityName));
                if (params != null) {
                    Bundle bundle = mapToBundle(params);
                    intent.putExtras(bundle);
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                if (currentActivity != null) {
                    currentActivity.startActivity(intent);
                } else {
                    context.startActivity(intent);
                }
            } catch (Exception e) {
                Log.e(TAG, "Present navigation error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void pop(Activity currentActivity) {
        Log.d(TAG, "===== POP =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity != null) {
                currentActivity.finish();
            }
        });
    }

    @Override
    public void popToRoot(Activity currentActivity) {
        Log.d(TAG, "===== POP TO ROOT =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity != null) {
                Intent intent = new Intent(currentActivity, DashboardIBActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                currentActivity.startActivity(intent);
                currentActivity.finish();
            }
        });
    }

    @Override
    public void popToScreen(Map<String, Object> navigateObject, Activity currentActivity) {
        Log.d(TAG, "===== POP TO SCREEN " + navigateObject + " =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                String activityName = extractActivityName(navigateObject, null, currentActivity);
                if (activityName == null || activityName.isEmpty()) {
                    Log.e(TAG, "ViewController not found");
                    return;
                }

                // For popToScreen, we need to find the target activity in the back stack
                // This is a simplified implementation - in a real app you might need more complex logic
                Intent intent = new Intent(currentActivity != null ? currentActivity : context,
                        Class.forName(activityName));
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                if (currentActivity != null) {
                    currentActivity.startActivity(intent);
                } else {
                    context.startActivity(intent);
                }
            } catch (Exception e) {
                Log.e(TAG, "PopToScreen navigation error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void getValueSharePref(String name, String key, boolean isShouldDecrypt, OnSuccessCallback onSuccess, OnErrorCallback onError) {
        try {
            Log.d(TAG, "===== GET VALUE SHARE PREF, name: " + name + ", key: " + key + " =====");

            if (context == null) {
                onError.onError("CONTEXT_ERROR", "Context is null", null);
                return;
            }

            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            String value = sp.getString(key, "");
            Log.d(TAG, "getValueSharePref: valueEncrypt=" + value);
            if (isShouldDecrypt && !value.isEmpty()) {
                try {
                    value = MyCryptStatic.decryptAsBase64(value);
                } catch (Exception e) {
                    Log.e(TAG, "decrypt error: " + e.getMessage(), e);
                    value = "";
                }
            }

            Log.d(TAG, "getValueSharePref: valueDecrypt=" + value);
            onSuccess.onSuccess(value);
        } catch (Exception e) {
            Log.e(TAG, "getValueSharePref error: " + e.getMessage(), e);
            onError.onError("SHAREDPREF_ERROR", e.getMessage(), e);
        }
    }

    @Override
    public void saveValueSharePref(String name, String key, String value, boolean isShouldEncrypt) {
        try {
            Log.d(TAG, "===== SAVE DATA SHARE PREF, name: " + name + ", key: " + key + ", value: " + value + " =====");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot save shared preference");
                return;
            }

            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            String toSave = value;
            if (isShouldEncrypt && value != null && !value.isEmpty()) {
                try {
                    toSave = MyCryptStatic.encryptAsBase64(value);
                } catch (Exception e) {
                    Log.e(TAG, "encrypt error: " + e.getMessage(), e);
                    toSave = value;
                }
            }
            editor.putString(key, toSave);
            editor.apply();
            Log.d(TAG, "saveDataSharePref: apply() called");
        } catch (Exception e) {
            Log.e(TAG, "saveDataSharePref error: " + e.getMessage(), e);
        }
    }

    @Override
    public void removeValueSharePref(String name, String key) {
        try {
            Log.d(TAG, "===== REMOVE DATA SHARE PREF, name: " + name + ", key: " + key + " =====");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot remove shared preference");
                return;
            }

            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            editor.remove(key);
            editor.apply();
            Log.d(TAG, "removeDataSharePref: apply() called");
        } catch (Exception e) {
            Log.e(TAG, "removeDataSharePref error: " + e.getMessage(), e);
        }
    }

    public static void navigateToReact(String bundleName, String appName, Map<String, Object> params, Activity currentActivity) {
        try {
            bundleName.toLowerCase();
            Log.d(TAG, "===== NAVIGATE TO REACT NATIVE, bundleName: " + bundleName + ", appName: " + appName + " ====="+ ", params: " + params + " =====");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot navigate to React Native");
                return;
            }

            Intent intent = new Intent(currentActivity != null ? currentActivity : context, ReactNativeActivity.class);
            intent.putExtra(COMPONENT_NAME_KEY, appName);
            intent.putExtra(BUNDLE_NAME_KEY, bundleName);
            if (params != null) {
                Bundle bundle = mapToBundle(params);
                intent.putExtra(INITIAL_PROPS_KEY, bundle);
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (currentActivity != null) {
                currentActivity.startActivity(intent);
            } else {
                context.startActivity(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Navigate to React error: " + e.getMessage(), e);
        }
    }

    private String extractActivityName(Map<String, Object> navigateObject, Map<String, Object> params, Activity currentActivity) {
        if (navigateObject == null) return null;

        try {
            // Check if it's React Native navigation (bundleName + appName)
            if (navigateObject.containsKey("bundleName") && navigateObject.containsKey("appName")) {
                Object bundleNameObj = navigateObject.get("bundleName");
                Object appNameObj = navigateObject.get("appName");

                if (bundleNameObj instanceof String && appNameObj instanceof String) {
                    String appName = (String) appNameObj;
                    String bundleName = ((String) bundleNameObj).toLowerCase();

                    // Navigate to React Native activity
                    navigateToReact(bundleName, appName, params, currentActivity);
                    return null; // Return null since we're handling React Native navigation
                } else {
                    Log.e(TAG, "Invalid bundleName or appName type: bundleName=" +
                            (bundleNameObj != null ? bundleNameObj.getClass().getName() : "null") +
                            ", appName=" + (appNameObj != null ? appNameObj.getClass().getName() : "null"));
                }
            }

            // Check if it's native navigation (activityName)
            if (navigateObject.containsKey("activityName")) {
                Object activityNameObj = navigateObject.get("activityName");
                if (activityNameObj instanceof String activityName) {
                    if (!activityName.isEmpty()) {
                        return activityName;
                    }
                } else {
                    Log.e(TAG, "Invalid activityName type: " +
                            (activityNameObj != null ? activityNameObj.getClass().getName() : "null"));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error extracting activity name: " + e.getMessage(), e);
        }

        return null;
    }

    private void openNativeActivity(String activityName, Map<String, Object> params) {
        try {
            if (activityName == null || activityName.isEmpty()) return;
            Class<?> clazz = Class.forName(activityName);
            if (!Activity.class.isAssignableFrom(clazz)) return;
            Intent intent = new Intent(context, clazz);
            if (params != null) {
                Bundle bundle = mapToBundle(params);
                if (bundle != null) {
                    intent.putExtras(bundle);
                }
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Log.w(TAG, Objects.requireNonNull(e.getMessage()));
        }
    }

    private static Bundle mapToBundle(Map<String, Object> map) {
        Bundle bundle = new Bundle();
        if (map == null) return bundle;

        try {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                Object value = entry.getValue();
                String key = entry.getKey();

                if (key == null) {
                    Log.w(TAG, "Skipping null key in mapToBundle");
                    continue;
                }

                if (value instanceof Integer) {
                    bundle.putInt(key, (Integer) value);
                } else if (value instanceof Boolean) {
                    bundle.putBoolean(key, (Boolean) value);
                } else if (value instanceof Double) {
                    bundle.putDouble(key, (Double) value);
                } else if (value instanceof Long) {
                    bundle.putLong(key, (Long) value);
                } else if (value instanceof String) {
                    bundle.putString(key, (String) value);
                } else {
                    // For other types, convert to string
                    bundle.putString(key, value != null ? value.toString() : null);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error converting map to bundle: " + e.getMessage(), e);
        }

        return bundle;
    }
}