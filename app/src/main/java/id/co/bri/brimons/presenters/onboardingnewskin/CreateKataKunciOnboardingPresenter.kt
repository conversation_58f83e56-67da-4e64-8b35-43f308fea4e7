package id.co.bri.brimons.presenters.onboardingnewskin

import id.co.bri.brimons.contract.IPresenter.onboardingnewskin.ICreateKataKunciOnboardingPresenter
import id.co.bri.brimons.contract.IView.informasirekening.ICreateKataKunciOnboardingView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class CreateKataKunciOnboardingPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ICreateKataKunciOnboardingPresenter<V>
        where V : ICreateKataKunciOnboardingView {

    private var urlValidatePassword = ""
    private var urlValidateOldPassword = ""

    override fun setValidateKataKunciUrl(url: String) {
        urlValidatePassword = url
    }

    override fun setValidateOldKataKunciUrl(url: String) {
        urlValidateOldPassword = url
    }

    override fun confirmKataKunci() {
        val newPassword = view.getNewObserveNewKataKunci()
        val confirmPassword = view.getConfirmObserveNewKataKunci()

        if (newPassword == confirmPassword) {
            view.onKataKunciConfirmationSuccess()
        } else {
            view.onKataKunciConfirmationFailed()
        }
    }

    override fun isInputValid(): Boolean {
        val password = view.getNewObserveNewKataKunci()
        val confirm = view.getConfirmObserveNewKataKunci()

        if (password.length < 8) {
            return false
        }

        if (password.contains(" ")) {
            return false
        }

        if (password != confirm) {
            return false
        }

        return true
    }

    override fun onCreateKataKunciSubmit() {
        if (!isViewAttached || !isInputValid() || urlValidatePassword.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlValidatePassword, "", seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                    }

                    override fun onFailureHttp(errorMessage: String) {

                    }
                })
        )
    }
}
