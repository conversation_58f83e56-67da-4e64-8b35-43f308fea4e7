package id.co.bri.brimons.presenters.openaccount;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.openaccount.IKonfirmasiGeneralOpenAccountPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.openaccount.IKonfirmasiGeneralOpenAccountView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.GeneralHelper;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PaymentGeneralOpenAccountRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimons.models.apimodel.response.PendingResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiGeneralOpenAccountPresenter <V extends IMvpView & IKonfirmasiGeneralOpenAccountView> extends MvpPresenter<V> implements IKonfirmasiGeneralOpenAccountPresenter<V> {

    private static final String TAG = "KonfirmasiGeneralOpenAc";

    protected String urlPayment;

    public KonfirmasiGeneralOpenAccountPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataPayment(String pin, GeneralConfirmationResponse generalConfirmationResponse) {
        if(isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            PaymentGeneralOpenAccountRequest paymentGeneralOpenAccountRequest = new PaymentGeneralOpenAccountRequest(generalConfirmationResponse.getReferenceNumber(),pin,generalConfirmationResponse.getPfmCategory());
            getCompositeDisposable().add(getApiSource().getData(urlPayment,paymentGeneralOpenAccountRequest, seq)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seq) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                getView().onException01(response.getDesc());
                            }
                            else{
                                PendingResponse pendingResponse = response.getData(PendingResponse.class);
//                                if(pendingResponse.getImmediatelyFlag())
//                                    onSaveTransaksiMasukPfm(generateTransaksiModel(
//                                            generalConfirmationResponse.getPfmCategory(),
//                                            generalConfirmationResponse.getPfm_amount_masuk().longValue(),
//                                            generalConfirmationResponse.getReferenceNumber(),
//                                            generalConfirmationResponse.getPfm_description_masuk(),
//                                            DbConfig.TRX_IN)
//                                    );
//                                onSaveTransaksiPfm(generateTransaksiModel(
//                                        generalConfirmationResponse.getPfm_category_keluar(),
//                                        generalConfirmationResponse.getPfm_amount_keluar().longValue(),
//                                        generalConfirmationResponse.getReferenceNumber(),
//                                        generalConfirmationResponse.getPfm_description_keluar(),
//                                        DbConfig.TRX_OUT)
//                                );
                                getView().onSuccessGetPayment(pendingResponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException93(restResponse.getDesc());
                            }
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {


                        }

                        @Override
                        public void onError(Throwable e) {
                            if (!GeneralHelper.isProd()){
                                Log.e(TAG, "onError: ", e);
                            }

                        }
                    })
            );
        }
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName,String trxType) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    trxType,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            if (!GeneralHelper.isProd()){
                Log.e(TAG, "generateTransaksiModel: ", e);
            }
        }

        return transaksi;
    }

    public void onSaveTransaksiMasukPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {
                        }

                        @Override
                        public void onError(Throwable e) {
                            if (!GeneralHelper.isProd()){
                                Log.e(TAG, "onError: ", e);
                            }

                        }
                    })
            );
        }
    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

}