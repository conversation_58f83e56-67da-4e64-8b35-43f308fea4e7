package id.co.bri.brimons.presenters.qrmpm

import id.co.bri.brimons.contract.IPresenter.qrmpm.IQrMPMPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.qrmpm.IQrMPMView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserver
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.nfcpayment.GeneratePayloadNfcRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.nfcpayment.NfcPayloadResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class QrMPMPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IQrMPMPresenter<V> where V : IMvpView, V : IQrMPMView {

    private var urlDataPayloadNfc: String = ""

    override fun setUrlGetDataPayload(url: String) {
        this.urlDataPayloadNfc = url
    }

    override fun getDataPayloadNfc(pin: String, isFromFastmenu: Boolean) {
        if (view != null) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val tokenKey = brImoPrefRepository.tokenKey
            val username = brImoPrefRepository.username
            var request: GeneratePayloadNfcRequest? = null

            if (isFromFastmenu) {
                request = GeneratePayloadNfcRequest(username, tokenKey, pin, "", "", "");
            } else {
                request = GeneratePayloadNfcRequest("", "", pin, "", "", "");
            }

            compositeDisposable.add(
                apiSource.getData(urlDataPayloadNfc, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            view.hideProgress()
                            view.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(NfcPayloadResponse::class.java)
                            getView().onSuccessGetNfcPayload(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            view.hideProgress()
                            onApiError(restResponse)
                        }
                    })
            )
        }
    }
}