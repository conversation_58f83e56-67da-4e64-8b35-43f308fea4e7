package id.co.bri.brimons.presenters.voiceassistant

import id.co.bri.brimons.contract.IPresenter.voiceassistant.IVoiceAssistantPresenter
import id.co.bri.brimons.contract.IView.IMvpView
import id.co.bri.brimons.contract.IView.voiceassistant.IVoiceAssistantView
import id.co.bri.brimons.data.api.ApiSource
import id.co.bri.brimons.data.api.observer.ApiObserverVoiceAssistant
import id.co.bri.brimons.data.preference.BRImoPrefSource
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimons.data.repository.category.CategoryPfmSource
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimons.domain.config.AppConfig
import id.co.bri.brimons.domain.config.Constant
import id.co.bri.brimons.domain.config.VoiceAssistantConfig
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimons.models.apimodel.request.voiceassistant.VoiceAssistantRequest
import id.co.bri.brimons.models.apimodel.response.RestResponse
import id.co.bri.brimons.models.apimodel.response.voiceassistant.VoiceAssistantResponse
import id.co.bri.brimons.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class VoiceAssistantPresenter<V> @Inject constructor(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource,
), IVoiceAssistantPresenter<V> where V : IMvpView?, V : IVoiceAssistantView? {

    private var urlVoiceAssistant: String? = null

    override fun getTokenKey(): String {
        return brImoPrefRepository.tokenKey
    }

    override fun getUsername(): String {
        return brImoPrefRepository.username
    }

    override fun getNickname(): String {
        return brImoPrefRepository.nickname
    }

    override fun setUrlVoiceAssistant(url: String) {
        urlVoiceAssistant = url
    }

    override fun sendTextToVoiceAssistant(voiceAssistantRequest: VoiceAssistantRequest) {
        if (!isViewAttached || urlVoiceAssistant.isNullOrEmpty()) {
            return
        }

        val seqNumber = brImoPrefRepository.seqNumber
        view?.onWaitingResponseFromVoiceAssistant(true, voiceAssistantRequest.action == VoiceAssistantConfig.CHAT)
        compositeDisposable.addAll(
            apiSource.getData(urlVoiceAssistant, voiceAssistantRequest, seqNumber)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverVoiceAssistant(view, seqNumber) {
                    override fun onFailureHttp(type: String) {
                        getView()?.onWaitingResponseFromVoiceAssistant(false, voiceAssistantRequest.action == VoiceAssistantConfig.CHAT)
                        getView()?.onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val voiceAssistantResponse = response.getData(VoiceAssistantResponse::class.java)

                        getView()?.onWaitingResponseFromVoiceAssistant(false, voiceAssistantRequest.action == VoiceAssistantConfig.CHAT)
                        if (response.code == Constant.RE_ANSWER_VOICE) {
                            getView()?.onShowingTextOnly(voiceAssistantResponse)
                        } else {
                            when (voiceAssistantResponse.action) {
                                VoiceAssistantConfig.SAVED_LIST -> getView()?.onShowingSavedRecipientList(voiceAssistantResponse)
                                VoiceAssistantConfig.CHECK_BALANCE -> getView()?.onCheckBalance(voiceAssistantResponse)
                                VoiceAssistantConfig.TRANSFER_METHODS -> getView()?.onShowingTransferMethod(voiceAssistantResponse)
                                VoiceAssistantConfig.ALL_RECIPIENT -> getView()?.onShowingSavedRecipientList(voiceAssistantResponse)
                                VoiceAssistantConfig.HOME_PAGE -> getView()?.onShowingInitView(voiceAssistantResponse)
                                VoiceAssistantConfig.INPUT_PIN_TRANSFER -> getView()?.onRequestPIN(voiceAssistantResponse)
                                VoiceAssistantConfig.INPUT_PIN_BALANCE -> getView()?.onRequestPIN(voiceAssistantResponse)
                                VoiceAssistantConfig.RECIPIENT_DETAIL -> getView()?.onRequestAmountTransfer(voiceAssistantResponse)
                                VoiceAssistantConfig.COMPLETE_TRANSFER -> getView()?.onCompleteTransfer(voiceAssistantResponse)
                                VoiceAssistantConfig.TRANSFER_CONFIRMATION -> getView()?.onShowingConfirmationTransfer(voiceAssistantResponse)
                                else -> getView()?.onShowingTextOnly(voiceAssistantResponse)
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.onWaitingResponseFromVoiceAssistant(false, voiceAssistantRequest.action == VoiceAssistantConfig.CHAT)

                        when (restResponse.code) {
                            Constant.RE_ANSWER_PIN_INVALID -> getView()?.onExceptionVoiceAssistantAnswerPINInvalid(restResponse.desc)
                            Constant.RE_ANSWER_ACCOUNT_BLOCK, Constant.RE99 -> getView()?.onExceptionVoiceAssistantAnswerAccountBlock(restResponse.desc)
                            Constant.RE_ANSWER_ERROR, Constant.RE12, Constant.RE01, Constant.RE02 -> getView()?.onExceptionVoiceAssistantAnswerError(restResponse.desc)
                            Constant.RE_TRX_EXPIRED -> getView()?.onExceptionVoiceAssistant93(restResponse.desc)
                            else -> getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

}