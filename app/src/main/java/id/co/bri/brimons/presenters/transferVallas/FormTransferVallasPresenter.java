package id.co.bri.brimons.presenters.transferVallas;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.transferVallas.IFormTransferVallasPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.base.IBaseFormView;
import id.co.bri.brimons.contract.IView.transferVallas.IFormTransferVallasView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserveFormKonversiValas;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.InquiryVallasRequest;
import id.co.bri.brimons.models.apimodel.response.onExceptionWH;
import id.co.bri.brimons.models.apimodel.response.InquiryTransferVallasResponse;
import id.co.bri.brimons.models.apimodel.response.RekValasListResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.base.BaseFormPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class FormTransferVallasPresenter<V extends IMvpView & IBaseFormView & IFormTransferVallasView> extends BaseFormPresenter<V> implements IFormTransferVallasPresenter<V> {

    private static final String TAG = "FormTransferVallas";
    protected String konfirmasiUrl;
    protected String inquiryUrl;
    protected String paymentUrl;
    protected String formUrl;

    public FormTransferVallasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getData() {
        if (formUrl == null || !isViewAttached()) {
            Log.d(TAG, "getData: form null");
            return;
        }

        if (!isViewAttached())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getDataForm(formUrl,seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserveFormKonversiValas(seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            //TO-DO onSuccess
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                RekValasListResponse response1 = response.getData(RekValasListResponse.class);
                                getView().onSuccesGetData(response1);
                            }
                            else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                getView().onException55(response.getDesc());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onExcepion12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }

    @Override
    public void getInquiry(InquiryVallasRequest request) {
        if (isViewAttached()) {
            //initiate param with getter from view
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(inquiryUrl, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserveFormKonversiValas(seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                        InquiryTransferVallasResponse transferVallasResponse = response.getData(InquiryTransferVallasResponse.class);
                                        getView().onSuccesGetInquiry(transferVallasResponse);
                                    }
                                      else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                                        onExceptionWH onExceptionWH = response.getData(onExceptionWH.class);
                                        getView().onException60(onExceptionWH);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());

                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }

    }

    @Override
    public void setInquiryUrl(String inquiryUrl) {
        this.inquiryUrl = inquiryUrl;
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setKonfirmasiUrl(String konfirmasiUrl) {
        this.konfirmasiUrl = konfirmasiUrl;
    }

    @Override
    public void setPaymentUrl(String setPaymentUrl) {
        this.paymentUrl = setPaymentUrl;
    }

}