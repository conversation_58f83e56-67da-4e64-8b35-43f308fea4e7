package id.co.bri.brimons.presenters.travel;

import android.util.Log;

import id.co.bri.brimons.contract.IPresenter.travel.IKonfirmasiTravelPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.travel.IKonfirmasiTravelView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserverKonfirmasi;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.DbConfig;
import id.co.bri.brimons.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.PaymentRequest;
import id.co.bri.brimons.models.apimodel.response.ConfirmTravelResponse;
import id.co.bri.brimons.models.apimodel.response.ReceiptTravelResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.daomodel.Transaksi;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiTravelPresenter <V extends IMvpView & IKonfirmasiTravelView> extends MvpPresenter<V> implements IKonfirmasiTravelPresenter<V> {

    private static final String TAG = "GeneralConfirmationPres";

    protected String urlPayment;
    protected Object paymentRequest;
    protected boolean isCashback;
    protected boolean isGeneral;

    public KonfirmasiTravelPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataPayment(String pin, String note, ConfirmTravelResponse confirmTravelResponse, boolean fromFast) {
        if (urlPayment == null) {
            Log.d(TAG, "getDataPayment: url payment null");
            return;
        }

        if (!isViewAttached()) {
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();


            paymentRequest = new PaymentRequest(confirmTravelResponse.getReferenceNumber(), pin, confirmTravelResponse.getPfmCategory().toString(), note);

            Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserverKonfirmasi(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            ReceiptTravelResponse receiptTravelResponse = response.getData(ReceiptTravelResponse.class);
                            getView().onSuccessGetPayment(receiptTravelResponse);

                            if(receiptTravelResponse.getImmediatelyFlag())
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        confirmTravelResponse.getPfmCategory().intValue(),
                                        confirmTravelResponse.getPayAmount(),
                                        confirmTravelResponse.getReferenceNumber(),
                                        confirmTravelResponse.getPfmDescription())
                                );

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())){
                                getView().onException99(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }

    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void isGeneral(boolean general) {

    }

    @Override
    public void isCashback(boolean cashback) {
        isCashback = cashback;
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {

                        }

                        @Override
                        public void onError(Throwable e) {
                            Log.d(TAG, "onError: " + e.toString());
                        }
                    })
            );
        }
    }

    @Override
    public void start() {
        super.start();
        setDisablePopup(true);
    }

    @Override
    public void stop() {
        super.stop();
    }
}