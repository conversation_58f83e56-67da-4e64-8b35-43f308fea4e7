package id.co.bri.brimons.presenters.waiting;



import id.co.bri.brimons.contract.IPresenter.waiting.IWaitingSmsPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.waiting.IWaitingSmsView;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.Constant;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.bilingual.PrefrencesLanguageRequest;
import id.co.bri.brimons.models.apimodel.response.AktivasiResponse;
import id.co.bri.brimons.models.apimodel.response.ResendResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import id.co.bri.brimons.security.IMyEncrypt;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class WaitingActivityPresenter<V extends IMvpView & IWaitingSmsView> extends MvpPresenter<V> implements IWaitingSmsPresenter<V> {
    private static final String TAG = "WaitingActivityPresente";

    public WaitingActivityPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, IMyEncrypt myEncrypt) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    private String mUrlPrefrences = "";

    @Override
    public void setUrllPrefrences(String mUrl) {
        this.mUrlPrefrences = mUrl;
    }

    @Override
    public void onClick(String code, String refNum) {
        String seq = getBRImoPrefRepository().getSeqNumber();

        if (getView() != null) {

            getView().showProgress();

            Disposable disposable = getApiSource().confirmationOTP(code, refNum, seq)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seq) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            AktivasiResponse response2 = restResponse.getData(AktivasiResponse.class);

                            getBRImoPrefRepository().saveUsername(response2.getUsername());
                            getBRImoPrefRepository().deteleSavedMenu();
                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                getBRImoPrefRepository().saveUserType(Constant.IB_TYPE);
                                getView().onEditUsername(response.getDesc());
                            } else if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getBRImoPrefRepository().saveUserType(Constant.IB_TYPE);
                                getView().onSuccess();
                            } else if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                //save prefrences to be
                                updatePrefrencesLanguage();
                                //nyimpen flag user IB
                                getBRImoPrefRepository().saveUserType(Constant.IB_TYPE);
                                //update flag login
                                updateLoginFlag(true);
                                //goto view change device
                                getView().onSuccessGetChangeDevice();
                                //delete value key biometric dan sett status aktivasi false
                                getBRImoPrefRepository().deleteValueKeyBiometric();
                                getBRImoPrefRepository().saveStatusAktivasi(false);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();

                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void onConfirmOTP(String code, String refNum) {
        if (!code.equalsIgnoreCase("")) {
            this.onClick(code, refNum);
        }
    }

    @Override
    public void onResendOtp(String refNum) {
        if (getView() != null) {
            getView().showProgress();
            getView().setDisableClick(true);


            String seqNum = getBRImoPrefRepository().getSeqNumber();
            String tokenKey = getBRImoPrefRepository().getTokenKey();
            Disposable disposable = getApiSource().resendOTP(tokenKey, refNum, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().setDisableClick(false);
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            ResendResponse resendResponse = response.getData(ResendResponse.class);
                            getBRImoPrefRepository().saveTokenKey(resendResponse.getTokenKey());
                            getView().onResendOtp(resendResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();

                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    public void updatePrefrencesLanguage() {
        if (!getBRImoPrefRepository().getChangeDeviceFlag()) return;
        String id = getBRImoPrefRepository().getLanguange();
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        PrefrencesLanguageRequest request = new PrefrencesLanguageRequest(getFastMenuRequest(), id) ;
        getCompositeDisposable().add(
                getApiSource().getData(mUrlPrefrences, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                            }
                        })
        );
    }

}