package id.co.bri.brimons.presenters.esbn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimons.contract.IPresenter.esbn.IRiwayatPembelianSbnPresenter;
import id.co.bri.brimons.contract.IView.IMvpView;
import id.co.bri.brimons.contract.IView.esbn.IRiwayatPembelianSbnView;
import id.co.bri.brimons.data.api.ApiSource;
import id.co.bri.brimons.data.api.observer.ApiObserver;
import id.co.bri.brimons.data.preference.BRImoPrefSource;
import id.co.bri.brimons.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimons.data.repository.category.CategoryPfmSource;
import id.co.bri.brimons.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimons.domain.config.AppConfig;
import id.co.bri.brimons.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimons.models.apimodel.request.esbn.InquiryMpnSbnRequest;
import id.co.bri.brimons.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimons.models.apimodel.response.RestResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.ConfirmationSbnLimitResponse;
import id.co.bri.brimons.models.apimodel.response.esbn.PembelianSbnResponse;
import id.co.bri.brimons.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class RiwayatPembelianSbnPresenter<V extends IMvpView & IRiwayatPembelianSbnView> extends MvpPresenter<V> implements IRiwayatPembelianSbnPresenter<V> {
    private String url_success;
    private String url_inquiry;

    public RiwayatPembelianSbnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlGetRiwayatDone(String url) {
        this.url_success = url;
    }
    @Override
    public void setUrlGetInquiry(String url) {
        this.url_inquiry = url;
    }

    @Override
    public void getDataRiwayatDone() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_success, "", seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                PembelianSbnResponse dataResponse = response.getData(PembelianSbnResponse.class);
                                getView().onSuccesGetRiwayatDone(dataResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getInquiry(InquiryMpnSbnRequest model) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_inquiry,model , seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    GeneralInquiryResponse dataResponse = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccesSubmitInquiry(dataResponse);
                                }else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                    ConfirmationSbnLimitResponse dataResponse = response.getData(ConfirmationSbnLimitResponse.class);
                                    getView().onSuccesSubmitLimit(dataResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void stop() {
        super.stop();
    }

    @Override
    public void start() {
        super.start();
    }
}