<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_new_skin_activity_container">
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!-- Toolbar -->
                <id.co.bri.brimons.ui.widget.BaseScreenLayout
                    android:id="@+id/bsl_content"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/ll_bottom">

                    <id.co.bri.brimons.ui.widget.ParallaxHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:id="@+id/ll_content_main"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="21dp"
                            android:orientation="vertical">
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">
                                <id.co.bri.brimons.ui.widget.input_til.BaseInputView
                                    android:id="@+id/biv_no_pelanggan"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:prefixText="+62"
                                    app:expanded="false"
                                    app:hintText="Nomor HP" />
                            </RelativeLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/size_1dp"
                                android:layout_marginVertical="@dimen/size_21dp"
                                android:background="#E9EEF6" />
                        </LinearLayout>
                    </id.co.bri.brimons.ui.widget.ParallaxHeader>

                    <id.co.bri.brimons.ui.widget.StickyHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">
                            <!-- Search bar -->
                            <LinearLayout
                                android:id="@+id/searchview_briva"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingVertical="12dp"
                                android:paddingHorizontal="16dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:theme="@style/AppSearchViewSmall"
                                android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
                                android:layout_marginBottom="24dp">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:src="@drawable/ic_search_new_skin_20"
                                    android:layout_marginEnd="8dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    style="@style/Body.lg.reg"
                                    android:text="@string/cari_pelanggan_atau_layanan" />
                            </LinearLayout>

                            <!-- Tab Layout -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="24dp">
                                <TextView
                                    android:id="@+id/tab_favorit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Favorit"
                                    style="@style/Body.md.reg"
                                    android:textAlignment="center"
                                    android:textColor="@color/text_brand_primary_ns"
                                    android:background="@drawable/rounded_button_soft_ns"
                                    android:paddingVertical="9dp"
                                    android:paddingHorizontal="16dp"
                                    android:layout_marginEnd="8dp"
                                    android:clickable="true"
                                    android:focusable="true" />

                                <TextView
                                    android:id="@+id/tab_riwayat"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Riwayat"
                                    style="@style/Body.md.reg"
                                    android:textAlignment="center"
                                    android:textColor="@color/black"
                                    android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
                                    android:paddingVertical="9dp"
                                    android:paddingHorizontal="16dp"
                                    android:clickable="true"
                                    android:focusable="true" />
                            </LinearLayout>
                        </LinearLayout>
                    </id.co.bri.brimons.ui.widget.StickyHeader>

                    <!-- Tab Section for Favorit and Riwayat -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!--                &lt;!&ndash; Search bar &ndash;&gt;-->
                        <!--                <LinearLayout-->
                        <!--                    android:id="@+id/searchview_briva"-->
                        <!--                    android:layout_width="match_parent"-->
                        <!--                    android:layout_height="wrap_content"-->
                        <!--                    android:paddingVertical="12dp"-->
                        <!--                    android:paddingHorizontal="16dp"-->
                        <!--                    android:gravity="center_vertical"-->
                        <!--                    android:orientation="horizontal"-->
                        <!--                    android:theme="@style/AppSearchViewSmall"-->
                        <!--                    android:background="@drawable/bg_input_black_100_brimo_ns_rounded"-->
                        <!--                    android:layout_marginBottom="24dp">-->

                        <!--                    <ImageView-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="match_parent"-->
                        <!--                        android:src="@drawable/ic_search_new_skin_20"-->
                        <!--                        android:layout_marginEnd="8dp" />-->
                        <!--                    -->
                        <!--                    <TextView-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="match_parent"-->
                        <!--                        style="@style/Body.lg.reg"-->
                        <!--                        android:text="@string/cari_pelanggan_atau_layanan" />-->
                        <!--                </LinearLayout>-->

                        <!--                &lt;!&ndash; Tab Layout &ndash;&gt;-->
                        <!--                <LinearLayout-->
                        <!--                    android:layout_width="wrap_content"-->
                        <!--                    android:layout_height="wrap_content"-->
                        <!--                    android:orientation="horizontal"-->
                        <!--                    android:layout_marginBottom="24dp">-->
                        <!--                    <TextView-->
                        <!--                        android:id="@+id/tab_favorit"-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:layout_weight="1"-->
                        <!--                        android:text="Favorit"-->
                        <!--                        style="@style/Body.md.reg"-->
                        <!--                        android:textAlignment="center"-->
                        <!--                        android:textColor="@color/text_brand_primary_ns"-->
                        <!--                        android:background="@drawable/rounded_button_soft_ns"-->
                        <!--                        android:paddingVertical="9dp"-->
                        <!--                        android:paddingHorizontal="16dp"-->
                        <!--                        android:layout_marginEnd="8dp"-->
                        <!--                        android:clickable="true"-->
                        <!--                        android:focusable="true" />-->

                        <!--                    <TextView-->
                        <!--                        android:id="@+id/tab_riwayat"-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:layout_weight="1"-->
                        <!--                        android:text="Riwayat"-->
                        <!--                        style="@style/Body.md.reg"-->
                        <!--                        android:textAlignment="center"-->
                        <!--                        android:textColor="@color/black"-->
                        <!--                        android:background="@drawable/bg_input_black_100_brimo_ns_rounded"-->
                        <!--                        android:paddingVertical="9dp"-->
                        <!--                        android:paddingHorizontal="16dp"-->
                        <!--                        android:clickable="true"-->
                        <!--                        android:focusable="true" />-->
                        <!--                </LinearLayout>-->

                        <!-- Favorit Content (Saved List) -->
                        <LinearLayout
                            android:id="@+id/content_favorit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <!-- Saved List RecyclerView -->
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_daftar_favorit"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:nestedScrollingEnabled="false"
                                android:layoutAnimation="@anim/layout_animation_fade_in"
                                android:visibility="gone" />

                            <!-- No Saved Data Message -->
                            <LinearLayout
                                android:id="@+id/ll_no_data_saved"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="32dp"
                                android:visibility="visible">

                                <ImageView
                                    android:layout_width="120dp"
                                    android:layout_height="120dp"
                                    android:src="@drawable/empty_box_3d"
                                    android:layout_marginBottom="16dp" />
                                <TextView
                                    android:id="@+id/no_data_saved"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Belum Ada Daftar Favorit"
                                    style="@style/Body.lg.smbold"
                                    android:textColor="@color/black"
                                    android:layout_marginTop="8dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Yuk, tambah favorit biar transaksi berikutnya lebih cepat."
                                    style="@style/Body.md.reg"
                                    android:gravity="center"
                                    android:layout_marginTop="4dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <!-- Riwayat Content (History) -->
                        <LinearLayout
                            android:id="@+id/content_riwayat"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <!-- History RecyclerView -->
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_riwayat"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layoutAnimation="@anim/layout_animation_fade_in"
                                android:nestedScrollingEnabled="false"
                                android:visibility="visible" />

                            <!-- No History Message -->
                            <LinearLayout
                                android:id="@+id/ll_no_history"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:padding="32dp"
                                android:visibility="gone">

                                <ImageView
                                    android:layout_width="120dp"
                                    android:layout_height="120dp"
                                    android:layout_marginBottom="16dp"
                                    android:src="@drawable/empty_box_3d" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="8dp"
                                    android:text="Belum Ada Daftar Riwayat"
                                    style="@style/Body.lg.smbold"
                                    android:textColor="@color/black_ns_main"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:gravity="center"
                                    android:text="Semua transaksi yang telah dibuat akan tampil di sini."
                                    style="@style/Body.md.reg"
                                    android:textColor="@color/black_ns_700" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </id.co.bri.brimons.ui.widget.BaseScreenLayout>

                <LinearLayout
                    android:id="@+id/ll_bottom"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@android:color/white"
                    android:orientation="vertical">
                    <View
                        android:id="@+id/bottom_border"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/size_1dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
                        android:background="@color/border_gray_soft_ns" />

                    <Button
                        android:id="@+id/btnSubmit"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        style="@style/CustomButtonStyle"
                        android:text="Lanjutkan"
                        android:layout_margin="16dp"
                        android:textSize="16sp"
                        android:textAllCaps="false"
                        android:background="@drawable/rounded_button_ns"
                        android:textColor="@color/selector_text_color_button_primary_ns"
                        android:enabled="false" />
                </LinearLayout>

                <!--        <View-->
                <!--            android:id="@+id/bottom_border"-->
                <!--            android:layout_width="wrap_content"-->
                <!--            android:layout_height="@dimen/size_1dp"-->
                <!--            app:layout_constraintLeft_toLeftOf="parent"-->
                <!--            app:layout_constraintRight_toRightOf="parent"-->
                <!--            app:layout_constraintBottom_toTopOf="@+id/ll_bottom"-->
                <!--            android:background="@color/border_gray_soft_ns" />-->

                <!--        &lt;!&ndash; Bottom Button &ndash;&gt;-->
                <!--        <LinearLayout-->
                <!--            android:id="@+id/ll_bottom"-->
                <!--            android:layout_width="0dp"-->
                <!--            android:layout_height="wrap_content"-->
                <!--            android:layout_gravity="bottom"-->
                <!--            app:layout_constraintLeft_toLeftOf="parent"-->
                <!--            app:layout_constraintRight_toRightOf="parent"-->
                <!--            app:layout_constraintBottom_toBottomOf="parent"-->
                <!--            android:orientation="vertical"-->
                <!--            android:padding="16dp"-->
                <!--            android:background="@android:color/white">-->

                <!--            <Button-->
                <!--                android:id="@+id/btnSubmit"-->
                <!--                android:layout_width="match_parent"-->
                <!--                android:layout_height="56dp"-->
                <!--                style="@style/CustomButtonStyle"-->
                <!--                android:text="Lanjutkan"-->
                <!--                android:textSize="16sp"-->
                <!--                android:textAllCaps="false"-->
                <!--                android:background="@drawable/rounded_button_ns"-->
                <!--                android:textColor="@color/selector_text_color_button_primary_ns"-->
                <!--                android:enabled="false" />-->
                <!--        </LinearLayout>-->
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </FrameLayout>
</layout>