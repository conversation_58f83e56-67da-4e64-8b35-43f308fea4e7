<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_receipt_basic_ns">

        <id.co.bri.brimons.ui.widget.BaseScreenLayout
            android:id="@+id/bsl_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/ll_bottom">

            <!-- Scrollable content -->
            <ScrollView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/bg_card_rounded_ns"
                android:clipToPadding="false">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Wilayah input -->
                    <LinearLayout
                        android:id="@+id/ll_listrik_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical|center_horizontal"
                        android:layout_gravity="center_horizontal"
                        android:minHeight="64dp"
                        android:paddingHorizontal="21dp"
                        android:background="@drawable/bg_input_black_100_brimo_ns">

                        <RelativeLayout
                            android:id="@+id/icon_container"
                            android:layout_width="@dimen/size_32dp"
                            android:layout_height="@dimen/size_32dp"
                            android:layout_marginStart="0dp"
                            android:layout_marginEnd="16dp"
                            android:layout_gravity="center_vertical"
                            android:background="@drawable/round_icon_ns"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_area"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_alignParentTop="true"
                                android:src="@drawable/ikon_wilayah" />
                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                tools:ignore="RtlSymmetry">

                                <TextView
                                    android:id="@+id/region_textview"
                                    style="@style/BodySmallText.Medium.Grey"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_black_default_ns"
                                    android:layout_marginBottom="4dp"
                                    android:textSize="@dimen/size_12sp"
                                    android:text="Jenis Listrik"
                                    android:visibility="gone" />


                                <com.google.android.material.textfield.TextInputLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:hintEnabled="false"
                                    android:gravity="center_vertical"
                                    app:boxStrokeWidth="0dp">

                                    <EditText
                                        android:id="@+id/et_jenis_listrik"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:hint="Jenis Listrik"
                                        android:focusable="false"
                                        android:textColorHint="@color/text_black_default_ns"
                                        android:layout_gravity="center_vertical"
                                        android:background="@android:color/transparent"
                                        android:paddingStart="0dp"
                                        android:paddingEnd="0dp"
                                        android:inputType="none|textNoSuggestions"
                                        android:paddingVertical="0dp"
                                        android:saveEnabled="false" />
                                </com.google.android.material.textfield.TextInputLayout>

                            </LinearLayout>

                            <ImageView
                                android:layout_gravity="center_vertical"
                                android:layout_width="@dimen/size_16dp"
                                android:layout_height="@dimen/size_16dp"
                                android:src="@drawable/ic_arrow_down_black" />

                        </LinearLayout>
                    </LinearLayout>

                    <!-- Nomor Pelanggan input -->
                    <id.co.bri.brimons.ui.widget.input_til.BaseInputView
                        android:id="@+id/biv_no_pelanggan"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21dp"
                        app:hintText="Nomor Meter / ID Pelanggan"
                        android:enabled="false" />

                    <!-- Nama Tersimpan input -->
                    <id.co.bri.brimons.ui.widget.input_til.BaseInputView
                        android:id="@+id/etSavedName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21dp"
                        app:hintText="Nama Tersimpan"
                        android:enabled="false" />
                </LinearLayout>
            </ScrollView>

        </id.co.bri.brimons.ui.widget.BaseScreenLayout>

        <!-- Bottom Button -->
        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:elevation="8dp"
            android:background="@android:color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <Button
                android:id="@+id/btnSubmit"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                style="@style/CustomButtonStyle"
                android:text="Simpan"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:background="@drawable/rounded_button_ns"
                android:textColor="@color/selector_text_color_button_primary_ns"
                android:enabled="false" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>