<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/bg_new_skin_activity_container"
        android:orientation="vertical">
        <include
            android:id="@+id/incl_toolbar"
            layout="@layout/toolbar_ns"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingVertical="16dp"
            android:background="@drawable/bg_card_rounded_ns">
            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@+id/cl_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appbarDashboard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:background="@color/transparent"
                    app:elevation="0dp"
                    app:liftOnScroll="false">

                    <com.google.android.material.appbar.CollapsingToolbarLayout
                        android:id="@+id/collapsingToolbar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
                        app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
                        app:scrimAnimationDuration="0"
                        app:titleEnabled="false">

                        <LinearLayout
                            android:id="@+id/content_appbar"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <!-- Parallax (atas) -->
                            <FrameLayout
                                android:id="@+id/parallax_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:layout_collapseMode="parallax" />

                            <!-- Sticky (bawah) -->
                            <FrameLayout
                                android:id="@+id/sticky_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:layout_collapseMode="pin" />

                        </LinearLayout>

                    </com.google.android.material.appbar.CollapsingToolbarLayout>

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fillViewport="true"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior">
                    <LinearLayout
                        android:id="@+id/ll_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:orientation="vertical" />
                </androidx.core.widget.NestedScrollView>

            </androidx.coordinatorlayout.widget.CoordinatorLayout>
        </FrameLayout>

    </LinearLayout>
</layout>